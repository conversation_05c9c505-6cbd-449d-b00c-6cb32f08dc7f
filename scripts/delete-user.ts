import { db } from '../src/lib/db'
import {
  user,
  projects,
  renderJobs,
  mediaAssets,
  apiKeys,
  youtubeConnections,
  usage,
  subscription,
  organization,
  member,
  invitation,
  session,
  account,
} from '../src/db/schema'
import { eq, and, count, sql } from 'drizzle-orm'

/**
 * <PERSON><PERSON><PERSON> to safely delete a user and ALL associated data
 * Usage: bun run scripts/delete-user.ts <email>
 * WARNING: This will permanently delete the user and all associated data including:
 * - Projects and render jobs
 * - Media assets and storage
 * - API keys and YouTube connections
 * - Organization memberships (and orphaned organizations)
 * - Usage data and subscriptions
 * - Sessions and accounts
 */

interface UserAnalysis {
  projectsCount: number
  renderJobsCount: number
  mediaAssetsCount: number
  apiKeysCount: number
  youtubeConnectionsCount: number
  usageCount: number
  sessionsCount: number
  accountsCount: number
  membershipsCount: number
  invitationsCount: number
  subscriptionsCount: number
  orphanedOrganizations: Array<{
    organizationId: string
    organizationName: string
  }>
  stripeCustomerId?: string | null
}

async function analyzeUserData(userId: string): Promise<UserAnalysis> {
  console.log('📊 Analyzing user data...')

  try {
    // Count all related data
    const [projectsCount] = await db
      .select({ count: count() })
      .from(projects)
      .where(eq(projects.userId, userId))
    const [renderJobsCount] = await db
      .select({ count: count() })
      .from(renderJobs)
      .where(eq(renderJobs.userId, userId))
    const [mediaAssetsCount] = await db
      .select({ count: count() })
      .from(mediaAssets)
      .where(eq(mediaAssets.userId, userId))
    const [apiKeysCount] = await db
      .select({ count: count() })
      .from(apiKeys)
      .where(eq(apiKeys.userId, userId))
    const [youtubeConnectionsCount] = await db
      .select({ count: count() })
      .from(youtubeConnections)
      .where(eq(youtubeConnections.userId, userId))
    const [usageCount] = await db
      .select({ count: count() })
      .from(usage)
      .where(sql`${userId} = ANY(${usage.members})`)
    const [sessionsCount] = await db
      .select({ count: count() })
      .from(session)
      .where(eq(session.userId, userId))
    const [accountsCount] = await db
      .select({ count: count() })
      .from(account)
      .where(eq(account.userId, userId))
    const [membershipsCount] = await db
      .select({ count: count() })
      .from(member)
      .where(eq(member.userId, userId))
    const [invitationsCount] = await db
      .select({ count: count() })
      .from(invitation)
      .where(eq(invitation.inviterId, userId))

    // Check for subscriptions (linked by stripeCustomerId)
    const [targetUser] = await db
      .select({ stripeCustomerId: user.stripeCustomerId })
      .from(user)
      .where(eq(user.id, userId))
    let subscriptionsCount = { count: 0 }
    if (targetUser?.stripeCustomerId) {
      ;[subscriptionsCount] = await db
        .select({ count: count() })
        .from(subscription)
        .where(eq(subscription.stripeCustomerId, targetUser.stripeCustomerId))
    }

    // Check organizations where user is the only member
    const userOrganizations = await db
      .select({
        organizationId: member.organizationId,
        organizationName: organization.name,
      })
      .from(member)
      .innerJoin(organization, eq(member.organizationId, organization.id))
      .where(eq(member.userId, userId))

    const orphanedOrganizations = []
    for (const org of userOrganizations) {
      const [memberCount] = await db
        .select({ count: count() })
        .from(member)
        .where(eq(member.organizationId, org.organizationId))

      if (memberCount.count === 1) {
        orphanedOrganizations.push(org)
      }
    }

    console.log('\n📈 Data Summary:')
    console.log(`   🗂️  Projects: ${projectsCount.count}`)
    console.log(`   🎬 Render Jobs: ${renderJobsCount.count}`)
    console.log(`   📁 Media Assets: ${mediaAssetsCount.count}`)
    console.log(`   🔑 API Keys: ${apiKeysCount.count}`)
    console.log(`   📺 YouTube Connections: ${youtubeConnectionsCount.count}`)
    console.log(`   📊 Usage Records: ${usageCount.count}`)
    console.log(`   🔐 Sessions: ${sessionsCount.count}`)
    console.log(`   👤 Accounts: ${accountsCount.count}`)
    console.log(`   🏢 Organization Memberships: ${membershipsCount.count}`)
    console.log(`   ✉️  Invitations Sent: ${invitationsCount.count}`)
    console.log(`   💳 Subscriptions: ${subscriptionsCount.count}`)

    if (orphanedOrganizations.length > 0) {
      console.log(
        `\n⚠️  Organizations that will be deleted (user is sole member):`
      )
      orphanedOrganizations.forEach(org => {
        console.log(`   🏢 ${org.organizationName} (${org.organizationId})`)
      })
    }

    return {
      projectsCount: projectsCount.count,
      renderJobsCount: renderJobsCount.count,
      mediaAssetsCount: mediaAssetsCount.count,
      apiKeysCount: apiKeysCount.count,
      youtubeConnectionsCount: youtubeConnectionsCount.count,
      usageCount: usageCount.count,
      sessionsCount: sessionsCount.count,
      accountsCount: accountsCount.count,
      membershipsCount: membershipsCount.count,
      invitationsCount: invitationsCount.count,
      subscriptionsCount: subscriptionsCount.count,
      orphanedOrganizations,
      stripeCustomerId: targetUser?.stripeCustomerId,
    }
  } catch (error) {
    console.error('❌ Error analyzing user data:', error)
    throw error
  }
}

async function deleteUser(email: string) {
  console.log(`🔍 Looking for user with email: ${email}`)

  try {
    // First, find the user
    const [existingUser] = await db
      .select()
      .from(user)
      .where(eq(user.email, email))

    if (!existingUser) {
      console.log('❌ User not found')
      return
    }

    console.log('👤 Found user:')
    console.log(`   📧 Email: ${existingUser.email}`)
    console.log(`   👤 Name: ${existingUser.name}`)
    console.log(`   🆔 ID: ${existingUser.id}`)
    console.log(`   📅 Created: ${existingUser.createdAt?.toISOString()}`)

    // Analyze all related data
    await analyzeUserData(existingUser.id)

    // Confirm deletion
    console.log('')
    console.log(
      '⚠️  WARNING: This will permanently delete the user and ALL associated data!'
    )
    console.log('🚫 This action cannot be undone!')
    console.log('')
    console.log('To proceed, run this command with --confirm flag:')
    console.log(`bun run scripts/delete-user.ts ${email} --confirm`)
  } catch (error) {
    console.error('❌ Database error:', error)
  }
}

async function confirmDeleteUser(email: string) {
  console.log(`🗑️  Deleting user with email: ${email}`)

  try {
    // First, find the user to get their ID
    const [existingUser] = await db
      .select()
      .from(user)
      .where(eq(user.email, email))

    if (!existingUser) {
      console.log('❌ User not found')
      return
    }

    const userId = existingUser.id
    const stripeCustomerId = existingUser.stripeCustomerId

    console.log('🚀 Starting comprehensive user deletion...')

    // Step 1: Delete user-owned data (no foreign key constraints)
    console.log('1️⃣ Deleting user-owned data...')

    // Delete API keys
    const deletedApiKeys = await db
      .delete(apiKeys)
      .where(eq(apiKeys.userId, userId))
      .returning({ id: apiKeys.id })
    if (deletedApiKeys.length > 0) {
      console.log(`   ✅ Deleted ${deletedApiKeys.length} API keys`)
    }

    // Delete YouTube connections
    const deletedYoutubeConnections = await db
      .delete(youtubeConnections)
      .where(eq(youtubeConnections.userId, userId))
      .returning({ id: youtubeConnections.id })
    if (deletedYoutubeConnections.length > 0) {
      console.log(
        `   ✅ Deleted ${deletedYoutubeConnections.length} YouTube connections`
      )
    }

    // Update usage records (remove user from members array)
    const userUsageRecords = await db
      .select()
      .from(usage)
      .where(sql`${userId} = ANY(${usage.members})`)

    let updatedUsageCount = 0
    for (const usageRecord of userUsageRecords) {
      // Remove user from members array
      await db
        .update(usage)
        .set({
          members: sql`array_remove(${usage.members}, ${userId})`,
          updatedAt: new Date(),
        })
        .where(eq(usage.id, usageRecord.id))
      updatedUsageCount++
    }
    if (updatedUsageCount > 0) {
      console.log(`   ✅ Removed user from ${updatedUsageCount} usage records`)
    }

    // Delete media assets
    const deletedMediaAssets = await db
      .delete(mediaAssets)
      .where(eq(mediaAssets.userId, userId))
      .returning({ id: mediaAssets.id })
    if (deletedMediaAssets.length > 0) {
      console.log(`   ✅ Deleted ${deletedMediaAssets.length} media assets`)
    }

    // Delete render jobs
    const deletedRenderJobs = await db
      .delete(renderJobs)
      .where(eq(renderJobs.userId, userId))
      .returning({ id: renderJobs.id })
    if (deletedRenderJobs.length > 0) {
      console.log(`   ✅ Deleted ${deletedRenderJobs.length} render jobs`)
    }

    // Delete projects
    const deletedProjects = await db
      .delete(projects)
      .where(eq(projects.userId, userId))
      .returning({ projectId: projects.projectId })
    if (deletedProjects.length > 0) {
      console.log(`   ✅ Deleted ${deletedProjects.length} projects`)
    }

    // Step 2: Delete subscription data (if exists)
    if (stripeCustomerId) {
      console.log('2️⃣ Deleting subscription data...')
      const deletedSubscriptions = await db
        .delete(subscription)
        .where(eq(subscription.stripeCustomerId, stripeCustomerId))
        .returning({ id: subscription.id })
      if (deletedSubscriptions.length > 0) {
        console.log(
          `   ✅ Deleted ${deletedSubscriptions.length} subscriptions`
        )
      }
    }

    // Step 3: Handle orphaned organizations (where user is the only member)
    console.log('3️⃣ Checking for orphaned organizations...')
    const userOrganizations = await db
      .select({
        organizationId: member.organizationId,
        organizationName: organization.name,
      })
      .from(member)
      .innerJoin(organization, eq(member.organizationId, organization.id))
      .where(eq(member.userId, userId))

    for (const org of userOrganizations) {
      const [memberCount] = await db
        .select({ count: count() })
        .from(member)
        .where(eq(member.organizationId, org.organizationId))

      if (memberCount.count === 1) {
        // User is the only member, delete the organization (this will cascade delete members and invitations)
        const deletedOrgs = await db
          .delete(organization)
          .where(eq(organization.id, org.organizationId))
          .returning({ id: organization.id })
        if (deletedOrgs.length > 0) {
          console.log(
            `   ✅ Deleted orphaned organization: ${org.organizationName}`
          )
        }
      } else {
        // Just remove the user's membership
        const deletedMemberships = await db
          .delete(member)
          .where(
            and(
              eq(member.userId, userId),
              eq(member.organizationId, org.organizationId)
            )
          )
          .returning({ id: member.id })
        if (deletedMemberships.length > 0) {
          console.log(
            `   ✅ Removed membership from organization: ${org.organizationName}`
          )
        }
      }
    }

    // Step 4: Delete authentication data (these have cascade delete, but being explicit)
    console.log('4️⃣ Deleting authentication data...')

    // Delete sessions (has cascade delete)
    const deletedSessions = await db
      .delete(session)
      .where(eq(session.userId, userId))
      .returning({ id: session.id })
    if (deletedSessions.length > 0) {
      console.log(`   ✅ Deleted ${deletedSessions.length} sessions`)
    }

    // Delete accounts (has cascade delete)
    const deletedAccounts = await db
      .delete(account)
      .where(eq(account.userId, userId))
      .returning({ id: account.id })
    if (deletedAccounts.length > 0) {
      console.log(`   ✅ Deleted ${deletedAccounts.length} accounts`)
    }

    // Delete invitations sent by user (has cascade delete)
    const deletedInvitations = await db
      .delete(invitation)
      .where(eq(invitation.inviterId, userId))
      .returning({ id: invitation.id })
    if (deletedInvitations.length > 0) {
      console.log(`   ✅ Deleted ${deletedInvitations.length} invitations`)
    }

    // Step 5: Finally, delete the user
    console.log('5️⃣ Deleting user...')
    const deletedUsers = await db
      .delete(user)
      .where(eq(user.email, email))
      .returning()

    if (deletedUsers.length > 0) {
      console.log('✅ User deleted successfully!')
      console.log(
        `👤 Deleted user: ${deletedUsers[0].name} (${deletedUsers[0].email})`
      )
      console.log('')
      console.log(
        '🎉 All user data has been completely removed from the system.'
      )
    } else {
      console.log('❌ User not found or already deleted')
    }
  } catch (error) {
    console.error('❌ Failed to delete user:', error)
    console.log('')
    console.log('💡 The deletion was stopped to prevent data corruption.')
    console.log('   Please check the error and try again.')
  }
}

// Get arguments
const targetEmail = process.argv[2]
const confirmFlag = process.argv[3]

if (!targetEmail) {
  console.log('Usage: bun run scripts/delete-user.ts <email> [--confirm]')
  console.log(
    'Example: bun run scripts/delete-user.ts <EMAIL>'
  )
  console.log('')
  console.log(
    'This script will delete ALL data associated with the user including:'
  )
  console.log('• Projects and render jobs')
  console.log('• Media assets and storage')
  console.log('• API keys and YouTube connections')
  console.log('• Organization memberships (and orphaned organizations)')
  console.log('• Usage data and subscriptions')
  console.log('• Sessions and accounts')
  process.exit(1)
}

// Validate email format
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
if (!emailRegex.test(targetEmail)) {
  console.log('❌ Invalid email format')
  process.exit(1)
}

if (confirmFlag === '--confirm') {
  confirmDeleteUser(targetEmail)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('💥 Script failed:', error)
      process.exit(1)
    })
} else {
  deleteUser(targetEmail)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('💥 Script failed:', error)
      process.exit(1)
    })
}
