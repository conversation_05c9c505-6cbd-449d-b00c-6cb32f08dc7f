export function register() {
  // No-op for initialization
}

export const onRequestError = async (
  err: unknown,
  request:
    | Request
    | { headers?: { get?: (name: string) => string | null; cookie?: string } }
) => {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    const { getPostHogServer } = await import('./src/lib/posthog')
    const posthog = await getPostHogServer()
    let distinctId = null

    // Handle both NextRequest and standard Request objects
    let cookieString: string | null = null

    if (request && typeof request === 'object') {
      // Check if it's a NextRequest (has headers property)
      if ('headers' in request && request.headers) {
        // For NextRequest, headers might be accessed differently
        if (typeof request.headers.get === 'function') {
          cookieString = request.headers.get('cookie')
        } else if ('cookie' in request.headers && request.headers.cookie) {
          cookieString = request.headers.cookie
        }
      }
    }

    if (cookieString) {
      const postHogCookieMatch = cookieString.match(
        /ph_phc_.*?_posthog=([^;]+)/
      )
      if (postHogCookieMatch && postHogCookieMatch[1]) {
        try {
          const decodedCookie = decodeURIComponent(postHogCookieMatch[1])
          const postHogData = JSON.parse(decodedCookie)
          distinctId = postHogData.distinct_id
        } catch (e) {
          console.error('Error parsing PostHog cookie:', e)
        }
      }
    }
    await posthog.captureException(err, distinctId || undefined)
  }
}
