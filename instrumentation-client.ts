import posthog from 'posthog-js'

// Only initialize PostHog in production environments
const isProduction = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return false

  const host = window.location.host
  const isLocalhost =
    host.includes('localhost') ||
    host.includes('127.0.0.1') ||
    host.includes('0.0.0.0') ||
    host.includes('::1') ||
    host.startsWith('192.168.') ||
    host.startsWith('10.') ||
    host.startsWith('172.')

  // Also check for common development domains
  const isDevDomain =
    host.includes('.staging') ||
    host.includes('.dev') ||
    (host.includes('vercel.app') && !host.includes('adoriai.com'))

  return !isLocalhost && !isDevDomain
}

if (isProduction()) {
  posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
    api_host: '/ingest',
    ui_host: 'https://us.posthog.com',
    defaults: '2025-05-24',
    capture_exceptions: true, // This enables capturing exceptions using Error Tracking, set to false if you don't want this
    debug: false, // Disable debug in production
  })
}
