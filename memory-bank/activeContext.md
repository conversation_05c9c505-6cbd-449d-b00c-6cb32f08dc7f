# Active Context

## Current work focus

The project has progressed significantly beyond initial planning. We are currently in the **usage tracking and billing system implementation phase**. Recent focus areas include:

1. **Usage Tracking System**: Complete implementation of usage tracking for projects, video exports, and AI images
2. **Usage API**: RESTful API for fetching and managing user usage data with plan limits
3. **Usage UI**: Modern card-based usage dashboard with real-time data and plan information
4. **Usage Integration**: Automatic usage tracking in video creation, rendering, and AI image generation workflows

## Recent changes

**Billing Permission Fix**:

- **Simple Role Check**: Fixed issue where organization members could attempt to upgrade subscriptions, causing Stripe errors
- **Clean Implementation**: Created `useUserRole()` hook to get user's role from session/API and simplified `canUpgradeSubscription()` function
- **User-Friendly Error Messages**: Members now see clear error message: "As you are a member of this organization, you don't have permission to change the plan. Only owners can change the plan."
- **Frontend Validation**: Added permission checks in pricing table and upgrade flows to prevent unauthorized upgrade attempts
- **Consistent UX**: All upgrade paths (pricing page, upgrade modal, upgrade buttons) now respect organization role permissions

**Organization Auto-Creation Implementation**:

- **Database Hook Integration**: Added automatic organization creation in the user creation database hook
- **Organization Naming**: Uses existing email utilities for consistent organization naming based on user's email domain
- **Active Organization Setting**: Automatically sets the newly created organization as the user's active organization
- **Error Handling**: Graceful error handling that doesn't break user creation if organization creation fails
- **Logging**: Comprehensive logging for debugging and monitoring organization creation process

**Usage Tracking System Implementation**:

- **Usage API (`/api/usage`)**: Complete RESTful API for fetching user usage data with plan limits and automatic usage record creation
- **Usage Database Schema**: Implemented usage table with tracking for projects, video exports, AI images, team members, and storage
- **Usage Utilities (`/lib/usage-utils.ts`)**: Simple and reliable functions for incrementing, checking, and managing usage limits
- **Usage Hook (`/hooks/use-usage.ts`)**: React Query hook for fetching usage data with proper caching and error handling

**Usage UI Implementation**:

- **Usage Page Redesign**: Complete overhaul from table format to modern card-based layout with:
  - Individual usage cards for each resource type (Projects, Video Exports, AI Images, Team Members, Storage)
  - Real-time progress bars with color-coded status (green/orange/red based on usage percentage)
  - Plan information display with current plan type and status
  - Plan period information with time remaining
  - Upgrade CTA for free plan users
- **Loading States**: Skeleton loading states for better UX
- **Error Handling**: Proper error states with retry functionality

**Usage Integration**:

- **Project Creation**: Automatic usage increment when projects are created in Inngest function
- **Video Rendering**: Usage tracking when videos are successfully rendered and exported
- **AI Image Generation**: Usage checking before generation and incrementing after successful creation
- **Usage Limits**: Proper enforcement of plan limits with 429 status codes when limits are exceeded

**Technical Improvements**:

- **Plan Limits**: Defined limits for FREE (3 projects, 0 exports, 20 AI images), BASIC (10/10/100), and PREMIUM (20/20/200) plans
- **Organization Support**: Usage tracking works with both individual users and organization contexts
- **Error Resilience**: Usage tracking failures don't break core functionality
- **Type Safety**: Full TypeScript support with proper interfaces and type checking

## Next steps

1. **Usage System Enhancements**:
   - Usage analytics and reporting
   - Usage history and trends
   - Usage notifications and alerts
   - Usage reset functionality for plan renewals

2. **Billing Integration**:
   - Stripe integration for plan upgrades
   - Automatic plan changes based on usage
   - Billing webhooks for usage management
   - Usage-based billing for overages

3. **User Experience Improvements**:
   - Usage warnings when approaching limits
   - In-app upgrade prompts
   - Usage comparison between plans
   - Usage export functionality

4. **Advanced Features**:
   - Team usage tracking and management
   - Usage quotas and restrictions
   - Usage-based feature gating
   - Usage optimization recommendations

## Active decisions and considerations

1. **UI/UX Decisions**:
   - Component-based architecture working well
   - Card-based layouts preferred over table formats
   - Hover effects and micro-interactions improving user experience
   - Mobile-first responsive design approach successful

2. **Technical Considerations**:
   - Component organization in feature-based folders
   - shadcn/ui components providing consistent design system
   - Theme system integrated across all components
   - TypeScript ensuring type safety

3. **User Workflow Preferences**:
   - **Git**: User prefers to run git commands manually, I provide commit messages only
   - **Development**: User runs `npm run dev` locally, I should never start dev servers
   - Focus on code implementation without attempting to run app directly

4. **Priority Areas**:
   - Video creation functionality implementation
   - Performance optimization for video processing
   - Enhanced user feedback and progress indicators
   - Mobile experience optimization
