# System Patterns

## System architecture

### Frontend Architecture

1. **Next.js App Router**

   - Server and client components with proper "use client" directives
   - API routes (ready for video processing endpoints)
   - Route groups:
     - (auth): Authentication related routes (sign-in, sign-up)
     - (dashboard): Main application dashboard with nested routes
   - File-based routing with proper layouts
   - Page title mapping system for better UX

2. **Component Structure**

   - **Feature-based organization**: Components organized by feature (pricing, dashboard, etc.)
   - **Atomic design principles**: Small, focused, reusable components
   - **Shared components library**: Well-organized UI components in `/components/ui/`
   - **Layout components**: Proper separation of layout and content
   - **Index exports**: Clean component exports with organized index files

3. **State Management**
   - React hooks for local state
   - Zustand for global state management
   - Theme state managed via next-themes
   - User authentication state via Better Auth

### Data Flow & Component Architecture

1. **Dashboard Cards System**

   - `ConversionCard` component with hover effects and transitions
   - Icon integration with gradient backgrounds
   - Responsive grid layout with proper spacing
   - Link integration for navigation

2. **Pricing System Architecture**

   ```
   PricingPage
   ├── SubscriptionCard (current plan display)
   ├── BillingToggle (monthly/annual switching)
   ├── PricingTable (comparison table for desktop)
   └── PlanCard[] (individual plan cards for mobile)
   ```

3. **Layout Architecture**
   ```
   DashboardLayout
   ├── AppSidebar (navigation)
   ├── SidebarInset
   │   ├── Header (with page title mapping)
   │   └── Main Content
   └── ModalProvider (for modals)
   ```

## Key technical decisions

1. **Component Organization Strategy**

   - Feature-based folders: `/components/pricing/`, `/app/(dashboard)/_components/`
   - Shared UI components in `/components/ui/` (shadcn/ui)
   - Clean index exports for better imports
   - TypeScript interfaces in separate type files

2. **Styling and Theme Approach**

   - Tailwind CSS with custom CSS variables using oklch color space
   - Dark/light theme support with next-themes
   - Component-specific hover effects and transitions
   - Responsive design with mobile-first approach
   - shadcn/ui for consistent, accessible UI components

3. **User Experience Patterns**

   - Card-based layouts preferred over table formats
   - Hover effects and micro-interactions for better engagement
   - Proper loading states and transitions
   - Mobile-responsive design patterns

4. **Development Workflow**
   - **Git**: User manages git commands, AI provides commit messages
   - **Development Server**: User runs local dev server, AI focuses on code implementation
   - Component-first development approach

## Design patterns

1. **Component Composition**

   - Small, focused components with single responsibilities
   - Composition over inheritance
   - Props-based configuration
   - Proper TypeScript interfaces for all components

2. **Card Component Pattern**

   ```tsx
   <Card className="hover:shadow-md transition-all">
     <CardHeader>
       <Icon + Title + Description>
     </CardHeader>
     <CardContent>
       <Content Area>
     </CardContent>
     <CardFooter>
       <Action Button>
     </CardFooter>
   </Card>
   ```

3. **Theme Integration Pattern**

   - All components support dark/light themes
   - Consistent color variables across components
   - Proper contrast ratios and accessibility

4. **Responsive Design Pattern**
   - Mobile-first CSS classes
   - Responsive grid layouts
   - Adaptive component behavior
   - Touch-friendly interfaces

## Code organization

1. **Directory Structure**

   ```
   src/
   ├── app/
   │   ├── (auth)/          # Authentication routes
   │   ├── (dashboard)/     # Main app routes
   │   │   ├── _components/ # Dashboard-specific components
   │   │   ├── pricing/     # Pricing page and components
   │   │   └── [other]/     # Other feature pages
   │   └── layout.tsx       # Root layout
   ├── components/
   │   ├── ui/              # shadcn/ui components
   │   ├── pricing/         # Pricing feature components
   │   └── [shared]/        # Other shared components
   └── lib/                 # Utility functions
   ```

2. **Component Export Pattern**

   ```tsx
   // index.ts files for clean imports
   export { SubscriptionCard } from './subscription-card'
   export { BillingToggle } from './billing-toggle'
   export type { Plan } from './types'
   ```

3. **Naming Conventions**
   - PascalCase for components and interfaces
   - camelCase for functions and variables
   - kebab-case for file names
   - Descriptive, feature-based naming

## Component relationships

1. **Layout Hierarchy**

   ```
   RootLayout (theme provider)
   └── DashboardLayout (sidebar + header)
       └── Page Components
           └── Feature Components
   ```

2. **Dashboard Component Flow**

   ```
   DashboardPage
   ├── SectionHeader (title + description)
   └── ConversionGrid
       └── ConversionCard[] (individual conversion options)
   ```

3. **Pricing Component Relationships**

   ```
   PricingPage
   ├── SubscriptionCard (standalone)
   ├── BillingToggle (state management)
   ├── PricingTable (desktop layout)
   │   └── PlanCard[] (embedded in table)
   └── PlanCard[] (mobile layout)
   ```

4. **Theme and Provider Integration**
   - ThemeProvider wraps entire application
   - Better Auth handles authentication state
   - ModalProvider manages modal states
   - Proper provider composition in root layout

## Implemented Patterns

1. **Hover Effect System**

   - Consistent hover transitions (300ms duration)
   - Scale transforms for interactive elements
   - Color transitions for better feedback
   - Shadow elevation changes

2. **Responsive Component Pattern**

   - Mobile-first design approach
   - Breakpoint-aware layouts
   - Touch-friendly button sizes
   - Adaptive grid systems

3. **TypeScript Integration**
   - Strong typing for all component props
   - Interface definitions for complex objects
   - Type-safe routing and navigation
   - Proper error handling
