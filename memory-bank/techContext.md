# Tech Context

## Technologies used

### Frontend Core

- Next.js - Modern React framework for production
- React - UI library
- TypeScript - Type-safe JavaScript

### UI/Design

- shadcn/ui - Modern UI component library
- Tailwind CSS - Utility-first CSS framework
- Responsive design principles

### Video Processing

- Remotion - React framework for video creation

### Form Handling & Validation

- Zod - TypeScript-first schema validation

### Authentication

- Better Auth - Modern authentication solution with organization support

### Development Tools

- ESLint - Code linting
- Prettier - Code formatting
- TypeScript - Static type checking
- Bun - Local development runtime and package manager
- npm - Cloud hosting and Docker package manager

## Development setup

### Prerequisites

- Bun (Latest version) - For local development
- Node.js (Latest LTS version) - For cloud hosting
- npm - For cloud hosting and Dockerfile
- Git

### Package Manager Strategy

- Use Bun for local development (faster performance)
- Use npm for cloud hosting and Dockerfile (better compatibility)
- Keep package-lock.json for npm compatibility
- Use bun.lockb for local development

### Project Structure

- Component-based architecture
- Mobile-first approach
- Type-safe development
- Modular design patterns

### Best Practices

- TypeScript for all components
- Responsive design implementation
- Accessibility standards compliance
- Performance optimization
- Component reusability

## Technical constraints

### Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers
- Progressive enhancement

### Performance Requirements

- Fast initial page load
- Optimized video processing
- Responsive UI interactions
- Mobile performance optimization

### Security Considerations

- Authentication implementation
- Data validation
- API security
- Content security

## Dependencies

### Core Dependencies

- next
- react
- react-dom
- typescript
- tailwindcss
- @remotion/cli
- @clerk/nextjs
- zod
- shadcn/ui components

### Development Dependencies

- @types/react
- @types/node
- eslint
- prettier
- typescript
- postcss
- autoprefixer

Note: Specific version numbers will be determined during initial setup based on compatibility requirements and latest stable versions.
