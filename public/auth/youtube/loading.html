<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Connecting to YouTube...</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;
        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;
        --primary: 240 5.9% 10%;
        --border: 240 5.9% 90%;
        --destructive: 0 84.2% 60.2%;
        --green: 142 76% 36%;
      }

      @media (prefers-color-scheme: dark) {
        :root {
          --background: 240 10% 3.9%;
          --foreground: 0 0% 98%;
          --muted: 240 3.7% 15.9%;
          --muted-foreground: 240 5% 64.9%;
          --primary: 0 0% 98%;
          --border: 240 3.7% 15.9%;
          --destructive: 0 62.8% 30.6%;
          --green: 142 76% 36%;
        }
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>,
          'Helvetica Neue', Aria<PERSON>, sans-serif;
        background: hsl(var(--background));
        color: hsl(var(--foreground));
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        line-height: 1.5;
      }

      .container {
        text-align: center;
        max-width: 400px;
        width: 100%;
      }

      .youtube-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 1.5rem;
        padding: 12px;
        background: hsl(var(--muted));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .youtube-icon svg {
        width: 24px;
        height: 24px;
        fill: #ff0000;
      }

      .title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: hsl(var(--foreground));
      }

      .subtitle {
        color: hsl(var(--muted-foreground));
        margin-bottom: 2rem;
        font-size: 0.875rem;
      }

      .loading-container {
        margin-bottom: 2rem;
      }

      .spinner {
        width: 32px;
        height: 32px;
        border: 3px solid hsl(var(--muted));
        border-top: 3px solid hsl(var(--primary));
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .loading-text {
        color: hsl(var(--muted-foreground));
        font-size: 0.875rem;
      }

      .error-container {
        display: none;
        background: hsl(var(--destructive) / 0.1);
        border: 1px solid hsl(var(--destructive) / 0.2);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
      }

      .error-title {
        color: hsl(var(--destructive));
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }

      .error-text {
        color: hsl(var(--destructive));
        font-size: 0.75rem;
      }

      .success-container {
        display: none;
        background: hsl(var(--green) / 0.1);
        border: 1px solid hsl(var(--green) / 0.2);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
      }

      .success-title {
        color: hsl(var(--green));
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }

      .success-text {
        color: hsl(var(--green));
        font-size: 0.75rem;
      }

      .footer {
        position: fixed;
        bottom: 1rem;
        left: 50%;
        transform: translateX(-50%);
        color: hsl(var(--muted-foreground));
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .security-icon {
        width: 12px;
        height: 12px;
        fill: currentColor;
      }

      /* Smooth transitions */
      .fade-in {
        animation: fadeIn 0.3s ease-in-out;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- YouTube Icon -->
      <div class="youtube-icon">
        <svg viewBox="0 0 24 24">
          <path
            d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
          />
        </svg>
      </div>

      <!-- Title -->
      <h1 class="title">Connecting to YouTube</h1>
      <p class="subtitle">Please wait while we set up your connection...</p>

      <!-- Loading State -->
      <div class="loading-container fade-in">
        <div class="spinner"></div>
        <p class="loading-text" id="loading-text">
          Initializing secure connection...
        </p>
      </div>

      <!-- Error State -->
      <div class="error-container" id="error-container">
        <div class="error-title">Connection Failed</div>
        <div class="error-text" id="error-text">
          Something went wrong. Please try again.
        </div>
      </div>

      <!-- Success State -->
      <div class="success-container" id="success-container">
        <div class="success-title">Success!</div>
        <div class="success-text">
          Connection established. You can close this window.
        </div>
      </div>
    </div>

    <!-- Security Footer -->
    <div class="footer">
      <svg class="security-icon" viewBox="0 0 24 24">
        <path
          d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"
        />
      </svg>
      Secured by Google OAuth 2.0
    </div>

    <script>
      // State management
      let currentState = 'loading'
      const loadingTexts = [
        'Initializing secure connection...',
        'Connecting to Google...',
        'Setting up OAuth flow...',
        'Almost ready...',
      ]
      let textIndex = 0

      // Update loading text periodically
      const loadingTextElement = document.getElementById('loading-text')
      const updateLoadingText = () => {
        if (currentState === 'loading') {
          loadingTextElement.textContent = loadingTexts[textIndex]
          textIndex = (textIndex + 1) % loadingTexts.length
        }
      }

      // Start text rotation
      const textRotationInterval = setInterval(updateLoadingText, 1500)

      // Show error state
      function showError(message) {
        currentState = 'error'
        clearInterval(textRotationInterval)

        document.querySelector('.loading-container').style.display = 'none'
        document.getElementById('error-text').textContent = message
        document.getElementById('error-container').style.display = 'block'
        document.getElementById('error-container').classList.add('fade-in')
      }

      // Show success state
      function showSuccess() {
        currentState = 'success'
        clearInterval(textRotationInterval)

        document.querySelector('.loading-container').style.display = 'none'
        document.getElementById('success-container').style.display = 'block'
        document.getElementById('success-container').classList.add('fade-in')

        // Auto-close after success
        setTimeout(() => {
          window.close()
        }, 2000)
      }

      // Communication with parent window
      window.addEventListener('message', event => {
        // Ensure message is from the same origin
        if (event.origin !== window.location.origin) {
          console.log('Message from different origin, ignoring:', event.origin)
          return
        }

        const { type, url } = event.data

        switch (type) {
          case 'NAVIGATE_TO_OAUTH':
            console.log('Navigating to OAuth URL:', url)
            loadingTextElement.textContent = 'Redirecting to Google...'

            // Small delay for UX, then navigate
            setTimeout(() => {
              window.location.href = url
            }, 500)
            break

          case 'SHOW_ERROR':
            showError(
              event.data.message || 'Authentication failed. Please try again.'
            )
            break

          case 'SHOW_SUCCESS':
            showSuccess()
            break

          default:
            console.log('Unknown message type:', type)
        }
      })

      // Notify parent that loading page is ready
      function notifyParentReady() {
        try {
          if (window.opener) {
            window.opener.postMessage(
              { type: 'YOUTUBE_LOADING_READY' },
              window.location.origin
            )
            console.log('Notified parent that loading page is ready')
          }
        } catch (error) {
          console.error('Failed to notify parent:', error)
          showError('Failed to communicate with parent window')
        }
      }

      // Handle page load
      window.addEventListener('DOMContentLoaded', () => {
        console.log('YouTube loading page ready')

        // Add a small delay to ensure smooth animation
        setTimeout(() => {
          notifyParentReady()
        }, 100)
      })

      // Handle errors
      window.addEventListener('error', error => {
        console.error('Page error:', error)
        showError('An unexpected error occurred')
      })

      // Handle unload (when navigating to OAuth)
      window.addEventListener('beforeunload', () => {
        clearInterval(textRotationInterval)
      })

      // Cleanup on window close
      window.addEventListener('unload', () => {
        clearInterval(textRotationInterval)
      })
    </script>
  </body>
</html>
