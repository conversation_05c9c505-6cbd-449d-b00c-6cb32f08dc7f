// Service Worker for caching Clerk authentication assets
const CACHE_NAME = 'clerk-auth-cache-v2'
const STATIC_CACHE_NAME = 'auth-static-cache-v1'

// Clerk assets to cache
const CLERK_ASSETS = ['https://clerk.com', 'https://img.clerk.com']

// Static auth assets to cache aggressively
const STATIC_ASSETS = ['/signin', '/signup', '/auth.css']

// Install event - cache assets
self.addEventListener('install', event => {
  event.waitUntil(
    Promise.all([
      // Cache Clerk assets
      caches.open(CACHE_NAME).then(cache => {
        console.log('Caching Clerk assets for faster auth loading')
        return cache.addAll(CLERK_ASSETS).catch(error => {
          console.log('Failed to cache Clerk assets:', error)
        })
      }),
      // Cache static auth assets
      caches.open(STATIC_CACHE_NAME).then(cache => {
        console.log('Caching static auth assets')
        return cache.addAll(STATIC_ASSETS).catch(error => {
          console.log('Failed to cache static auth assets:', error)
        })
      }),
    ])
  )
  // Force activation of new service worker
  self.skipWaiting()
})

// Fetch event - serve from cache when possible
self.addEventListener('fetch', event => {
  // Only handle Clerk-related requests
  if (
    event.request.url.includes('clerk.com') ||
    event.request.url.includes('img.clerk.com')
  ) {
    event.respondWith(
      caches
        .match(event.request)
        .then(response => {
          // Return cached version or fetch from network
          return (
            response ||
            fetch(event.request).then(fetchResponse => {
              // Cache the new response
              const responseClone = fetchResponse.clone()
              caches.open(CACHE_NAME).then(cache => {
                cache.put(event.request, responseClone)
              })
              return fetchResponse
            })
          )
        })
        .catch(() => {
          // Fallback for offline scenarios
          console.log('Clerk asset request failed, using cache')
        })
    )
  }
})

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME && cacheName.includes('clerk')) {
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
})
