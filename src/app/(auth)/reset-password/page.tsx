import { ChevronLeft } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { Suspense } from 'react'

import { ResetPasswordForm } from '@/components/forms/reset-password-form'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'

export default function ResetPasswordPage() {
  // const { theme } = useTheme()
  const theme = 'dark'
  const logoSrc =
    theme === 'dark' ? '/adori-logo-dark.svg' : '/adori-logo-light.svg'
  return (
    <div className='bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10'>
      <Link href='/forgot-password' className='absolute top-5 left-5'>
        <Button variant='ghost'>
          <ChevronLeft />
          back
        </Button>
      </Link>

      <div className='flex w-full max-w-sm flex-col gap-6'>
        <Link
          href='/'
          className='flex items-center gap-2 self-center font-medium'
        >
          <div className='  text-primary-foreground flex size-6 items-center justify-center rounded-md'>
            <Image
              width={50}
              height={50}
              src={logoSrc}
              alt='Adori AI Logo'
              priority
            />
          </div>
          Adori AI
        </Link>
        <Suspense fallback={<Skeleton className='h-72 w-full' />}>
          <ResetPasswordForm />
        </Suspense>
      </div>
    </div>
  )
}
