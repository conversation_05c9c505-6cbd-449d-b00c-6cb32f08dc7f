import { ConversionGrid, DashboardContainer } from './_components'
import { conversionOptions } from './_components/conversion-options'
import type { Metadata } from 'next'

// Enable static generation with ISR - revalidate every hour
export const revalidate = 86400
export const dynamic = 'force-static'

// Generate metadata for SEO optimization
export const metadata: Metadata = {
  title: 'Create Video - Adori AI',
  description:
    'Convert your content into engaging videos with AI. Transform ideas, blogs, text, PDFs, audio, and podcasts into dynamic video content.',
  keywords: [
    'AI video creation',
    'content to video',
    'video generator',
    'blog to video',
    'text to video',
    'podcast to video',
  ],
  openGraph: {
    title: 'Create Video - Adori AI',
    description: 'Convert your content into engaging videos with AI.',
    type: 'website',
  },
}

export default function Dashboard() {
  return (
    <DashboardContainer>
      <ConversionGrid options={conversionOptions} />
    </DashboardContainer>
  )
}
