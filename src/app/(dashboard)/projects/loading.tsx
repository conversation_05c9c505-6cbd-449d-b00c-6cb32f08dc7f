import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader } from '@/components/ui/card'

export default function ProjectsLoading() {
  return (
    <div className='max-w-7xl mx-auto px-4 py-6 space-y-6'>
      {/* Header Section */}
      <div className='flex items-center justify-between'>
        <div className='space-y-2'>
          <Skeleton className='h-8 w-48' />
          <Skeleton className='h-5 w-64' />
        </div>
        <Skeleton className='h-10 w-32' />
      </div>

      {/* Search and Filters */}
      <div className='flex items-center gap-4'>
        <Skeleton className='h-10 flex-1 max-w-sm' />
        <Skeleton className='h-10 w-32' />
        <Skeleton className='h-10 w-24' />
      </div>

      {/* Projects Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className='group cursor-pointer'>
            <CardHeader className='p-0'>
              <Skeleton className='aspect-video w-full rounded-t-lg' />
            </CardHeader>
            <CardContent className='p-4 space-y-3'>
              <div className='space-y-2'>
                <Skeleton className='h-5 w-full' />
                <Skeleton className='h-4 w-24' />
              </div>
              
              <div className='flex items-center justify-between'>
                <Skeleton className='h-4 w-20' />
                <Skeleton className='h-8 w-8 rounded-full' />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
