'use client'

import { useState } from 'react'
import { ProjectCard } from './_components/ProjectCard'
import { useProjects } from '@/hooks/useProjects'
import { Button } from '@/components/ui/button'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertCircle, FileX } from 'lucide-react'
import Link from 'next/link'

// Loading skeleton for project cards
function ProjectCardSkeleton() {
  return (
    <div className='rounded-xl p-5 min-h-[140px] bg-gray-100 dark:bg-gray-800 animate-pulse'>
      <div className='flex justify-between items-start mb-4'>
        <Skeleton className='h-5 w-20 rounded-full' />
        <Skeleton className='h-5 w-5 rounded' />
      </div>
      <Skeleton className='h-6 w-3/4 mb-2' />
      <Skeleton className='h-4 w-1/2 mb-1' />
      <Skeleton className='h-4 w-1/3' />
    </div>
  )
}

// Empty state component
function EmptyState() {
  return (
    <div className='flex flex-col items-center justify-center py-16 text-center'>
      <div className='rounded-full bg-gray-100 dark:bg-gray-800 p-4 mb-4'>
        <FileX className='h-8 w-8 text-gray-400' />
      </div>
      <h3 className='text-lg font-semibold mb-2'>No projects yet</h3>
      <p className='text-muted-foreground mb-6 max-w-md'>
        Start creating your first video project to see it here. You can create
        videos from ideas, blogs, text, PDFs, audio, or podcasts.
      </p>
      <Button asChild>
        <Link href='/create-video/idea'>Create Your First Project</Link>
      </Button>
    </div>
  )
}

// Error state component
function ErrorState({
  error,
  onRetry,
}: {
  error: string
  onRetry: () => void
}) {
  return (
    <div className='flex flex-col items-center justify-center py-16 text-center'>
      <div className='rounded-full bg-red-100 dark:bg-red-900/20 p-4 mb-4'>
        <AlertCircle className='h-8 w-8 text-red-500' />
      </div>
      <h3 className='text-lg font-semibold mb-2'>Failed to load projects</h3>
      <p className='text-muted-foreground mb-6 max-w-md'>{error}</p>
      <Button onClick={onRetry} variant='outline'>
        Try Again
      </Button>
    </div>
  )
}

export default function ProjectsPage() {
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 8

  const { data, isLoading, error, refetch } = useProjects(
    currentPage,
    itemsPerPage
  )

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Generate pagination items
  const generatePaginationItems = () => {
    if (!data?.pagination) return []

    const { page, totalPages } = data.pagination
    const items = []

    // Always show first page
    if (totalPages > 0) {
      items.push(
        <PaginationItem key={1}>
          <PaginationLink
            onClick={() => handlePageChange(1)}
            isActive={page === 1}
            className='cursor-pointer'
          >
            1
          </PaginationLink>
        </PaginationItem>
      )
    }

    // Show ellipsis if there's a gap
    if (page > 3) {
      items.push(
        <PaginationItem key='ellipsis-start'>
          <PaginationEllipsis />
        </PaginationItem>
      )
    }

    // Show pages around current page
    for (
      let i = Math.max(2, page - 1);
      i <= Math.min(totalPages - 1, page + 1);
      i++
    ) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={page === i}
            className='cursor-pointer'
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      )
    }

    // Show ellipsis if there's a gap
    if (page < totalPages - 2) {
      items.push(
        <PaginationItem key='ellipsis-end'>
          <PaginationEllipsis />
        </PaginationItem>
      )
    }

    // Always show last page (if more than 1 page)
    if (totalPages > 1) {
      items.push(
        <PaginationItem key={totalPages}>
          <PaginationLink
            onClick={() => handlePageChange(totalPages)}
            isActive={page === totalPages}
            className='cursor-pointer'
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      )
    }

    return items
  }

  if (error) {
    return (
      <div className='container mx-auto p-6'>
        <h1 className='text-3xl font-bold mb-6'>Projects</h1>
        <ErrorState error={error.message} onRetry={() => refetch()} />
      </div>
    )
  }

  return (
    <div className='container mx-auto p-6'>
      <div className='flex items-center justify-between mb-6'>
        <h1 className='text-3xl font-bold'>Projects</h1>
        {data?.pagination && (
          <div className='text-sm text-muted-foreground'>
            {data.pagination.totalCount === 0
              ? 'No projects'
              : `${(data.pagination.page - 1) * data.pagination.limit + 1}-${Math.min(data.pagination.page * data.pagination.limit, data.pagination.totalCount)} of ${data.pagination.totalCount} projects`}
          </div>
        )}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'>
          {Array.from({ length: itemsPerPage }).map((_, index) => (
            <ProjectCardSkeleton key={index} />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading &&
        Array.isArray(data?.projects) &&
        data.projects.length === 0 && <EmptyState />}

      {/* Projects Grid */}
      {!isLoading &&
        Array.isArray(data?.projects) &&
        data.projects.length > 0 && (
          <>
            <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8'>
              {data.projects.map(project => (
                <ProjectCard key={project.projectId} {...project} />
              ))}
            </div>

            {/* Pagination */}
            {data.pagination.totalPages > 1 && (
              <div className='flex justify-center'>
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() =>
                          handlePageChange(Math.max(1, currentPage - 1))
                        }
                        className={`cursor-pointer ${!data.pagination.hasPrev ? 'pointer-events-none opacity-50' : ''}`}
                      />
                    </PaginationItem>

                    {generatePaginationItems()}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() =>
                          handlePageChange(
                            Math.min(
                              data.pagination.totalPages,
                              currentPage + 1
                            )
                          )
                        }
                        className={`cursor-pointer ${!data.pagination.hasNext ? 'pointer-events-none opacity-50' : ''}`}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </>
        )}
    </div>
  )
}
