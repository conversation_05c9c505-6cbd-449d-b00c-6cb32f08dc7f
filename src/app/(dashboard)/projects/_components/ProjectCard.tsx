'use client'

import Link from 'next/link'
import { Trash } from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'
import { useDeleteProject, Project } from '@/hooks/useProjects'
import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

const pastelColors = [
  { light: 'bg-pink-100', dark: 'dark:bg-pink-300' },
  { light: 'bg-yellow-100', dark: 'dark:bg-yellow-300' },
  { light: 'bg-green-100', dark: 'dark:bg-green-300' },
  { light: 'bg-blue-100', dark: 'dark:bg-blue-300' },
  { light: 'bg-purple-100', dark: 'dark:bg-purple-300' },
  { light: 'bg-orange-100', dark: 'dark:bg-orange-300' },
]

function getConsistentPastel(projectId: string) {
  // Use the project ID to ensure consistent colors
  const hash = projectId.split('').reduce((a, b) => {
    a = (a << 5) - a + b.charCodeAt(0)
    return a & a
  }, 0)
  const index = Math.abs(hash) % pastelColors.length
  return pastelColors[index]
}

export function ProjectCard({
  projectId,
  projectName,
  updatedAt,
  method,
  orientation,
}: Project) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const deleteProjectMutation = useDeleteProject()
  const pastel = getConsistentPastel(projectId)

  // Prevent navigation when clicking on buttons
  const stopPropagation = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = () => {
    deleteProjectMutation.mutate(projectId)
    setShowDeleteDialog(false)
  }

  const updatedAtDate = new Date(updatedAt)

  return (
    <>
      <div
        className={cn(
          'rounded-xl p-5 relative min-h-[140px] flex flex-col justify-between shadow-sm transition-all duration-300 hover:shadow-md group ',
          pastel.light,
          pastel.dark
        )}
      >
        {/* Method pill top left */}
        <span className='absolute left-5 top-5 bg-white/80 dark:bg-gray-800 text-xs font-medium px-3 py-1 rounded-full shadow-sm z-10 text-gray-900 dark:text-white'>
          {method}
        </span>

        {/* Trash button top right */}
        <button
          className='absolute right-5 top-5 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-300 transition z-10 disabled:opacity-50 cursor-pointer'
          onClick={handleDeleteClick}
          onMouseDown={stopPropagation}
          disabled={deleteProjectMutation.isPending}
          tabIndex={-1}
          type='button'
        >
          <Trash className='w-5 h-5' />
        </button>

        <div className='font-semibold text-lg mb-2 line-clamp-2 pr-8 text-gray-900 dark:text-gray-800 mt-8'>
          {projectName}
        </div>

        <div className='flex flex-col gap-1 text-xs text-gray-600 dark:text-gray-800 mt-auto'>
          <span className='font-medium'>{orientation}</span>
          <span>
            Edited {formatDistanceToNow(updatedAtDate, { addSuffix: true })}
          </span>
        </div>

        <Link
          href={`/scene-editor?projectId=${projectId}`}
          className='mt-2 flex justify-center transition-opacity duration-300 z-20 bg-black/60 text-white px-3 py-1 rounded-full text-xs font-medium shadow cursor-pointer'
          tabIndex={0}
        >
          Edit
        </Link>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Project</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete &ldquo;{projectName}&rdquo;? This
              action cannot be undone and will permanently remove the project
              and all its data.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteConfirm}
              disabled={deleteProjectMutation.isPending}
              className='bg-red-600 hover:bg-red-700'
            >
              {deleteProjectMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
