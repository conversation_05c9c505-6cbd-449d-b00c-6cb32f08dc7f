'use client'

import React from 'react'
import { DashboardContainer } from '../../_components'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'

interface CreateVideoLayoutProps {
  children: React.ReactNode
  backUrl?: string
}

const CreateVideoLayout = ({
  children,
  backUrl = '/',
}: CreateVideoLayoutProps) => {
  const router = useRouter()

  return (
    <DashboardContainer>
      <div className='flex items-center justify-end mb-6'>
        <Button
          variant='outline'
          onClick={() => router.push(backUrl)}
          className='h-9'
        >
          Back
        </Button>
      </div>

      <div className='bg-card border rounded-lg p-6'>{children}</div>
    </DashboardContainer>
  )
}

export default CreateVideoLayout
