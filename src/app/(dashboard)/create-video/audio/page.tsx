import { AudioLines, Music } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import type { Metadata } from 'next'
import { AudioToVideoForm } from './_components/audio-to-video-form'

// Enable static generation with ISR - revalidate every hour
export const revalidate = 3600
export const dynamic = 'force-static'

// Generate metadata for SEO optimization
export const metadata: Metadata = {
  title: 'Audio to Video - AI Audio Content Converter | Adori AI',
  description:
    'Transform your audio files into engaging videos with AI. Upload your audio content and create professional videos with AI-generated visuals and animations.',
  keywords: [
    'audio to video converter',
    'AI audio transformation',
    'audio content videos',
    'podcast to video',
    'audio automation',
    'video creation',
    'audio processing',
    'content repurposing',
  ],
  openGraph: {
    title: 'Audio to Video - AI Audio Content Converter | Adori AI',
    description: 'Transform your audio files into engaging videos with AI.',
    type: 'website',
  },
}

export default function AudioToVideoPage() {
  return (
    <div className='max-w-2xl mx-auto px-4 py-4 space-y-4'>
      {/* Header Section - Static, renders immediately */}
      <div className='text-center space-y-2'>
        <div className='inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium'>
          <AudioLines className='h-3 w-3' />
          Audio to Video Generator
        </div>
        {/* <h1 className='text-2xl font-bold'>
          Transform Audio Into Engaging Videos
        </h1> */}
        <p className='text-muted-foreground text-sm max-w-2xl mx-auto'>
          Upload your audio files and create professional videos with
          AI-generated visuals and animations
        </p>
      </div>

      {/* Audio Configuration Card - Static structure */}
      <Card>
        <CardHeader className='pb-3'>
          <CardTitle className='flex items-center gap-2 text-lg'>
            <Music className='h-4 w-4 text-primary' />
            Audio Upload & Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Client component with internal progressive loading */}
          <AudioToVideoForm />
        </CardContent>
      </Card>
    </div>
  )
}
