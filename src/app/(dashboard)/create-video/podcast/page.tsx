import { Podcast as PodcastIcon } from 'lucide-react'
import { PodcastWorkflowWrapper } from './_components/podcast-workflow-wrapper'

export const revalidate = 3600 // 1 hour
export const dynamic = 'force-static'

export default function PodcastToVideoPage() {
  return (
    <div className='min-h-screen bg-background'>
      {/* Header Section - Static */}
      <div className='text-center space-y-2 py-8'>
        <div className='inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium'>
          <PodcastIcon className='h-3 w-3' />
          Podcast to Video Generator
        </div>
        {/* <h1 className='text-2xl font-bold'>Podcast to Video</h1> */}
        <p className='text-muted-foreground text-sm max-w-2xl mx-auto'>
          Transform podcast episodes into engaging videos with subtitles and
          visuals
        </p>
      </div>

      {/* Progressive Loading Workflow */}
      <PodcastWorkflowWrapper />
    </div>
  )
}
