'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Search, Link, Loader2, AlertCircle, X } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useInfinitePodcastSearch } from '@/hooks/usePodcastSearch'
import { usePodcastRSS } from '@/hooks/usePodcastRSS'
import type { Podcast } from '@/hooks/usePodcastSearch'
import { PodcastResultsSkeleton } from '@/app/(dashboard)/_components/video-form'

interface PodcastSearchProps {
  onPodcastSelect: (podcast: Podcast) => void
}

export function PodcastSearch({ onPodcastSelect }: PodcastSearchProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [rssUrl, setRssUrl] = useState('')
  const [activeTab, setActiveTab] = useState('search')
  const [searchTrigger, setSearchTrigger] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Recently searched podcasts state
  const [recentPodcasts, setRecentPodcasts] = useState<Podcast[]>([])

  // Helper: Only keep fields needed for card
  const getCardFields = (podcast: Podcast): Podcast => ({
    id: podcast.id,
    title: podcast.title,
    author: podcast.author,
    description: podcast.description,
    image: podcast.image,
    url: podcast.url,
    link: podcast.link,
    episodeCount: podcast.episodeCount,
    language: podcast.language,
    explicit: podcast.explicit,
    lastUpdateTime: podcast.lastUpdateTime,
    categories: podcast.categories,
  })

  // Load from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem('recentPodcasts')
    if (stored) {
      try {
        setRecentPodcasts(JSON.parse(stored))
      } catch {}
    }
  }, [])

  // Add to recent podcasts (dedupe by id, latest first, max 5)
  const addRecentPodcast = (podcast: Podcast) => {
    if (!podcast || !podcast.id || !podcast.title) {
      console.warn('addRecentPodcast: invalid podcast', podcast)
      return
    }

    setRecentPodcasts(prev => {
      const filtered = prev.filter(p => p.id !== podcast.id)
      const updated = [getCardFields(podcast), ...filtered].slice(0, 5)
      return updated
    })
    // Write to localStorage immediately, outside setState
    const stored = localStorage.getItem('recentPodcasts')
    let prevArr: Podcast[] = []
    if (stored) {
      try {
        prevArr = JSON.parse(stored)
      } catch {}
    }
    const filtered = prevArr.filter(p => p.id !== podcast.id)
    const updated = [getCardFields(podcast), ...filtered].slice(0, 5)
    localStorage.setItem('recentPodcasts', JSON.stringify(updated))
  }

  // Clear all recent podcasts
  const clearRecentPodcasts = () => {
    setRecentPodcasts([])
    localStorage.setItem('recentPodcasts', JSON.stringify([]))
  }

  // Debounce search input to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery.trim())
    }, 500) // 500ms delay

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Trigger search when debounced query changes
  useEffect(() => {
    if (debouncedSearchQuery && activeTab === 'search') {
      setSearchTrigger(debouncedSearchQuery)
    }
  }, [debouncedSearchQuery, activeTab])

  const {
    data: infiniteSearchResults,
    isLoading: isSearching,
    error: searchError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfinitePodcastSearch(searchTrigger, !!searchTrigger, 20)

  // Flatten the infinite query results for easier use
  const searchResults = infiniteSearchResults
    ? {
        status: infiniteSearchResults.pages[0]?.status || 'success',
        podcasts: infiniteSearchResults.pages.flatMap(page => page.podcasts),
        count: infiniteSearchResults.pages[0]?.count || 0,
        query: infiniteSearchResults.pages[0]?.query || searchTrigger,
      }
    : null

  const {
    data: rssResult,
    isLoading: isRssLoading,
    error: rssError,
  } = usePodcastRSS(rssUrl, activeTab === 'rss' && !!rssUrl)

  const handleSearch = () => {
    if (searchQuery.trim()) {
      setSearchTrigger(searchQuery.trim())
    }
  }

  const handleRssLookup = () => {
    if (rssUrl.trim()) {
      // RSS lookup happens automatically via the hook
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent, type: 'search' | 'rss') => {
    if (e.key === 'Enter') {
      if (type === 'search') {
        handleSearch()
      } else {
        handleRssLookup()
      }
    }
  }

  const formatEpisodeCount = (count: number) => {
    if (count > 1000) {
      return `${(count / 1000).toFixed(1)}k episodes`
    }
    return `${count} episodes`
  }

  const truncateDescription = (
    description: string,
    maxLength: number = 100
  ) => {
    if (description.length <= maxLength) return description
    return description.substring(0, maxLength) + '...'
  }

  // Simple placeholder data URL for a gray square
  const placeholderImage =
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNC41IDIwSDM5LjVDNDAuMzI4NCAyMCA0MSAyMC42NzE2IDQxIDIxLjVWNDIuNUM0MSA0My4zMjg0IDQwLjMyODQgNDQgMzkuNSA0NEgyNC41QzIzLjY3MTYgNDQgMjMgNDMuMzI4NCAyMyA0Mi41VjIxLjVDMjMgMjAuNjcxNiAyMy42NzE2IDIwIDI0LjUgMjBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K'

  return (
    <Card className='gap-3 overflow-hidden rounded-lg min-h-[60vh] flex flex-col'>
      <CardHeader className=''>
        <CardTitle className='flex items-center gap-2 text-lg'>
          <Search className='h-4 w-4 text-pink-600 dark:text-pink-400' />
          Find Podcast
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-3 p-4 flex-1 flex flex-col'>
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className='w-full flex-1 flex flex-col'
        >
          <TabsList className='grid w-full grid-cols-2 bg-foreground/20'>
            <TabsTrigger
              className='dark:data-[state=active]:bg-background dark:data-[state=active]:text-foreground dark:data-[state=active]:shadow-sm'
              value='search'
            >
              Search Podcasts
            </TabsTrigger>
            <TabsTrigger
              className='dark:data-[state=active]:bg-background dark:data-[state=active]:text-foreground dark:data-[state=active]:shadow-sm'
              value='rss'
            >
              RSS Feed URL
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value='search'
            className='space-y-3 mt-3 flex-1 flex flex-col'
          >
            <div className='space-y-2'>
              <Label htmlFor='search' className='text-sm'>
                Search for podcasts
              </Label>
              <div className='flex gap-2'>
                <Input
                  ref={searchInputRef}
                  id='search'
                  placeholder='e.g. TED Talks, Joe Rogan, Serial'
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  onKeyPress={e => handleKeyPress(e, 'search')}
                  className='h-9 text-sm'
                />
                <Button
                  onClick={handleSearch}
                  disabled={!searchQuery.trim() || isSearching}
                  size='sm'
                  className='h-9 px-3'
                >
                  {isSearching ? (
                    <Loader2 className='w-4 h-4 animate-spin' />
                  ) : (
                    <Search className='w-4 h-4' />
                  )}
                </Button>
              </div>
            </div>

            {/* Recently Searched Podcasts (only when search box is empty) */}
            {recentPodcasts.length > 0 && searchQuery.trim() === '' && (
              <div className='mb-2 flex-1 flex flex-col'>
                <div className='flex items-center justify-between mb-1'>
                  <span className='text-xs font-semibold text-muted-foreground'>
                    Recently searched podcast
                  </span>
                  <button
                    className='text-xs text-muted-foreground hover:text-destructive flex items-center gap-1 px-1 py-0.5 rounded transition-colors'
                    onClick={clearRecentPodcasts}
                    title='Clear all'
                  >
                    <X className='w-3 h-3' /> Clear
                  </button>
                </div>
                <ScrollArea className='flex-1 w-full mb-2 px-2 overflow-y-auto'>
                  <div className='flex flex-col gap-2'>
                    {recentPodcasts.map((podcast, index) => (
                      <motion.div
                        key={podcast.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className='group'
                      >
                        <div
                          className='flex items-start gap-3 p-3 rounded-md border bg-card hover:bg-accent/50 hover:shadow-sm transition-all duration-200 cursor-pointer'
                          onClick={() => {
                            addRecentPodcast(podcast)
                            onPodcastSelect(podcast)
                          }}
                        >
                          <img
                            src={podcast.image || placeholderImage}
                            alt={podcast.title}
                            className='w-12 h-12 rounded-md object-cover flex-shrink-0 bg-muted'
                            onError={e => {
                              const target = e.target as HTMLImageElement
                              if (target.src !== placeholderImage) {
                                target.src = placeholderImage
                              }
                            }}
                          />
                          <div className='flex-1 min-w-0'>
                            <h3 className='font-medium text-sm leading-tight line-clamp-2 mb-1'>
                              {podcast.title}
                            </h3>
                            <p className='text-xs text-muted-foreground mb-1'>
                              {podcast.author}
                            </p>
                            {podcast.description && (
                              <p className='text-xs text-muted-foreground line-clamp-2 mb-2'>
                                {truncateDescription(podcast.description)}
                              </p>
                            )}
                            <div className='flex items-center gap-2'>
                              <Badge
                                variant='secondary'
                                className='text-xs px-2 py-0.5'
                              >
                                {formatEpisodeCount(podcast.episodeCount)}
                              </Badge>
                              {podcast.explicit && (
                                <Badge
                                  variant='destructive'
                                  className='text-xs px-2 py-0.5'
                                >
                                  Explicit
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* Search Results */}
            {isSearching && <PodcastResultsSkeleton />}

            {searchError && (
              <div className='flex items-center justify-center py-8'>
                <div className='text-center'>
                  <AlertCircle className='w-8 h-8 mx-auto mb-3 text-destructive' />
                  <p className='text-sm text-destructive'>
                    Failed to search podcasts. Please try again.
                  </p>
                </div>
              </div>
            )}

            {searchResults && searchResults.podcasts.length > 0 && (
              <ScrollArea className='h-[350px] w-full'>
                <div className='space-y-2 pr-4'>
                  {searchResults.podcasts.map((podcast, index) => (
                    <motion.div
                      key={podcast.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className='group'
                    >
                      <div
                        className='flex items-start gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 hover:shadow-sm transition-all duration-200 cursor-pointer'
                        onClick={() => {
                          addRecentPodcast(podcast)
                          onPodcastSelect(podcast)
                        }}
                      >
                        <img
                          src={podcast.image || placeholderImage}
                          alt={podcast.title}
                          className='w-12 h-12 rounded-md object-cover flex-shrink-0 bg-muted'
                          onError={e => {
                            const target = e.target as HTMLImageElement
                            if (target.src !== placeholderImage) {
                              target.src = placeholderImage
                            }
                          }}
                        />
                        <div className='flex-1 min-w-0'>
                          <h3 className='font-medium text-sm leading-tight line-clamp-2 mb-1'>
                            {podcast.title}
                          </h3>
                          <p className='text-xs text-muted-foreground mb-1'>
                            {podcast.author}
                          </p>
                          {podcast.description && (
                            <p className='text-xs text-muted-foreground line-clamp-2 mb-2'>
                              {truncateDescription(podcast.description)}
                            </p>
                          )}
                          <div className='flex items-center gap-2'>
                            <Badge
                              variant='secondary'
                              className='text-xs px-2 py-0.5'
                            >
                              {formatEpisodeCount(podcast.episodeCount)}
                            </Badge>
                            {podcast.explicit && (
                              <Badge
                                variant='destructive'
                                className='text-xs px-2 py-0.5'
                              >
                                Explicit
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
                {/* Show More Button */}
                {searchResults &&
                  searchResults.podcasts.length > 0 &&
                  hasNextPage && (
                    <div className='flex justify-center py-4'>
                      <Button
                        variant='outline'
                        onClick={() => fetchNextPage()}
                        disabled={isFetchingNextPage}
                        className='text-sm'
                      >
                        {isFetchingNextPage
                          ? 'Loading...'
                          : 'Show More Podcasts'}
                      </Button>
                    </div>
                  )}
              </ScrollArea>
            )}

            {searchResults &&
              searchResults.podcasts.length === 0 &&
              searchTrigger && (
                <div className='flex items-center justify-center py-8'>
                  <div className='text-center'>
                    <AlertCircle className='w-8 h-8 mx-auto mb-3 text-muted-foreground' />
                    <p className='text-sm text-muted-foreground'>
                      No podcasts found for &ldquo;{searchTrigger}&rdquo;. Try a
                      different search term.
                    </p>
                  </div>
                </div>
              )}
          </TabsContent>

          <TabsContent
            value='rss'
            className='space-y-3 mt-3 flex-1 flex flex-col'
          >
            <div className='space-y-2'>
              <Label htmlFor='rss' className='text-sm'>
                RSS Feed URL
              </Label>
              <div className='flex gap-2'>
                <Input
                  id='rss'
                  placeholder='https://feeds.example.com/podcast.xml'
                  value={rssUrl}
                  onChange={e => setRssUrl(e.target.value)}
                  onKeyPress={e => handleKeyPress(e, 'rss')}
                  className='h-9 text-sm'
                />
                <Button
                  onClick={handleRssLookup}
                  disabled={!rssUrl.trim() || isRssLoading}
                  size='sm'
                  className='h-9 px-3'
                >
                  {isRssLoading ? (
                    <Loader2 className='w-4 h-4 animate-spin' />
                  ) : (
                    <Link className='w-4 h-4' />
                  )}
                </Button>
              </div>
            </div>

            {/* Recently Searched Podcasts (always show in RSS tab if any) */}
            {recentPodcasts.length > 0 && rssUrl.trim() === '' && (
              <div className='mb-2 flex-1 flex flex-col'>
                <div className='flex items-center justify-between mb-1'>
                  <span className='text-xs font-semibold text-muted-foreground'>
                    Recently searched podcast
                  </span>
                  <button
                    className='text-xs text-muted-foreground hover:text-destructive flex items-center gap-1 px-1 py-0.5 rounded transition-colors'
                    onClick={clearRecentPodcasts}
                    title='Clear all'
                  >
                    <X className='w-3 h-3' /> Clear
                  </button>
                </div>
                <ScrollArea className='flex-1 w-full mb-2 px-2 overflow-y-auto'>
                  <div className='flex flex-col gap-2'>
                    {recentPodcasts.map((podcast, index) => (
                      <motion.div
                        key={podcast.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className='group'
                      >
                        <div
                          className='flex items-start gap-3 p-3 rounded-md border bg-card hover:bg-accent/50 hover:shadow-sm transition-all duration-200 cursor-pointer'
                          onClick={() => {
                            addRecentPodcast(podcast)
                            onPodcastSelect(podcast)
                          }}
                        >
                          <img
                            src={podcast.image || placeholderImage}
                            alt={podcast.title}
                            className='w-12 h-12 rounded-md object-cover flex-shrink-0 bg-muted'
                            onError={e => {
                              const target = e.target as HTMLImageElement
                              if (target.src !== placeholderImage) {
                                target.src = placeholderImage
                              }
                            }}
                          />
                          <div className='flex-1 min-w-0'>
                            <h3 className='font-medium text-sm leading-tight line-clamp-2 mb-1'>
                              {podcast.title}
                            </h3>
                            <p className='text-xs text-muted-foreground mb-1'>
                              {podcast.author}
                            </p>
                            {podcast.description && (
                              <p className='text-xs text-muted-foreground line-clamp-2 mb-2'>
                                {truncateDescription(podcast.description)}
                              </p>
                            )}
                            <div className='flex items-center gap-2'>
                              <Badge
                                variant='secondary'
                                className='text-xs px-2 py-0.5'
                              >
                                {formatEpisodeCount(podcast.episodeCount)}
                              </Badge>
                              {podcast.explicit && (
                                <Badge
                                  variant='destructive'
                                  className='text-xs px-2 py-0.5'
                                >
                                  Explicit
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* RSS Loading */}
            {isRssLoading && (
              <div className='flex items-center justify-center py-8'>
                <div className='text-center'>
                  <Loader2 className='w-8 h-8 animate-spin mx-auto mb-3 text-muted-foreground' />
                  <p className='text-sm text-muted-foreground'>
                    Looking up RSS feed...
                  </p>
                </div>
              </div>
            )}

            {rssError && (
              <div className='flex items-center justify-center py-8'>
                <div className='text-center'>
                  <AlertCircle className='w-8 h-8 mx-auto mb-3 text-destructive' />
                  <p className='text-sm text-destructive'>
                    Failed to lookup RSS feed. Please check the URL and try
                    again.
                  </p>
                </div>
              </div>
            )}

            {rssResult && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className='group'
              >
                <div
                  className='flex items-start gap-3 p-3 rounded-md border bg-card hover:bg-accent/50 hover:shadow-sm transition-all duration-200 cursor-pointer'
                  onClick={() => {
                    addRecentPodcast(rssResult.podcast)
                    onPodcastSelect(rssResult.podcast)
                  }}
                >
                  <img
                    src={rssResult.podcast.image || placeholderImage}
                    alt={rssResult.podcast.title}
                    className='w-12 h-12 rounded-md object-cover flex-shrink-0 bg-muted'
                    onError={e => {
                      const target = e.target as HTMLImageElement
                      if (target.src !== placeholderImage) {
                        target.src = placeholderImage
                      }
                    }}
                  />
                  <div className='flex-1 min-w-0'>
                    <h3 className='font-medium text-sm leading-tight line-clamp-2 mb-1'>
                      {rssResult.podcast.title}
                    </h3>
                    <p className='text-xs text-muted-foreground mb-1'>
                      {rssResult.podcast.author}
                    </p>
                    {rssResult.podcast.description && (
                      <p className='text-xs text-muted-foreground line-clamp-2 mb-2'>
                        {truncateDescription(rssResult.podcast.description)}
                      </p>
                    )}
                    <div className='flex items-center gap-2'>
                      <Badge
                        variant='secondary'
                        className='text-xs px-2 py-0.5'
                      >
                        {formatEpisodeCount(rssResult.podcast.episodeCount)}
                      </Badge>
                      {rssResult.podcast.explicit && (
                        <Badge
                          variant='destructive'
                          className='text-xs px-2 py-0.5'
                        >
                          Explicit
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
