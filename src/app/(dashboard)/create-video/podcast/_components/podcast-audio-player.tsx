'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Play, Pause } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PodcastAudioPlayerProps {
  audioUrl: string
  onDurationChange?: (duration: number) => void
  className?: string
}

/**
 * PodcastAudioPlayer - A custom audio player component for podcast episodes
 *
 * This component provides:
 * - Play/pause functionality
 * - Seek/scrub through audio
 * - Real-time duration tracking
 * - Automatic duration detection via loadmetadata event
 * - Compact design with centered layout
 *
 * The component automatically loads the audio file and extracts the actual duration
 * when the metadata is loaded, which is more accurate than API-provided durations.
 */
export function PodcastAudioPlayer({
  audioUrl,
  onDurationChange,
  className,
}: PodcastAudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)

  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Format time as MM:SS (no decimals)
  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60)
    const seconds = Math.floor(timeInSeconds % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Initialize audio element
  useEffect(() => {
    if (!audioUrl) return

    const audio = new Audio(audioUrl)
    audioRef.current = audio

    // Set up event listeners
    const handleLoadedMetadata = () => {
      setDuration(audio.duration)
      setHasLoaded(true)
      setIsLoading(false)
      // Notify parent component of the actual duration
      onDurationChange?.(audio.duration)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime)
    }

    const handleEnded = () => {
      setIsPlaying(false)
      setCurrentTime(0)
    }

    const handleError = (error: Event) => {
      console.error('Audio loading error:', error)
      setIsLoading(false)
      setHasLoaded(false)
    }

    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('ended', handleEnded)
    audio.addEventListener('error', handleError)

    // Start loading
    setIsLoading(true)
    audio.load()

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('ended', handleEnded)
      audio.removeEventListener('error', handleError)
      audio.pause()
      audioRef.current = null
    }
  }, [audioUrl, onDurationChange])

  // Handle play/pause
  const togglePlayPause = async () => {
    if (!audioRef.current || !hasLoaded) return

    try {
      if (isPlaying) {
        audioRef.current.pause()
        setIsPlaying(false)
      } else {
        await audioRef.current.play()
        setIsPlaying(true)
      }
    } catch (error) {
      console.error('Error playing audio:', error)
    }
  }

  // Handle seek
  const handleSeek = (value: number[]) => {
    if (!audioRef.current || !hasLoaded) return
    const newTime = value[0]
    audioRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }

  return (
    <div
      className={cn(
        'flex items-center gap-3 p-2 bg-muted/50 rounded-lg',
        className
      )}
    >
      {/* Play/Pause Button */}
      <Button
        variant='ghost'
        size='sm'
        onClick={togglePlayPause}
        disabled={!hasLoaded || isLoading}
        className='h-8 w-8 p-0 flex-shrink-0'
      >
        {isLoading ? (
          <div className='h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
        ) : isPlaying ? (
          <Pause className='h-4 w-4' />
        ) : (
          <Play className='h-4 w-4 ml-0.5' />
        )}
      </Button>

      {/* Current Time */}
      <span className='text-xs text-muted-foreground w-8 text-center'>
        {formatTime(currentTime)}
      </span>

      {/* Progress Bar */}
      <div className='flex-1 min-w-0'>
        <Slider
          value={[currentTime]}
          onValueChange={handleSeek}
          max={duration}
          step={0.1}
          disabled={!hasLoaded}
          className='w-full'
        />
      </div>

      {/* Total Duration */}
      <span className='text-xs text-muted-foreground w-8 text-center'>
        {formatTime(duration)}
      </span>
    </div>
  )
}
