'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { ChevronLeft, Calendar, Clock, Play, AlertCircle } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useInfinitePodcastEpisodes } from '@/hooks/usePodcastEpisodes'
import type { Podcast } from '@/hooks/usePodcastSearch'
import type { Episode } from '@/hooks/usePodcastEpisodes'
import { EpisodesLoadingSkeleton } from '@/app/(dashboard)/_components/video-form'

interface EpisodeSelectionProps {
  podcast: Podcast
  onEpisodeSelect: (episode: Episode) => void
  onBack: () => void
}

export function EpisodeSelection({
  podcast,
  onEpisodeSelect,
  onBack,
}: EpisodeSelectionProps) {
  const [selectedEpisodeId, setSelectedEpisodeId] = useState<number | null>(
    null
  )

  const {
    data: infiniteEpisodesData,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfinitePodcastEpisodes(podcast.id, true, 20)

  // Flatten the infinite query results for easier use
  const episodesData = infiniteEpisodesData
    ? {
        status: infiniteEpisodesData.pages[0]?.status || 'success',
        episodes: infiniteEpisodesData.pages.flatMap(page => page.episodes),
        count: infiniteEpisodesData.pages[0]?.count || 0,
      }
    : null

  const handleEpisodeSelect = (episode: Episode) => {
    setSelectedEpisodeId(episode.id)
    onEpisodeSelect(episode)
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const truncateDescription = (
    description: string,
    maxLength: number = 120
  ) => {
    if (description.length <= maxLength) return description
    return description.substring(0, maxLength) + '...'
  }

  // Simple placeholder data URL for a gray square
  const placeholderImage =
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNC41IDIwSDM5LjVDNDAuMzI4NCAyMCA0MSAyMC42NzE2IDQxIDIxLjVWNDIuNUM0MSA0My4zMjg0IDQwLjMyODQgNDQgMzkuNSA0NEgyNC41QzIzLjY3MTYgNDQgMjMgNDMuMzI4NCAyMyA0Mi41VjIxLjVDMjMgMjAuNjcxNiAyMy42NzE2IDIwIDI0LjUgMjBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K'

  return (
    <div className='space-y-3'>
      {/* Header with back button and podcast info */}
      <Card className='gap-1'>
        <CardHeader className='pb-2'>
          <div className='flex items-center gap-2'>
            <Button
              variant='ghost'
              size='sm'
              onClick={onBack}
              className='h-8 px-2'
            >
              <ChevronLeft className='w-4 h-4 mr-1' />
              Back
            </Button>
          </div>
        </CardHeader>
        <CardContent className='pt-0'>
          <div className='flex items-start gap-3'>
            <img
              src={podcast.image || placeholderImage}
              alt={podcast.title}
              className='w-12 h-12 rounded-md object-cover flex-shrink-0 bg-muted'
              onError={e => {
                const target = e.target as HTMLImageElement
                if (target.src !== placeholderImage) {
                  target.src = placeholderImage
                }
              }}
            />
            <div className='flex-1 min-w-0'>
              <h2 className='font-medium text-base leading-tight line-clamp-2 mb-1'>
                {podcast.title}
              </h2>
              <p className='text-sm text-muted-foreground mb-2'>
                {podcast.author}
              </p>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary' className='text-xs px-2 py-0.5'>
                  {podcast.episodeCount} episodes
                </Badge>
                {podcast.explicit && (
                  <Badge variant='destructive' className='text-xs px-2 py-0.5'>
                    Explicit
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Episodes List */}
      <Card className='gap-1'>
        <CardHeader className='pb-2'>
          <CardTitle className='flex items-center gap-2 text-lg'>
            <Play className='h-4 w-4 text-primary' />
            Select Episode
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading && <EpisodesLoadingSkeleton />}

          {error && (
            <div className='flex items-center justify-center py-8'>
              <div className='text-center'>
                <AlertCircle className='w-8 h-8 mx-auto mb-3 text-destructive' />
                <p className='text-sm text-destructive'>
                  Failed to load episodes. Please try again.
                </p>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => window.location.reload()}
                  className='mt-2'
                >
                  Retry
                </Button>
              </div>
            </div>
          )}

          {episodesData && episodesData.episodes.length > 0 && (
            <ScrollArea className='h-[350px] w-full'>
              <div className='space-y-2 pr-4'>
                {episodesData.episodes.map((episode, index) => (
                  <motion.div
                    key={episode.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className='group'
                  >
                    <div
                      className={`flex items-start gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 hover:shadow-sm transition-all duration-200 cursor-pointer ${
                        selectedEpisodeId === episode.id
                          ? 'ring-2 ring-primary bg-accent/30'
                          : ''
                      }`}
                      onClick={() => handleEpisodeSelect(episode)}
                    >
                      {episode.image && (
                        <img
                          src={episode.image || placeholderImage}
                          alt={episode.title}
                          className='w-10 h-10 rounded-md object-cover flex-shrink-0 bg-muted'
                          onError={e => {
                            const target = e.target as HTMLImageElement
                            if (target.src !== placeholderImage) {
                              target.src = placeholderImage
                            }
                          }}
                        />
                      )}
                      <div className='flex-1 min-w-0'>
                        <h3 className='font-medium text-sm leading-tight line-clamp-2 mb-1'>
                          {episode.title}
                        </h3>

                        <div className='flex items-center gap-3 text-xs text-muted-foreground mb-1'>
                          <div className='flex items-center gap-1'>
                            <Calendar className='w-3 h-3' />
                            {formatDate(episode.datePublished)}
                          </div>
                          {episode.duration > 0 && (
                            <div className='flex items-center gap-1'>
                              <Clock className='w-3 h-3' />
                              {episode.durationFormatted}
                            </div>
                          )}
                          {episode.episode && (
                            <Badge
                              variant='outline'
                              className='text-xs px-1.5 py-0'
                            >
                              Episode {episode.episode}
                            </Badge>
                          )}
                        </div>

                        {episode.description && (
                          <p className='text-xs text-muted-foreground line-clamp-2 mb-2'>
                            {truncateDescription(episode.description)}
                          </p>
                        )}
                      </div>
                      <div className='flex-shrink-0'>
                        <Button
                          size='sm'
                          variant={
                            selectedEpisodeId === episode.id
                              ? 'default'
                              : 'outline'
                          }
                          className='h-7 px-3 text-xs opacity-60 group-hover:opacity-100 transition-opacity'
                          onClick={e => {
                            e.stopPropagation()
                            handleEpisodeSelect(episode)
                          }}
                        >
                          {selectedEpisodeId === episode.id
                            ? 'Selected'
                            : 'Select'}
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
              {/* Load More Episodes Button */}
              {episodesData &&
                episodesData.episodes.length > 0 &&
                hasNextPage && (
                  <div className='flex justify-center py-4'>
                    <Button
                      variant='outline'
                      onClick={() => fetchNextPage()}
                      disabled={isFetchingNextPage}
                      className='text-sm'
                    >
                      {isFetchingNextPage ? 'Loading...' : 'Load More Episodes'}
                    </Button>
                  </div>
                )}
            </ScrollArea>
          )}

          {episodesData && episodesData.episodes.length === 0 && (
            <div className='flex items-center justify-center py-8'>
              <div className='text-center'>
                <AlertCircle className='w-8 h-8 mx-auto mb-3 text-muted-foreground' />
                <p className='text-sm text-muted-foreground'>
                  No episodes found for this podcast.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
