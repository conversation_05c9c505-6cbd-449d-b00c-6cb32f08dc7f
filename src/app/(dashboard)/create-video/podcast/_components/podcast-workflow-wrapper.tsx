'use client'

import dynamic from 'next/dynamic'
import { PodcastSearchSkeleton } from '@/app/(dashboard)/_components/video-form'

// Dynamically import the PodcastWorkflow to avoid SSR issues with framer-motion
const PodcastWorkflow = dynamic(
  () =>
    import('./podcast-workflow').then(mod => ({
      default: mod.PodcastWorkflow,
    })),
  {
    ssr: false,
    loading: () => (
      <div className='max-w-2xl mx-auto px-4 py-4 space-y-4'>
        <PodcastSearchSkeleton />
      </div>
    ),
  }
)

export function PodcastWorkflowWrapper() {
  return <PodcastWorkflow />
}
