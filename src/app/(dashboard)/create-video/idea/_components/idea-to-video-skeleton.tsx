import { Skeleton } from '@/components/ui/skeleton'

export function IdeaToVideoSkeleton() {
  return (
    <div className='space-y-4 animate-in fade-in-50 duration-300'>
      {/* Video Idea */}
      <div className='space-y-1'>
        <Skeleton className='h-4 w-32' />
        <Skeleton className='h-20 w-full rounded-md' />
        <Skeleton className='h-3 w-48' />
      </div>

      {/* Dropdowns Row */}
      <div className='grid grid-cols-1 sm:grid-cols-3 gap-3'>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className='space-y-1'>
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-9 w-full rounded-md' />
          </div>
        ))}
      </div>

      {/* Checkboxes */}
      <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
        {Array.from({ length: 2 }).map((_, index) => (
          <div key={index} className='flex items-center space-x-2'>
            <Skeleton className='h-4 w-4 rounded' />
            <div className='space-y-1'>
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-3 w-32' />
            </div>
          </div>
        ))}
      </div>

      {/* Keywords */}
      <div className='space-y-1'>
        <Skeleton className='h-4 w-32' />
        <Skeleton className='h-9 w-full rounded-md' />
      </div>

      <Skeleton className='h-px w-full my-3' />

      {/* Advanced Settings */}
      <div className='space-y-3'>
        <div className='flex items-center gap-2'>
          <Skeleton className='h-3 w-3' />
          <Skeleton className='h-4 w-32' />
        </div>

        {/* Duration and Voice */}
        <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
          <div className='space-y-2'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-2 w-full rounded-full' />
            <div className='flex justify-between'>
              <Skeleton className='h-3 w-6' />
              <Skeleton className='h-3 w-8' />
            </div>
          </div>
          <div className='space-y-1'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-9 w-full rounded-md' />
          </div>
        </div>

        {/* Language, Orientation, Autopick */}
        <div className='grid grid-cols-1 sm:grid-cols-3 gap-3'>
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className='space-y-1'>
              <Skeleton className='h-4 w-16' />
              <Skeleton className='h-9 w-full rounded-md' />
            </div>
          ))}
        </div>
      </div>

      {/* Action Button */}
      <Skeleton className='h-10 w-full rounded-md mt-4' />
    </div>
  )
}
