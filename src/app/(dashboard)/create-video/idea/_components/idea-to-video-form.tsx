'use client'

import React, { useState, FormEvent, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { LoaderDialog } from '@/components/ui/loader-dialog'
import { Shuffle } from 'lucide-react'
import { toast } from '@/lib/toast'
import { useVideoStore } from '@/store/video-store'
import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import { useInngestRunStatus } from '@/lib/useInngestRunStatus'
import { useQueryClient } from '@tanstack/react-query'
import { useDefaultAutopickValue } from '@/hooks/use-gated-autopick-options'
import posthog from 'posthog-js'

import { IdeaToVideoSkeleton } from './idea-to-video-skeleton'

// Import reusable components
import {
  ToneSelector,
  AudienceSelector,
  PlatformSelector,
  IncludeOptions,
  KeywordsInput,
  AdvancedSettings,
  GatedActionButton,
  type IdeaVideoConfig,
} from '@/app/(dashboard)/_components/video-form'

// Use the reusable configuration type
type VideoConfig = IdeaVideoConfig

export function IdeaToVideoForm() {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const router = useRouter()
  const queryClient = useQueryClient()

  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const { defaultValue: defaultAutopick } = useDefaultAutopickValue()

  // Track video creation started when component mounts (only once)
  const hasTracked = React.useRef(false)
  useEffect(() => {
    if (!hasTracked.current) {
      posthog.capture('video_creation_started', {
        method: 'idea_to_video',
        source: 'create_video_page',
      })
      hasTracked.current = true
    }
  }, [])

  // Loading state tracking
  const [progressMessage, setProgressMessage] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)

  const [config, setConfig] = useState<VideoConfig>({
    idea: '',
    tone: 'friendly',
    audience: 'general',
    platform: 'youtube',
    duration: 60,
    includeHook: true,
    includeCTA: true,
    language: 'english',
    orientation: 'landscape',
    autopick: defaultAutopick,
    voice: null,
    creativity: 50,
    keywords: '',
  })

  const [eventId, setEventId] = useState<string | null>(null)
  const { status, output } = useInngestRunStatus(eventId)

  const updateConfig = (
    key: keyof VideoConfig,
    value: string | number | boolean | ElevenVoice | null
  ) => {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  const handleVoiceSelect = (voice: ElevenVoice) => {
    updateConfig('voice', voice)
    toast.success(`Voice "${voice.name}" selected`)
  }

  const simulateProgress = () => {
    setProgressMessage(
      '🤖 Analyzing your idea and generating script with AI...'
    )

    setTimeout(() => {
      setProgressMessage('🎬 Finding perfect visual assets and media...')
    }, 2000)

    setTimeout(() => {
      setProgressMessage('🎙️ Converting script to professional voiceovers...')
    }, 4000)

    setTimeout(() => {
      setProgressMessage('✨ Finalizing scenes and preparing your video...')
    }, 6000)

    setTimeout(() => {
      setProgressMessage('🎉 Almost done! Wrapping everything up...')
    }, 8000)
  }

  const handleGenerateScript = async (e?: FormEvent) => {
    e?.preventDefault()

    if (!config.idea.trim()) {
      toast.error('Please enter your video idea')
      return
    }

    if (!config.tone || !config.audience || !config.platform) {
      toast.error('Please select tone, audience, and platform')
      return
    }

    if (!config.voice) {
      toast.error('Please select a voice for the video')
      return
    }

    const userId = session?.user?.id
    if (!userId) {
      toast.error('You must be logged in to generate videos')
      return
    }

    // Track video creation flow started
    posthog.capture('video_creation_flow_started', {
      method: 'idea_to_video',
      source: 'generate_button',
    })

    setIsGenerating(true)
    simulateProgress()

    try {
      // Prepare automation request
      const automationRequest = {
        idea: config.idea,
        tone: config.tone,
        audience: config.audience,
        platform: config.platform,
        hook: config.includeHook,
        callToAction: config.includeCTA,
        keywords: config.keywords || undefined,
        duration: config.duration,
        language: config.language,
        orientation: config.orientation,
        autopick: config.autopick,
        voice: config.voice,
        userId: userId,
        organizationId: session?.session?.activeOrganizationId || undefined,
        method: 'Idea to Video',
      }

      // Call the new inngest API route
      const response = await fetch('/api/generate-video-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(automationRequest),
      })
      if (!response.ok) throw new Error('Failed to generate video')
      const result = await response.json()
      setEventId(result.eventId)
    } catch (error) {
      console.error('Video generation error:', error)
      setProgressMessage('')
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to generate video. Please try again.'
      )
      setIsGenerating(false)
    }
  }

  // Simulate initial loading for better UX
  useEffect(() => {
    // Show skeleton for a short time to prevent flash of content
    const timer = setTimeout(() => {
      setIsInitialLoading(false)
    }, 100) // Just enough time to prevent layout shift

    return () => clearTimeout(timer)
  }, [])

  // Watch for polling completion
  useEffect(() => {
    if (
      status === 'Completed' &&
      output &&
      typeof output === 'object' &&
      output !== null &&
      'projectId' in output &&
      typeof output.projectId === 'string'
    ) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const project = output as any // API response will be transformed by setProjectData
      setProgressMessage('🎉 Video generation complete! Redirecting...')
      useVideoStore.getState().setProjectData(project)
      // Invalidate projects cache when a new project is created via Inngest
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Video created successfully!')
      setTimeout(() => {
        router.push(`/scene-editor?projectId=${project.projectId}`)
      }, 1500)
      setIsGenerating(false)
    } else if (status === 'Failed' || status === 'Cancelled') {
      setProgressMessage('')
      toast.error('Video generation failed. Please try again.')
      setIsGenerating(false)
    }
  }, [status, output, queryClient, router])

  // Add a list of random ideas
  const randomIdeas = [
    'The best beach for beginner surfers in Europe',
    'How to make healthy smoothies at home',
    'Tips for remote work productivity',
    'Beginner guide to meditation',
    'Top 5 travel destinations for 2025',
    'How to start a podcast from scratch',
    'Creative ways to recycle at home',
    'A day in the life of a software engineer',
    'How to build your personal brand online',
    'The science behind good sleep habits',
  ]

  const setRandomIdea = () => {
    const idx = Math.floor(Math.random() * randomIdeas.length)
    updateConfig('idea', randomIdeas[idx])
  }

  // Show skeleton during initial loading
  if (isInitialLoading) {
    return <IdeaToVideoSkeleton />
  }

  return (
    <>
      <div className='space-y-4 animate-in fade-in-50 duration-300'>
        {/* Video Idea */}
        <div className='space-y-1 relative'>
          <Label htmlFor='idea' className='text-sm'>
            Describe your video idea
          </Label>
          <Textarea
            id='idea'
            placeholder='e.g. The best beach for beginner surfers in Europe'
            value={config.idea}
            onChange={e => updateConfig('idea', e.target.value)}
            className='min-h-[80px] resize-none text-sm pr-28'
            disabled={isGenerating}
          />
          {/* Random idea button in bottom right */}
          <Button
            type='button'
            variant='outline'
            size='sm'
            className='absolute bottom-2 right-2 flex items-center gap-2 text-muted-foreground border-muted-foreground/30 shadow-none px-3 py-1.5 h-6 text-xs'
            onClick={setRandomIdea}
            disabled={isGenerating}
            tabIndex={-1}
          >
            <Shuffle className='w-4 h-4 mr-1' />
            Random idea
          </Button>
          <p className='text-xs text-muted-foreground'>
            Be specific and detailed for better results
          </p>
        </div>

        {/* Dropdowns Row */}
        <div className='flex gap-x-4 gap-y-2 flex-wrap'>
          <ToneSelector
            value={config.tone}
            onValueChange={value => updateConfig('tone', value)}
            disabled={isGenerating}
          />

          <AudienceSelector
            value={config.audience}
            onValueChange={value => updateConfig('audience', value)}
            disabled={isGenerating}
          />

          <PlatformSelector
            value={config.platform}
            onValueChange={value => updateConfig('platform', value)}
            disabled={isGenerating}
          />
        </div>

        {/* Include Options */}
        <IncludeOptions
          includeHook={config.includeHook}
          includeCTA={config.includeCTA}
          onIncludeHookChange={checked => updateConfig('includeHook', checked)}
          onIncludeCTAChange={checked => updateConfig('includeCTA', checked)}
          disabled={isGenerating}
        />

        {/* Keywords */}
        <KeywordsInput
          value={config.keywords}
          onChange={value => updateConfig('keywords', value)}
          placeholder='e.g. surfing, beach, Europe, beginner'
          disabled={isGenerating}
        />

        {/* Advanced Settings */}
        <AdvancedSettings
          duration={config.duration}
          onDurationChange={value => updateConfig('duration', value)}
          voice={config.voice}
          onVoiceSelect={handleVoiceSelect}
          language={config.language}
          onLanguageChange={value => updateConfig('language', value)}
          orientation={config.orientation}
          onOrientationChange={value => updateConfig('orientation', value)}
          autopick={config.autopick}
          onAutopickChange={value => updateConfig('autopick', value)}
          disabled={isGenerating}
        />
      </div>

      {/* Action Button */}
      <GatedActionButton
        onClick={handleGenerateScript}
        disabled={!config.idea.trim() || !config.voice}
        isGenerating={isGenerating}
        progressMessage={progressMessage}
        actionText='Generate Video'
        loadingText='Creating Video...'
      />

      {/* Loader Dialog */}
      <LoaderDialog
        open={isGenerating || status === 'Running'}
        title='AI agent is cooking your video'
        subtitle={
          status === 'Running'
            ? 'Generating your video. This may take a few minutes...'
            : progressMessage || 'This may take a few minutes...'
        }
      />
    </>
  )
}
