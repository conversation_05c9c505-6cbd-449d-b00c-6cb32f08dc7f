import type { Metadata } from 'next'
import { MyVideosContent } from './_components'

// Enable static generation with ISR - revalidate every hour
export const revalidate = 3600
export const dynamic = 'force-static'

// Generate metadata for SEO optimization
export const metadata: Metadata = {
  title: 'My Videos - Video Library | Adori AI',
  description:
    'View and manage all your AI-generated videos. Download, publish to YouTube, and organize your video content library.',
  keywords: [
    'video library',
    'my videos',
    'video management',
    'AI videos',
    'video downloads',
    'YouTube publishing',
  ],
  openGraph: {
    title: 'My Videos - Video Library | Adori AI',
    description: 'View and manage all your AI-generated videos.',
    type: 'website',
  },
}

export default function MyVideosPage() {
  return (
    <div className='min-h-screen bg-background'>
      {/* Client component with progressive loading */}
      <MyVideosContent />
    </div>
  )
}
