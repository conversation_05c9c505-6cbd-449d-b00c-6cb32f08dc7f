'use client'
import { useEffect, useState } from 'react'
import { VideoCard, VideoGridSkeleton } from '.'
import { StockPagination } from '@/components/stock-pagination'
import { usePaginatedRenderJobs } from '@/hooks/useRenderJobs'
import { useYouTubeStatus } from '@/hooks/useYouTubeStatus'

export function MyVideosContent() {
  const [page, setPage] = useState(1)
  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const limit = 8

  const {
    jobs: paginatedJobs,
    total,
    isLoading,
    refreshJobs,
  } = usePaginatedRenderJobs(page, limit)

  // Preload YouTube connection data for the publishing modal using centralized hook
  useYouTubeStatus()

  const [jobs, setJobs] = useState(paginatedJobs)

  useEffect(() => {
    setJobs(paginatedJobs)
    console.log('🎬 MyVideosContent: Jobs updated:', {
      count: paginatedJobs.length,
      jobs: paginatedJobs.map(job => ({
        id: job.id,
        status: job.status,
        progress: job.progress,
      })),
    })
  }, [paginatedJobs]) // Jobs will update automatically through polling

  // Show loading state until we have data or confirmed no data
  useEffect(() => {
    // Only stop showing initial loading when we have data OR we've confirmed there's no data
    if (paginatedJobs.length > 0 || (!isLoading && total === 0)) {
      setIsInitialLoading(false)
    }
  }, [paginatedJobs.length, isLoading, total])

  // Pagination helpers
  const totalPages = Math.ceil(total / limit)
  const startIdx = total === 0 ? 0 : (page - 1) * limit + 1
  const endIdx = Math.min(page * limit, total)

  // Show skeleton during initial loading
  if (isInitialLoading) {
    return (
      <div className='container mx-auto p-6'>
        <div className='flex items-center justify-between mb-6'>
          <h1 className='text-3xl font-bold'>My Videos</h1>
          <div className='text-muted-foreground text-sm'>
            {/* Empty during loading */}
          </div>
        </div>
        <VideoGridSkeleton count={limit} />
      </div>
    )
  }

  return (
    <div className='container mx-auto p-6'>
      <div className='flex items-center justify-between mb-6'>
        <h1 className='text-3xl font-bold'>My Videos</h1>
        <div className='text-muted-foreground text-sm'>
          {isLoading || isInitialLoading || !total
            ? ''
            : `${startIdx}-${endIdx} of ${total} videos`}
        </div>
      </div>

      {isLoading || isInitialLoading ? (
        <VideoGridSkeleton count={limit} />
      ) : jobs.length === 0 ? (
        <div className='bg-card p-8 rounded-lg text-center'>
          <p className='text-muted-foreground'>
            You don&apos;t have any videos yet.
          </p>
        </div>
      ) : (
        <div className='grid grid-cols-1 min-[768px]:grid-cols-2 min-[1200px]:grid-cols-3 min-[1400px]:grid-cols-4 gap-6'>
          {jobs.map(job => (
            <VideoCard
              key={`${job.id}-${job.status}-${job.progress}`}
              thumbnail={job.thumbnailUrl || null}
              created_at={job.createdAt}
              url={job.publicUrl || null}
              youtubeId={job.youtubeId || null}
              status={job.status}
              progress={job.progress}
              errorMessage={job.errorMessage || undefined}
              exportName={job.exportName || undefined}
              exportResolution={job.exportResolution || undefined}
              renderJobId={job.id}
              onDeleted={() =>
                setJobs(jobs => jobs.filter(j => j.id !== job.id))
              }
              onYouTubePublished={youtubeId => {
                // Update the job with the YouTube ID immediately for instant feedback
                setJobs(jobs =>
                  jobs.map(j => (j.id === job.id ? { ...j, youtubeId } : j))
                )
                // Also refresh the data to ensure consistency with database
                setTimeout(() => refreshJobs(), 1000)
              }}
            />
          ))}
        </div>
      )}

      {/* Shadcn UI Pagination */}
      {totalPages > 1 && (
        <div className='mt-8'>
          <StockPagination
            currentPage={page}
            totalPages={totalPages}
            onPageChange={setPage}
            className='justify-center'
          />
        </div>
      )}
    </div>
  )
}
