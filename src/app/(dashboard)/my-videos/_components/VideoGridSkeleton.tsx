import { Skeleton } from '@/components/ui/skeleton'
import { Card } from '@/components/ui/card'

interface VideoGridSkeletonProps {
  count?: number
}

export function VideoGridSkeleton({ count = 8 }: VideoGridSkeletonProps) {
  return (
    <div className='grid grid-cols-1 min-[768px]:grid-cols-2 min-[1200px]:grid-cols-3 min-[1400px]:grid-cols-4 gap-6'>
      {Array.from({ length: count }).map((_, index) => (
        <Card
          key={index}
          className='relative group overflow-hidden bg-gradient-to-br from-background to-muted/20 border-2 border-border/50 hover:border-border hover:shadow-lg transition-all duration-300 backdrop-blur-sm p-0'
        >
          {/* Video preview area with overlays - True edge-to-edge design */}
          <div className='relative aspect-video bg-gradient-to-br from-muted/30 to-muted/50 overflow-hidden'>
            {/* Resolution badge skeleton - Top left */}
            <div className='absolute top-3 left-3 z-30'>
              <Skeleton className='h-6 w-12 rounded-md' />
            </div>

            {/* Delete button skeleton - Top right */}
            <div className='absolute top-3 right-3 z-30'>
              <Skeleton className='h-8 w-8 rounded-md' />
            </div>

            {/* Video area skeleton */}
            <Skeleton className='w-full h-full' />

            {/* Play button overlay skeleton */}
            <div className='absolute inset-0 flex items-center justify-center'>
              <Skeleton className='h-12 w-12 rounded-full' />
            </div>
          </div>

          {/* Compact card content */}
          <div className='p-4 space-y-3'>
            {/* Video info section */}
            <div className='space-y-1.5'>
              {/* Time text */}
              <Skeleton className='h-3 w-32' />

              {/* Export name */}
              <Skeleton className='h-4 w-24' />
            </div>

            {/* Action buttons */}
            <div className='grid grid-cols-1 gap-2.5'>
              <Skeleton className='h-8 w-full rounded-md' />
              <Skeleton className='h-8 w-full rounded-md' />
            </div>
          </div>
        </Card>
      ))}
    </div>
  )
}
