import { VideoGridSkeleton } from './_components'

export default function MyVideosLoading() {
  return (
    <div className='min-h-screen bg-background'>
      {/* Header Section - Static, renders immediately */}
      <div className='text-center space-y-2 py-4'>
        <div className='inline-flex items-center gap-2 px-3 py-1 bg-blue-500/10 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium'>
          Video Library
        </div>
        <p className='text-muted-foreground text-sm max-w-2xl mx-auto'>
          View, download, and manage all your AI-generated videos
        </p>
      </div>

      {/* Loading content */}
      <div className='container mx-auto p-6'>
        <div className='flex items-center justify-between mb-6'>
          <h1 className='text-3xl font-bold'>My Videos</h1>
          <div className='text-muted-foreground text-sm'>
            {/* Empty during loading */}
          </div>
        </div>
        <VideoGridSkeleton count={8} />
      </div>
    </div>
  )
}
