'use client'

import { usePathname, useRouter } from 'next/navigation'
import { Settings, Youtube, Key, Building2 } from 'lucide-react'

// Custom tab button component matching media picker modal style
const TabButton = ({
  active,
  onClick,
  icon,
  label,
}: {
  active: boolean
  onClick: () => void
  icon: React.ReactNode
  label: string
}) => (
  <button
    className={`py-2 sm:py-3 px-2 sm:px-4 mr-1 sm:mr-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1 whitespace-nowrap transition-colors ${
      active
        ? 'text-primary border-b-2 border-primary'
        : 'text-muted-foreground hover:text-foreground hover:bg-muted/40'
    }`}
    onClick={onClick}
  >
    {icon}
    <span className='hidden sm:inline'>{label}</span>
    <span className='sm:hidden'>{label.split(' ')[0]}</span>
  </button>
)

export default function SettingsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const router = useRouter()

  const tabs = [
    {
      id: 'general',
      label: 'General',
      icon: <Settings className='h-4 w-4' />,
      href: '/settings/general',
    },
    {
      id: 'youtube',
      label: 'YouTube',
      icon: <Youtube className='h-4 w-4' />,
      href: '/settings/youtube',
    },
    {
      id: 'api',
      label: 'API Keys',
      icon: <Key className='h-4 w-4' />,
      href: '/settings/api',
    },
    {
      id: 'workspace',
      label: 'Workspace',
      icon: <Building2 className='h-4 w-4' />,
      href: '/settings/workspace',
    },
  ]

  return (
    <div className='container mx-auto p-6'>
      <div className='space-y-6'>
        {/* Page Header */}
        <div>
          <h1 className='text-2xl font-bold'>Settings</h1>
          <p className='text-muted-foreground'>
            Manage your account settings and preferences
          </p>
        </div>

        {/* Tabbed Interface */}
        <div className='bg-card rounded-lg overflow-hidden'>
          {/* Tab Navigation */}
          <div className='border-b border-border flex pl-4'>
            <div className='flex'>
              {tabs.map(tab => (
                <TabButton
                  key={tab.id}
                  active={pathname === tab.href}
                  onClick={() => router.push(tab.href)}
                  icon={tab.icon}
                  label={tab.label}
                />
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className='p-6'>{children}</div>
        </div>
      </div>
    </div>
  )
}
