'use client'

import { ThemeToggle } from '@/components/theme-toggle'
import { AccountSettings } from '../_components/account-settings'

export default function GeneralSettingsPage() {
  return (
    <div className='space-y-6'>
      {/* Theme Settings */}
      <div>
        <h2 className='text-xl font-semibold mb-4'>Appearance</h2>
        <div className='flex items-center justify-between'>
          <div>
            <p className='font-medium'>Theme</p>
            <p className='text-sm text-muted-foreground'>
              Customize the appearance of the application
            </p>
          </div>
          <ThemeToggle />
        </div>
      </div>

      {/* Account Settings */}
      <div>
        <h2 className='text-xl font-semibold mb-4'>Account</h2>
        <p className='text-sm text-muted-foreground mb-4'>
          Manage your account settings and preferences
        </p>
        <AccountSettings />
      </div>
    </div>
  )
}
