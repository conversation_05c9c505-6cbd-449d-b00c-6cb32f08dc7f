'use client'

import { useState } from 'react'
import { authClient } from '@/lib/auth-client'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Loader2, UserX, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'

interface RemoveMemberModalProps {
  isOpen: boolean
  onClose: () => void
  member: {
    id: string
    user: {
      name: string
      email: string
    }
    role: string
  }
  organizationId: string
  onMemberRemoved: () => void
}

export function RemoveMemberModal({
  isOpen,
  onClose,
  member,
  organizationId,
  onMemberRemoved,
}: RemoveMemberModalProps) {
  const [confirmEmail, setConfirmEmail] = useState('')
  const [loading, setLoading] = useState(false)

  const handleRemoveMember = async () => {
    if (confirmEmail !== member.user.email) {
      toast.error('Email confirmation does not match')
      return
    }

    setLoading(true)
    try {
      // Use the client-side API like the rest of the codebase
      await authClient.organization.removeMember({
        memberIdOrEmail: member.user.email,
        organizationId,
      })

      toast.success('Member removed successfully')
      onMemberRemoved()
      onClose()
      setConfirmEmail('')
    } catch (error) {
      console.error('Failed to remove member:', error)
      toast.error('Failed to remove member')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2 text-destructive'>
            <UserX className='h-5 w-5' />
            Remove Member
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. The member will lose access to this
            workspace.
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          <div className='flex items-center gap-3 p-3 bg-destructive/10 border border-destructive/20 rounded-lg'>
            <AlertTriangle className='h-5 w-5 text-destructive' />
            <div>
              <p className='font-medium text-destructive'>
                {member.user.name || member.user.email}
              </p>
              <p className='text-sm text-muted-foreground'>
                Role: {member.role}
              </p>
            </div>
          </div>

          <div>
            <label className='text-sm font-medium'>
              Type <span className='font-mono'>{member.user.email}</span> to
              confirm
            </label>
            <Input
              value={confirmEmail}
              onChange={e => setConfirmEmail(e.target.value)}
              placeholder='Enter email to confirm'
              className='mt-1'
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            variant='destructive'
            onClick={handleRemoveMember}
            disabled={loading || confirmEmail !== member.user.email}
          >
            {loading ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin mr-2' />
                Removing...
              </>
            ) : (
              'Remove Member'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
