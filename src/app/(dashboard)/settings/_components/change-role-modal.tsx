'use client'

import { useState } from 'react'
import { authClient } from '@/lib/auth-client'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Loader2, User } from 'lucide-react'
import { toast } from 'sonner'
import { Label } from '@/components/ui/label'

interface ChangeRoleModalProps {
  isOpen: boolean
  onClose: () => void
  member: {
    id: string
    user: {
      name: string
      email: string
    }
    role: string
  }
  organizationId: string
  onRoleChange: () => void
}

const roleOptions = [
  {
    value: 'member',
    label: 'Member',
    description: 'Can view and edit projects',
  },
  // {
  //   value: 'admin',
  //   label: 'Admin',
  //   description: 'Can manage team and settings',
  // },
  { value: 'owner', label: 'Owner', description: 'Full access to everything' },
]

export function ChangeRoleModal({
  isOpen,
  onClose,
  member,
  organizationId,
  onRoleChange,
}: ChangeRoleModalProps) {
  const [selectedRole, setSelectedRole] = useState(member.role)
  const [loading, setLoading] = useState(false)

  const handleRoleChange = async () => {
    if (selectedRole === member.role) {
      onClose()
      return
    }

    setLoading(true)
    try {
      // Use the client-side API like the rest of the codebase
      await authClient.organization.updateMemberRole({
        memberId: member.id,
        role: selectedRole as 'member' | 'admin' | 'owner',
        organizationId,
      })

      toast.success(`Role updated to ${selectedRole}`)
      onRoleChange()
      onClose()
    } catch (error) {
      console.error('Failed to update role:', error)
      toast.error('Failed to update role')
    } finally {
      setLoading(false)
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'owner':
        return <Badge className='bg-green-100 text-green-800'>Owner</Badge>
      // case 'admin':
      //   return <Badge className='bg-blue-100 text-blue-800'>Admin</Badge>
      case 'member':
        return <Badge className='bg-gray-100 text-gray-800'>Member</Badge>
      default:
        return <Badge variant='outline'>{role}</Badge>
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <User className='h-5 w-5' />
            Change Member Role
          </DialogTitle>
          <DialogDescription>
            Update the role for{' '}
            <span className='font-medium'>
              {member.user.name || member.user.email}
            </span>
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          <div className='flex flex-col gap-2'>
            <label className='text-sm font-medium'>Current Role</label>
            <div className='mt-1'>{getRoleBadge(member.role)}</div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='role'>New Role</Label>
            <Select
              value={selectedRole}
              onValueChange={setSelectedRole}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder='Select role' />
              </SelectTrigger>
              <SelectContent>
                {roleOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className='text-xs text-muted-foreground'>
              Members can view and edit content. Owners have full control.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleRoleChange}
            disabled={loading || selectedRole === member.role}
          >
            {loading ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin mr-2' />
                Updating...
              </>
            ) : (
              'Update Role'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
