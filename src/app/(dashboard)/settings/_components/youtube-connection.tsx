'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Youtube,
  Loader2,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Shield,
  Eye,
  Upload,
  Play,
  Users,
  Settings,
  Zap,
  Image,
  Clock,
} from 'lucide-react'
import { toast } from 'sonner'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useYouTubeStatus } from '@/hooks/useYouTubeStatus'

interface ChannelInfo {
  id: string
  title: string
  thumbnailUrl?: string | null
  description?: string | null
  isVerified?: boolean
  canUploadCustomThumbnails?: boolean
  subscriberCount?: string
  videoCount?: string
  longUploadsStatus?: string
  customUrl?: string | null
}

interface YouTubeConnection {
  id: string
  channelInfo: ChannelInfo
  connectedAt: string
  scopes: string[]
  isExpired?: boolean
  expiresAt?: string
}

// Skeleton loader that matches the disconnected state structure
function YouTubeDisconnectSkeleton() {
  return (
    <div className='space-y-4'>
      {/* Header */}
      <div>
        <h2 className='text-xl font-semibold mb-2'>YouTube Integration</h2>
        <p className='text-sm text-muted-foreground'>
          Connect your YouTube account to enable video uploads directly from
          Adori AI
        </p>
      </div>

      {/* Compact Hero Skeleton */}
      <div className='text-center p-4 border rounded-lg bg-card'>
        <div className='mx-auto p-2 rounded-full bg-muted/50 w-fit mb-3 animate-pulse'>
          <div className='h-6 w-6 bg-muted rounded' />
        </div>
        <div className='h-5 w-40 bg-muted rounded mx-auto mb-1 animate-pulse' />
        <div className='h-4 w-64 bg-muted rounded mx-auto mb-4 animate-pulse' />

        <div className='h-10 w-44 bg-muted rounded mx-auto mb-2 animate-pulse' />
        <div className='h-3 w-48 bg-muted rounded mx-auto animate-pulse' />
      </div>

      {/* Compact Benefits Skeleton */}
      <div className='p-3 border rounded-lg bg-card'>
        <div className='h-4 w-28 bg-muted rounded mb-2 animate-pulse' />
        <div className='grid sm:grid-cols-2 gap-2'>
          {Array.from({ length: 4 }).map((_, i) => (
            <div
              key={i}
              className='flex items-start space-x-2 p-2 rounded border border-border/30'
            >
              <div className='flex-shrink-0 p-1 rounded bg-muted/50 animate-pulse'>
                <div className='h-3 w-3 bg-muted rounded' />
              </div>
              <div className='flex-1 min-w-0'>
                <div className='h-3 w-20 bg-muted rounded mb-1 animate-pulse' />
                <div className='h-3 w-full bg-muted rounded animate-pulse' />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Compact Legal Skeleton */}
      <div className='text-center p-2 border rounded bg-muted/20'>
        <div className='h-3 w-80 bg-muted rounded mx-auto mb-2 animate-pulse' />
        <div className='flex justify-center gap-3'>
          <div className='h-3 w-24 bg-muted rounded animate-pulse' />
          <div className='h-3 w-24 bg-muted rounded animate-pulse' />
        </div>
      </div>
    </div>
  )
}

// Enhanced Google Sign-In Button Component
function GoogleSignInButton({
  onClick,
  disabled,
  isLoading,
}: {
  onClick: () => void
  disabled: boolean
  isLoading: boolean
}) {
  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      className='group bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 hover:border-gray-400 hover:shadow-sm transition-all duration-300 h-10 px-4'
    >
      <div className='flex items-center justify-center'>
        {isLoading ? (
          <Loader2 className='w-4 h-4 animate-spin mr-2' />
        ) : (
          <svg className='w-4 h-4 mr-2' viewBox='0 0 24 24'>
            <path
              fill='#4285F4'
              d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'
            />
            <path
              fill='#34A853'
              d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'
            />
            <path
              fill='#FBBC05'
              d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'
            />
            <path
              fill='#EA4335'
              d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'
            />
          </svg>
        )}
        <span className='text-sm font-medium'>
          {isLoading ? 'Connecting...' : 'Connect with Google'}
        </span>
      </div>
    </Button>
  )
}

// Compact Feature Item Component
function FeatureItem({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode
  title: string
  description: string
}) {
  return (
    <div className='flex items-start space-x-3 p-3 rounded-lg border border-border/30 hover:border-border/60 hover:bg-muted/20 hover:shadow-sm transition-all duration-200 group'>
      <div className='flex-shrink-0 p-1.5 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 text-primary group-hover:scale-110 transition-transform duration-200'>
        {icon}
      </div>
      <div className='flex-1 min-w-0'>
        <h4 className='font-medium text-xs mb-1 text-foreground'>{title}</h4>
        <p className='text-xs text-muted-foreground leading-tight'>
          {description}
        </p>
      </div>
    </div>
  )
}

// Compact Channel Item Component
function ChannelItem({
  connection,
  onDisconnect,
  isDisconnecting,
}: {
  connection: YouTubeConnection
  onDisconnect: (id: string) => void
  isDisconnecting: boolean
}) {
  return (
    <div className='flex items-center justify-between p-4 rounded-xl border border-border/50 hover:border-border/80 hover:shadow-lg transition-all duration-300 hover:scale-[1.02] bg-card/50 backdrop-blur-sm hover:bg-card/80'>
      <div className='flex items-center space-x-3 flex-1 min-w-0'>
        <Avatar className='h-10 w-10 ring-2 ring-border/20 shadow-sm'>
          <AvatarImage
            src={connection.channelInfo.thumbnailUrl || undefined}
            alt={connection.channelInfo.title}
            className='object-cover'
          />
          <AvatarFallback className='bg-gradient-to-br from-primary/10 to-primary/20 text-sm'>
            <Youtube className='h-5 w-5 text-primary' />
          </AvatarFallback>
        </Avatar>
        <div className='flex-1 min-w-0'>
          <div className='flex items-center gap-2 mb-1'>
            <h4 className='font-medium text-sm truncate'>
              {connection.channelInfo.title}
            </h4>
            <Badge variant='secondary' className='text-xs h-4 px-1'>
              <CheckCircle className='h-2 w-2 mr-1' />
              Connected
            </Badge>
          </div>

          {/* Channel handle/username */}
          {connection.channelInfo.customUrl && (
            <p className='text-xs text-muted-foreground mb-1'>
              {connection.channelInfo.customUrl}
            </p>
          )}

          {/* Channel capabilities */}
          <div className='flex items-center gap-1 mb-1 flex-wrap'>
            {/* Verification status based on long uploads capability */}
            {connection.channelInfo.longUploadsStatus === 'allowed' ? (
              <Badge
                variant='default'
                className='text-xs h-4 px-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              >
                <CheckCircle className='h-2 w-2 mr-1' />
                Verified
              </Badge>
            ) : (
              <Badge
                variant='outline'
                className='text-xs h-4 px-1 border-yellow-300 text-yellow-700 dark:border-yellow-600 dark:text-yellow-400'
              >
                <AlertCircle className='h-2 w-2 mr-1' />
                Unverified
              </Badge>
            )}

            {/* Long uploads capability */}
            {connection.channelInfo.longUploadsStatus === 'allowed' ? (
              <Badge
                variant='default'
                className='text-xs h-4 px-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              >
                <Clock className='h-2 w-2 mr-1' />
                Long uploads
              </Badge>
            ) : (
              <Badge
                variant='outline'
                className='text-xs h-4 px-1 border-orange-300 text-orange-700 dark:border-orange-600 dark:text-orange-400'
              >
                <Clock className='h-2 w-2 mr-1' />
                15min limit
              </Badge>
            )}

            {/* Thumbnail uploads capability */}
            {connection.channelInfo.longUploadsStatus === 'allowed' ? (
              <Badge
                variant='default'
                className='text-xs h-4 px-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
              >
                <Image className='h-2 w-2 mr-1' />
                Thumbnail uploads
              </Badge>
            ) : (
              <Badge
                variant='outline'
                className='text-xs h-4 px-1 border-gray-300 text-gray-600 dark:border-gray-600 dark:text-gray-400'
              >
                <Image className='h-2 w-2 mr-1' />
                Thumbnail disabled
              </Badge>
            )}
          </div>

          <p className='text-xs text-muted-foreground flex items-center gap-1'>
            <Settings className='h-2 w-2' />
            Connected {new Date(connection.connectedAt).toLocaleDateString()}
          </p>
        </div>
      </div>
      <Button
        variant='outline'
        size='sm'
        onClick={() => onDisconnect(connection.id)}
        disabled={isDisconnecting}
        className='ml-3 h-7 px-2 text-xs hover:bg-destructive/10 hover:text-destructive hover:border-destructive/50 transition-colors duration-300'
      >
        {isDisconnecting ? (
          <Loader2 className='h-3 w-3 animate-spin' />
        ) : (
          'Disconnect'
        )}
      </Button>
    </div>
  )
}

export function YouTubeConnection() {
  const [isConnecting, setIsConnecting] = useState(false)
  const [isDisconnecting, setIsDisconnecting] = useState(false)
  const queryClient = useQueryClient()

  // Use the centralized YouTube status hook
  const { connections, isConnected, isLoading, isFetching } = useYouTubeStatus()

  // Convert to the expected format for backward compatibility
  const status = {
    connected: isConnected,
    connections,
    error: undefined, // Add error property for type compatibility
  }

  // Mutation to disconnect YouTube (can disconnect specific connection or all)
  const disconnectMutation = useMutation({
    mutationFn: async (connectionId?: string) => {
      // Set disconnecting state at the start
      setIsDisconnecting(true)

      try {
        // Step 1: Call disconnect API
        const response = await fetch('/api/youtube/auth/disconnect', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ connectionId }),
        })
        if (!response.ok) {
          throw new Error('Failed to disconnect YouTube account')
        }

        const result = await response.json()

        // Step 2: Refresh status to get updated state
        await queryClient.invalidateQueries({
          queryKey: ['youtube-connections'],
        })

        return result
      } finally {
        // Always clear disconnecting state
        setIsDisconnecting(false)
      }
    },
    onSuccess: data => {
      toast.success(data.message || 'YouTube account disconnected successfully')
    },
    onError: error => {
      console.error('Disconnect error:', error)
      toast.error('Failed to disconnect YouTube account')
      setIsDisconnecting(false)
    },
  })

  // Handle OAuth popup flow
  const handleConnect = async () => {
    setIsConnecting(true)

    // Open popup immediately with static loading page to prevent blocking
    const popup = window.open(
      '/auth/youtube/loading.html',
      'youtube-auth',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    )

    if (!popup) {
      setIsConnecting(false)
      toast.error('Popup blocked. Please allow popups for this site.')
      return
    }

    try {
      // Listen for messages from popup
      const handleMessage = (event: MessageEvent) => {
        // Allow messages from the same origin
        if (event.origin !== window.location.origin) {
          return
        }

        if (event.data.type === 'YOUTUBE_AUTH_SUCCESS') {
          toast.success('YouTube account connected successfully!')
          // Invalidate the correct query key used by useYouTubeStatus hook
          queryClient.invalidateQueries({
            queryKey: ['youtube-connections'],
          })
          setIsConnecting(false)
          window.removeEventListener('message', handleMessage)
          popup.close()
        } else if (event.data.type === 'YOUTUBE_AUTH_ERROR') {
          toast.error(`Connection failed: ${event.data.error}`)
          // Also refresh status on error to ensure UI is up-to-date
          queryClient.invalidateQueries({
            queryKey: ['youtube-connections'],
          })
          setIsConnecting(false)
          window.removeEventListener('message', handleMessage)
          popup.close()
        } else if (event.data.type === 'YOUTUBE_LOADING_READY') {
          // Now make the API call to get the OAuth URL
          initiateOAuth(popup)
        }
      }

      window.addEventListener('message', handleMessage)

      // Check if popup was closed manually
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed)
          setIsConnecting(false)
          window.removeEventListener('message', handleMessage)
          // Refresh YouTube status when popup closes to ensure UI is up-to-date
          queryClient.invalidateQueries({
            queryKey: ['youtube-connections'],
          })
        }
      }, 1000)

      // Function to initiate OAuth after loading page is ready
      const initiateOAuth = async (popupWindow: Window) => {
        try {
          // Get authorization URL
          const response = await fetch('/api/youtube/auth/initiate')
          if (!response.ok) {
            throw new Error('Failed to initiate YouTube connection')
          }

          const { authUrl } = await response.json()

          // Navigate popup to OAuth URL
          popupWindow.postMessage(
            {
              type: 'NAVIGATE_TO_OAUTH',
              url: authUrl,
            },
            window.location.origin
          )
        } catch (error) {
          console.error('OAuth initiation error:', error)
          toast.error('Failed to initiate YouTube connection')
          setIsConnecting(false)
          popupWindow.close()
          window.removeEventListener('message', handleMessage)
          // Refresh status on OAuth initiation error
          queryClient.invalidateQueries({
            queryKey: ['youtube-connections'],
          })
        }
      }

      // Timeout after 5 minutes
      setTimeout(
        () => {
          if (!popup.closed) {
            popup.close()
            setIsConnecting(false)
            window.removeEventListener('message', handleMessage)
            // Refresh status on timeout to ensure UI is up-to-date
            queryClient.invalidateQueries({
              queryKey: ['youtube-connections'],
            })
            toast.error('Connection timeout. Please try again.')
          }
        },
        5 * 60 * 1000
      )
    } catch (error) {
      console.error('Connection error:', error)
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to connect YouTube account'
      )
      setIsConnecting(false)
    }
  }

  const handleDisconnect = (connectionId?: string) => {
    disconnectMutation.mutate(connectionId)
  }

  // Show skeleton loader during disconnect process
  if (isDisconnecting) {
    return <YouTubeDisconnectSkeleton />
  }

  // Show regular loading state for initial load
  if (isLoading) {
    return (
      <div className='space-y-4'>
        <div>
          <h2 className='text-xl font-semibold mb-2'>YouTube Integration</h2>
          <p className='text-sm text-muted-foreground'>
            Connect your YouTube account to enable video uploads directly from
            Adori AI
          </p>
        </div>
        <div className='flex items-center justify-center py-6 border rounded-lg bg-muted/20'>
          <div className='flex items-center space-x-2'>
            <Loader2 className='h-4 w-4 animate-spin text-primary' />
            <span className='text-sm text-muted-foreground'>
              Loading connection status...
            </span>
          </div>
        </div>
      </div>
    )
  }

  // Show skeleton loader when refetching (after OAuth popup closes)
  if (isFetching && !isLoading) {
    return <YouTubeDisconnectSkeleton />
  }

  return (
    <div className='space-y-4'>
      {/* Header */}
      <div>
        <h2 className='text-xl font-semibold mb-2'>YouTube Integration</h2>
        <p className='text-sm text-muted-foreground'>
          Connect your YouTube account to enable video uploads directly from
          Adori AI
        </p>
      </div>

      {status?.connected && status.connections.length > 0 ? (
        /* Connected State */
        <div className='space-y-4'>
          {/* Compact Status Header */}
          <div className='flex items-center justify-between p-4 rounded-xl border-green-200/60 bg-gradient-to-r from-green-50/80 to-green-100/50 dark:border-green-800/60 dark:from-green-900/20 dark:to-green-800/10 border shadow-sm'>
            <div className='flex items-center space-x-2'>
              <CheckCircle className='h-4 w-4 text-green-600 dark:text-green-400' />
              <div>
                <h3 className='font-medium text-sm text-green-800 dark:text-green-400'>
                  {status.connections.length === 1
                    ? 'YouTube Connected'
                    : `${status.connections.length} YouTube Channels Connected`}
                </h3>
                <p className='text-xs text-green-700/80 dark:text-green-500/80'>
                  Ready to upload videos directly to YouTube
                </p>
              </div>
            </div>
            <Button
              variant='outline'
              size='sm'
              onClick={handleConnect}
              disabled={isConnecting}
              className='h-7 px-2 text-xs hover:bg-primary/10 hover:border-primary/50 hover:text-primary transition-colors duration-300'
            >
              {isConnecting ? (
                <Loader2 className='h-3 w-3 animate-spin mr-1' />
              ) : (
                <Youtube className='h-3 w-3 mr-1' />
              )}
              {isConnecting ? 'Connecting...' : 'Add Account'}
            </Button>
          </div>

          {/* Connected Channels - Compact Flex Layout */}
          <div className='space-y-3'>
            <h3 className='font-medium text-xs text-muted-foreground flex items-center gap-1'>
              <Users className='h-3 w-3' />
              Connected Channels ({status.connections.length})
            </h3>
            <div className='grid gap-2'>
              {status.connections.map(connection => (
                <ChannelItem
                  key={connection.id}
                  connection={connection}
                  onDisconnect={handleDisconnect}
                  isDisconnecting={isDisconnecting}
                />
              ))}
            </div>
          </div>

          {/* Compact Features */}
          <div className='p-4 rounded-xl border border-border/50 bg-gradient-to-br from-card/50 to-muted/20 shadow-sm'>
            <h4 className='font-medium text-xs flex items-center gap-1 mb-2 text-muted-foreground'>
              <Zap className='h-3 w-3' />
              Available Features
            </h4>
            <div className='grid sm:grid-cols-2 gap-2'>
              <FeatureItem
                icon={<Upload className='h-3 w-3' />}
                title='Direct Upload'
                description='Upload videos directly from scene editor'
              />
              <FeatureItem
                icon={<Play className='h-3 w-3' />}
                title='Auto Publishing'
                description='Schedule or publish immediately'
              />
            </div>
          </div>

          {/* Compact Legal Footer */}
          <div className='flex items-center justify-between text-xs text-muted-foreground p-3 border border-border/30 rounded-lg bg-muted/10 backdrop-blur-sm'>
            <div className='flex items-center gap-1'>
              <Shield className='h-3 w-3' />
              <span>Secured by Google OAuth</span>
            </div>
            <div className='flex gap-3'>
              <a
                href='https://policies.google.com/privacy'
                target='_blank'
                rel='noopener noreferrer'
                className='text-primary hover:text-primary/80 flex items-center gap-1 transition-colors duration-300'
              >
                Privacy <ExternalLink className='h-2 w-2' />
              </a>
              <a
                href='https://myaccount.google.com/permissions'
                target='_blank'
                rel='noopener noreferrer'
                className='text-primary hover:text-primary/80 flex items-center gap-1 transition-colors duration-300'
              >
                Permissions <ExternalLink className='h-2 w-2' />
              </a>
            </div>
          </div>

          {/* Compact Disconnect All (only show if multiple connections) */}
          {status.connections.length > 1 && (
            <div className='flex items-center justify-between p-2 border border-destructive/20 rounded bg-destructive/5'>
              <div>
                <p className='font-medium text-xs'>Disconnect All Accounts</p>
                <p className='text-xs text-muted-foreground'>
                  Remove all connections
                </p>
              </div>
              <Button
                variant='destructive'
                size='sm'
                onClick={() => handleDisconnect()}
                disabled={isDisconnecting}
                className='h-7 px-2 text-xs'
              >
                {isDisconnecting ? (
                  <>
                    <Loader2 className='mr-1 h-3 w-3 animate-spin' />
                    Disconnecting...
                  </>
                ) : (
                  'Disconnect All'
                )}
              </Button>
            </div>
          )}
        </div>
      ) : (
        /* Disconnected State - Compact */
        <div className='space-y-4'>
          {/* Error State */}
          {status?.error && (
            <div className='flex items-center space-x-2 p-3 border-yellow-200/50 bg-yellow-50/50 dark:border-yellow-800/50 dark:bg-yellow-900/10 border rounded-lg'>
              <AlertCircle className='h-4 w-4 text-yellow-600 dark:text-yellow-400' />
              <span className='text-sm text-yellow-800 dark:text-yellow-300'>
                {status.error}
              </span>
            </div>
          )}

          {/* Compact Hero */}
          <div className='text-center p-6 border border-border/50 rounded-xl bg-gradient-to-br from-card/80 to-muted/20 shadow-sm'>
            <div className='mx-auto p-2 rounded-full bg-primary/10 w-fit mb-3'>
              <Youtube className='h-6 w-6 text-primary' />
            </div>
            <h3 className='font-semibold mb-1'>Connect to YouTube</h3>
            <p className='text-sm text-muted-foreground mb-4'>
              Upload AI-generated videos directly to your channel
            </p>

            <GoogleSignInButton
              onClick={handleConnect}
              disabled={isConnecting}
              isLoading={isConnecting}
            />
            <p className='text-xs text-muted-foreground mt-2 flex items-center justify-center gap-1'>
              <Shield className='h-3 w-3' />
              Secured by Google OAuth 2.0
            </p>
          </div>

          {/* Compact Benefits */}
          <div className='p-4 border border-border/50 rounded-xl bg-gradient-to-br from-card/60 to-muted/30 shadow-sm'>
            <h4 className='font-medium text-sm mb-2 flex items-center gap-2'>
              <Zap className='h-4 w-4 text-primary' />
              What you&apos;ll get
            </h4>
            <div className='grid sm:grid-cols-2 gap-2'>
              <FeatureItem
                icon={<Zap className='h-3 w-3' />}
                title='Instant Publishing'
                description='Skip download-upload process'
              />
              <FeatureItem
                icon={<Settings className='h-3 w-3' />}
                title='Full Control'
                description='Set titles, descriptions, visibility'
              />
              <FeatureItem
                icon={<Shield className='h-3 w-3' />}
                title='Secure'
                description='Google OAuth protection'
              />
              <FeatureItem
                icon={<Eye className='h-3 w-3' />}
                title='Privacy Focused'
                description='Only upload access needed'
              />
            </div>
          </div>

          {/* Compact Legal */}
          <div className='text-center p-3 border border-border/30 rounded-lg bg-muted/10 backdrop-blur-sm'>
            <p className='text-xs text-muted-foreground mb-2'>
              By connecting, you agree to YouTube&apos;s terms and Google&apos;s
              privacy policy
            </p>
            <div className='flex justify-center gap-3 text-xs'>
              <a
                href='https://policies.google.com/privacy'
                target='_blank'
                rel='noopener noreferrer'
                className='text-primary hover:text-primary/80 flex items-center gap-1 transition-colors duration-300'
              >
                Google Privacy <ExternalLink className='h-2 w-2' />
              </a>
              <a
                href='https://www.youtube.com/t/terms'
                target='_blank'
                rel='noopener noreferrer'
                className='text-primary hover:text-primary/80 flex items-center gap-1 transition-colors duration-300'
              >
                YouTube Terms <ExternalLink className='h-2 w-2' />
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
