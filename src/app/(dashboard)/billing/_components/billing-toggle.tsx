import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface BillingToggleProps {
  activePeriod: 'monthly' | 'annual'
  onChange: (period: 'monthly' | 'annual') => void
}

const BillingToggle: React.FC<BillingToggleProps> = ({
  activePeriod,
  onChange,
}) => {
  const handleValueChange = (value: string) => {
    onChange(value as 'monthly' | 'annual')
  }

  return (
    <Tabs value={activePeriod} onValueChange={handleValueChange}>
      <TabsList>
        <TabsTrigger value='monthly'>Monthly</TabsTrigger>
        <TabsTrigger value='annual'>
          Yearly
          <Badge className='bg-primary'>20% off</Badge>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  )
}

export default BillingToggle
