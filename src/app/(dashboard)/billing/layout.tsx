'use client'
import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { CreditCard, Package, ChartColumn } from 'lucide-react'

// Custom tab button component matching settings page style
const TabButton = ({
  active,
  href,
  icon,
  label,
}: {
  active: boolean
  href: string
  icon: React.ReactNode
  label: string
}) => (
  <Link
    href={href}
    className={`py-2 sm:py-3 px-2 sm:px-4 mr-1 sm:mr-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1 whitespace-nowrap transition-colors ${
      active
        ? 'text-primary border-b-2 border-primary'
        : 'text-muted-foreground hover:text-foreground hover:bg-muted/40'
    }`}
  >
    {icon}
    <span className='hidden sm:inline'>{label}</span>
    <span className='sm:hidden'>{label.split(' ')[0]}</span>
  </Link>
)

export default function BillingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()

  return (
    <div className='container mx-auto p-6'>
      <div className='space-y-6'>
        {/* Page Header */}
        <div>
          <h1 className='text-2xl font-bold'>Billing</h1>
          <p className='text-muted-foreground'>
            Manage your subscription and billing preferences
          </p>
        </div>

        {/* Tabbed Interface */}
        <div className='bg-card rounded-lg overflow-hidden'>
          {/* Tab Navigation */}
          <div className='border-b border-border flex pl-4'>
            <div className='flex'>
              <TabButton
                active={pathname === '/billing/plans'}
                href='/billing/plans'
                icon={<Package className='h-4 w-4' />}
                label='Plans'
              />
              <TabButton
                active={pathname === '/billing/subscription'}
                href='/billing/subscription'
                icon={<CreditCard className='h-4 w-4' />}
                label='Subscription'
              />
              <TabButton
                active={pathname === '/billing/usage'}
                href='/billing/usage'
                icon={<ChartColumn className='h-4 w-4' />}
                label='Usage'
              />
            </div>
          </div>

          {/* Tab Content */}
          <div className='p-6'>{children}</div>
        </div>
      </div>
    </div>
  )
}
