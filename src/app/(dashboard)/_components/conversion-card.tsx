import React from 'react'
import Link from 'next/link'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
  CardDescription,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface ConversionCardProps {
  title: string
  description: string
  icon: React.ElementType
  actionText: string
  href: string
}

const ConversionCard = ({
  title,
  description,
  icon: Icon,
  actionText,
  href,
}: ConversionCardProps) => {
  return (
    <Card className='hover:shadow-md transition-all duration-300 group relative overflow-hidden'>
      <CardHeader className='flex flex-col items-center justify-center text-center'>
        <div className='flex items-center justify-center mb-4'>
          <div className='rounded-2xl bg-secondary text-primary flex items-center justify-center w-14 h-14 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105 shadow-sm'>
            <Icon className='h-7 w-7' />
          </div>
        </div>
        <div className='space-y-2'>
          <CardTitle className='text-base font-semibold text-center'>
            {title}
          </CardTitle>
          <CardDescription className='text-sm text-center h-4'>
            {description}
          </CardDescription>
        </div>
      </CardHeader>
      <CardFooter className='flex justify-center pt-2'>
        <Button
          asChild
          variant='default'
          className='w-full transition-all duration-300 hover:scale-105'
        >
          <Link href={href}>{actionText}</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}

export default ConversionCard
