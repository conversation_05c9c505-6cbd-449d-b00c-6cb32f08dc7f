import {
  Lightbulb,
  Link,
  FileText,
  FileUp,
  AudioLines,
  Radio,
} from 'lucide-react'
import { ConversionOption } from './index'

export const conversionOptions: ConversionOption[] = [
  {
    title: 'Idea to Video',
    description: 'Use AI to brainstorm ideas and write \nscript',
    icon: Lightbulb,
    actionText: 'Get Started',
    href: '/create-video/idea',
  },
  {
    title: 'Blog to Video',
    description: 'Paste your blog link and our AI will auto detect scenes',
    icon: Link,
    actionText: 'Convert Blog',
    href: '/create-video/blog',
  },
  {
    title: 'Text to Video',
    description: 'Paste text from any website i.e blog, article, news etc.',
    icon: FileText,
    actionText: 'Start with Text',
    href: '/create-video/text',
  },
  {
    title: 'PDF to Video',
    description: 'Upload pdf having images or plain text \nfile',
    icon: FileUp,
    actionText: 'Upload File',
    href: '/create-video/pdf',
  },
  {
    title: 'Audio to Video',
    description: 'Convert your audio files into engaging videos',
    icon: AudioLines,
    actionText: 'Convert Audio',
    href: '/create-video/audio',
  },
  {
    title: 'Podcast to Video',
    description: 'Transform your podcast episodes into dynamic video',
    icon: Radio,
    actionText: 'Select Podcast',
    href: '/create-video/podcast',
  },
]
