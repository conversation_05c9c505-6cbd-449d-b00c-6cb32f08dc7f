import React from 'react'
import ConversionCard from './conversion-card'
import { LucideIcon } from 'lucide-react'

export interface ConversionOption {
  title: string
  description: string
  icon: LucideIcon
  actionText: string
  href: string
}

interface ConversionGridProps {
  options: ConversionOption[]
  className?: string
}

const ConversionGrid = ({ options, className = '' }: ConversionGridProps) => {
  return (
    <div
      className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 auto-rows-fr ${className}`}
    >
      {options.map((option, index) => (
        <ConversionCard key={index} {...option} />
      ))}
    </div>
  )
}

export default ConversionGrid
