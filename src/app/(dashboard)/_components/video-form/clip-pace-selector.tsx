import { useEffect } from 'react'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ClipPaceSelectorProps {
  value: 'fast' | 'medium' | 'slow' | 'verySlow'
  onValueChange: (value: 'fast' | 'medium' | 'slow' | 'verySlow') => void
  disabled?: boolean
  className?: string
  episodeDuration?: number // Duration in seconds
}

export function ClipPaceSelector({
  value,
  onValueChange,
  disabled = false,
  className = '',
  episodeDuration,
}: ClipPaceSelectorProps) {
  // Define clip pace options
  const clipPaceOptions = [
    {
      value: 'fast' as const,
      label: '🐅 Fast (change every 5 sec)',
      emoji: '🐅',
    },
    {
      value: 'medium' as const,
      label: '🏃‍♂️ Medium (change every 8 sec)',
      emoji: '🏃‍♂️',
    },
    {
      value: 'slow' as const,
      label: '🐢 Slow (change every 12 sec)',
      emoji: '🐢',
    },
    {
      value: 'verySlow' as const,
      label: '🐌 Very Slow (change every 15 sec)',
      emoji: '🐌',
    },
  ]

  // Filter options based on episode duration
  const getAvailableOptions = () => {
    if (!episodeDuration) {
      // If no duration provided, show all options (for non-podcast flows like audio uploads)
      return clipPaceOptions
    }

    const durationMinutes = episodeDuration / 60

    if (durationMinutes < 10) {
      // Less than 10 minutes: show all options (fast, medium, slow, very slow)
      return clipPaceOptions
    } else if (durationMinutes >= 10 && durationMinutes <= 60) {
      // 10 minutes to 1 hour: show medium, slow, very slow (exclude fast)
      return clipPaceOptions.filter(option =>
        ['medium', 'slow', 'verySlow'].includes(option.value)
      )
    } else {
      // Greater than 1 hour: show only slow, very slow (exclude fast and medium)
      return clipPaceOptions.filter(option =>
        ['slow', 'verySlow'].includes(option.value)
      )
    }
  }

  const availableOptions = getAvailableOptions()

  // If current value is not in available options, default to the first available option
  const currentValue = availableOptions.some(option => option.value === value)
    ? value
    : availableOptions[0]?.value || 'medium'

  // Update the value if it's not in available options
  useEffect(() => {
    if (currentValue !== value && availableOptions.length > 0) {
      onValueChange(currentValue)
    }
  }, [currentValue, value, availableOptions.length, onValueChange])

  return (
    <div className={`flex-1 space-y-1 ${className}`}>
      <Label className='text-xs'>Clip pace</Label>
      <Select
        value={currentValue}
        onValueChange={onValueChange}
        disabled={disabled}
      >
        <SelectTrigger className='h-8 text-xs'>
          <SelectValue placeholder='Clip pace' />
        </SelectTrigger>
        <SelectContent>
          {availableOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
