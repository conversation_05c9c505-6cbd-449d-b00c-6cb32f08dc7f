'use client'

import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Clock, Gem } from 'lucide-react'
import { useVideoDurationLimits } from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'
import { useEffect } from 'react'

interface GatedDurationSliderProps {
  value: number
  onChange: (value: number) => void
  disabled?: boolean
  min?: number
  step?: number
}

export function GatedDurationSlider({
  value,
  onChange,
  disabled,
  min = 15,
  step = 15,
}: GatedDurationSliderProps) {
  const { maxDuration, upgradeMessage } = useVideoDurationLimits()
  const { openUpgradeModal } = useUpgradeModal()

  // Auto-reset to max allowed duration if current value exceeds limit
  useEffect(() => {
    if (value > maxDuration) {
      onChange(maxDuration)
      // Show upgrade modal when user tries to exceed limit
      openUpgradeModal('videoDuration', upgradeMessage)
    }
  }, [value, maxDuration, onChange, openUpgradeModal, upgradeMessage])

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`
    }
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    if (remainingSeconds === 0) {
      return `${minutes}min`
    }
    return `${minutes}min ${remainingSeconds}s`
  }

  const formatMinMax = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`
    }
    const minutes = Math.floor(seconds / 60)
    return `${minutes}min`
  }

  const handleValueChange = (values: number[]) => {
    const newValue = values[0]
    if (newValue > maxDuration) {
      // Show upgrade modal and don't change value
      openUpgradeModal('videoDuration', upgradeMessage)
      return
    }
    onChange(newValue)
  }

  return (
    <div className='space-y-2'>
      <Label className='flex items-center gap-1 text-sm'>
        <Clock className='h-3 w-3' />
        Duration: {formatDuration(value)}
        {value >= maxDuration && (
          <div className='flex items-center gap-1 ml-2'>
            <Gem className='h-3 w-3 text-orange-500' />
            <span className='text-xs text-orange-600'>Max for your plan</span>
          </div>
        )}
      </Label>
      <Slider
        value={[value]}
        onValueChange={handleValueChange}
        max={Math.max(maxDuration, value)} // Allow current value even if it exceeds limit
        min={min}
        step={step}
        className='w-full'
        disabled={disabled}
      />
      <div className='flex justify-between text-xs text-muted-foreground'>
        <span>{formatMinMax(min)}</span>
        <div className='flex items-center gap-1'>
          <span>{formatMinMax(maxDuration)}</span>
        </div>
      </div>
      {value >= maxDuration && (
        <div className='text-xs text-orange-600 text-center'>
          {upgradeMessage}
        </div>
      )}
    </div>
  )
}
