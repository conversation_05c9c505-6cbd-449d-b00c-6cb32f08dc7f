interface SideBySideSkeletonProps {
  leftSideType: 'text' | 'pdf'
}

export function SideBySideSkeleton({ leftSideType }: SideBySideSkeletonProps) {
  return (
    <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
      {/* Left Side - Text Editor or PDF Upload */}
      <div className='space-y-3'>
        <div className='h-4 w-32 bg-muted animate-pulse rounded' />
        
        {leftSideType === 'text' ? (
          // Text editor skeleton
          <div className='min-h-[440px] bg-muted animate-pulse rounded-md border' />
        ) : (
          // PDF upload skeleton
          <div className='h-[300px] bg-muted animate-pulse rounded-md border-2 border-dashed flex items-center justify-center'>
            <div className='text-center space-y-2'>
              <div className='h-12 w-12 bg-muted-foreground/20 animate-pulse rounded mx-auto' />
              <div className='h-4 w-24 bg-muted-foreground/20 animate-pulse rounded mx-auto' />
              <div className='h-3 w-32 bg-muted-foreground/20 animate-pulse rounded mx-auto' />
            </div>
          </div>
        )}
        
        {leftSideType === 'text' && (
          <div className='flex justify-between text-xs text-muted-foreground'>
            <div className='h-3 w-24 bg-muted animate-pulse rounded' />
            <div className='h-3 w-20 bg-muted animate-pulse rounded' />
          </div>
        )}
      </div>

      {/* Right Side - Form Controls */}
      <div className='space-y-4'>
        <div className='h-4 w-28 bg-muted animate-pulse rounded' />
        
        {/* Tone, Audience, Platform Row */}
        <div className='flex gap-x-4 gap-y-2 flex-wrap'>
          <div className='flex-1 min-w-[120px] space-y-1'>
            <div className='h-3 w-12 bg-muted animate-pulse rounded' />
            <div className='h-9 bg-muted animate-pulse rounded' />
          </div>
          <div className='flex-1 min-w-[120px] space-y-1'>
            <div className='h-3 w-16 bg-muted animate-pulse rounded' />
            <div className='h-9 bg-muted animate-pulse rounded' />
          </div>
          <div className='flex-1 min-w-[120px] space-y-1'>
            <div className='h-3 w-14 bg-muted animate-pulse rounded' />
            <div className='h-9 bg-muted animate-pulse rounded' />
          </div>
        </div>

        {/* Include Options */}
        <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
          <div className='flex items-center space-x-2'>
            <div className='h-4 w-4 bg-muted animate-pulse rounded' />
            <div className='space-y-1'>
              <div className='h-3 w-20 bg-muted animate-pulse rounded' />
              <div className='h-2 w-32 bg-muted animate-pulse rounded' />
            </div>
          </div>
          <div className='flex items-center space-x-2'>
            <div className='h-4 w-4 bg-muted animate-pulse rounded' />
            <div className='space-y-1'>
              <div className='h-3 w-24 bg-muted animate-pulse rounded' />
              <div className='h-2 w-20 bg-muted animate-pulse rounded' />
            </div>
          </div>
        </div>

        {/* Keywords */}
        <div className='space-y-1'>
          <div className='h-3 w-16 bg-muted animate-pulse rounded' />
          <div className='h-9 bg-muted animate-pulse rounded' />
        </div>

        {/* Advanced Settings */}
        <div className='space-y-3'>
          {/* Duration and Voice Row */}
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
            <div className='space-y-2'>
              <div className='h-3 w-20 bg-muted animate-pulse rounded' />
              <div className='h-2 bg-muted animate-pulse rounded' />
              <div className='flex justify-between'>
                <div className='h-2 w-6 bg-muted animate-pulse rounded' />
                <div className='h-2 w-8 bg-muted animate-pulse rounded' />
              </div>
            </div>
            <div className='space-y-1'>
              <div className='h-3 w-24 bg-muted animate-pulse rounded' />
              <div className='h-9 bg-muted animate-pulse rounded' />
            </div>
          </div>

          {/* Language, Orientation, Autopick Row */}
          <div className='flex space-x-4 space-y-2 flex-wrap'>
            <div className='flex-1 min-w-[100px] space-y-1'>
              <div className='h-3 w-16 bg-muted animate-pulse rounded' />
              <div className='h-9 bg-muted animate-pulse rounded' />
            </div>
            <div className='flex-1 min-w-[100px] space-y-1'>
              <div className='h-3 w-18 bg-muted animate-pulse rounded' />
              <div className='h-9 bg-muted animate-pulse rounded' />
            </div>
            <div className='flex-1 min-w-[100px] space-y-1'>
              <div className='h-3 w-14 bg-muted animate-pulse rounded' />
              <div className='h-9 bg-muted animate-pulse rounded' />
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className='space-y-2 mt-6'>
          <div className='h-10 bg-muted animate-pulse rounded' />
        </div>
      </div>
    </div>
  )
}
