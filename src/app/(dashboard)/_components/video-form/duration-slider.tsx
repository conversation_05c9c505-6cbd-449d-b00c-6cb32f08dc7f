'use client'

import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Clock } from 'lucide-react'

interface DurationSliderProps {
  value: number
  onChange: (value: number) => void
  disabled?: boolean
  min?: number
  max?: number
  step?: number
}

export function DurationSlider({
  value,
  onChange,
  disabled,
  min = 15,
  max = 300,
  step = 15,
}: DurationSliderProps) {
  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`
    }
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    if (remainingSeconds === 0) {
      return `${minutes}min`
    }
    return `${minutes}min ${remainingSeconds}s`
  }

  const formatMinMax = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`
    }
    const minutes = Math.floor(seconds / 60)
    return `${minutes}min`
  }

  return (
    <div className='space-y-2'>
      <Label className='flex items-center gap-1 text-sm'>
        <Clock className='h-3 w-3' />
        Duration: {formatDuration(value)}
      </Label>
      <Slider
        value={[value]}
        onValueChange={values => onChange(values[0])}
        max={max}
        min={min}
        step={step}
        className='w-full'
        disabled={disabled}
      />
      <div className='flex justify-between text-xs text-muted-foreground'>
        <span>{formatMinMax(min)}</span>
        <span>{formatMinMax(max)}</span>
      </div>
    </div>
  )
}
