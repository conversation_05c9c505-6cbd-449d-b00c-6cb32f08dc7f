import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'

export function PodcastResultsSkeleton() {
  return (
    <ScrollArea className='h-[350px] w-full'>
      <div className='space-y-2 pr-4'>
        {Array.from({ length: 5 }).map((_, index) => (
          <div
            key={index}
            className='flex items-start gap-3 p-3 rounded-lg border bg-card'
          >
            {/* Podcast image skeleton */}
            <Skeleton className='w-12 h-12 rounded-md flex-shrink-0' />
            
            {/* Podcast info skeleton */}
            <div className='flex-1 space-y-2'>
              <div className='space-y-1'>
                <Skeleton className='h-4 w-3/4' />
                <Skeleton className='h-3 w-1/2' />
              </div>
              <Skeleton className='h-3 w-full' />
              <Skeleton className='h-3 w-4/5' />
              
              {/* Episode count badge skeleton */}
              <Skeleton className='h-5 w-20 rounded-full' />
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  )
}
