import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Search } from 'lucide-react'

export function PodcastSearchSkeleton() {
  return (
    <Card className='gap-3 overflow-hidden rounded-lg min-h-[60vh] flex flex-col'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-lg'>
          <Search className='h-4 w-4' />
          Find Podcast
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-3 p-4 flex-1 flex flex-col'>
        {/* Tab buttons skeleton */}
        <div className='grid w-full grid-cols-2 bg-foreground/20 rounded-md p-1'>
          <Skeleton className='h-8 rounded-sm' />
          <Skeleton className='h-8 rounded-sm' />
        </div>

        {/* Search input area skeleton */}
        <div className='space-y-3 mt-3 flex-1 flex flex-col'>
          <div className='space-y-2'>
            <Skeleton className='h-4 w-32' />
            <div className='flex gap-2'>
              <Skeleton className='h-9 flex-1' />
              <Skeleton className='h-9 w-12' />
            </div>
          </div>

          {/* Recently searched section skeleton */}
          <div className='flex-1 space-y-3'>
            <Skeleton className='h-5 w-48' />
            <div className='space-y-2'>
              {Array.from({ length: 3 }).map((_, i) => (
                <div
                  key={i}
                  className='flex items-start gap-3 p-3 border rounded-lg'
                >
                  <Skeleton className='w-12 h-12 rounded-lg flex-shrink-0' />
                  <div className='flex-1 space-y-2'>
                    <Skeleton className='h-4 w-3/4' />
                    <Skeleton className='h-3 w-1/2' />
                    <div className='flex gap-2'>
                      <Skeleton className='h-5 w-16' />
                      <Skeleton className='h-5 w-20' />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
