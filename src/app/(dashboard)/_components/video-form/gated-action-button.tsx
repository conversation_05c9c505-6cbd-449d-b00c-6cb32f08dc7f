'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Loader2, <PERSON><PERSON><PERSON>, CheckCircle, Gem } from 'lucide-react'
import { useCanCreateProject } from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'

interface GatedActionButtonProps {
  onClick: (e?: React.FormEvent) => void
  disabled: boolean
  isGenerating: boolean
  progressMessage: string
  actionText: string
  loadingText: string
}

export function GatedActionButton({
  onClick,
  disabled,
  isGenerating,
  progressMessage,
  actionText,
  loadingText,
}: GatedActionButtonProps) {
  const { canCreate, upgradeMessage } = useCanCreateProject()
  const { openUpgradeModal } = useUpgradeModal()

  const handleClick = (e?: React.FormEvent) => {
    if (!canCreate) {
      openUpgradeModal('projects', upgradeMessage)
      return
    }
    onClick(e)
  }

  const isDisabled = disabled || isGenerating || !canCreate

  return (
    <div className='space-y-2 mt-6'>
      <Button
        onClick={handleClick}
        disabled={isDisabled}
        className={`w-full h-10 text-sm ${
          !canCreate
            ? 'bg-gradient-to-r from-orange-600 to-orange-500 text-white hover:from-orange-700 hover:to-orange-600'
            : ''
        }`}
        size='default'
      >
        {isGenerating ? (
          <>
            <Loader2 className='h-4 w-4 animate-spin mr-2' />
            {loadingText}
          </>
        ) : !canCreate ? (
          <>
            <Gem className='h-4 w-4 mr-2' />
            Upgrade to Create More Projects
          </>
        ) : (
          <>
            <Sparkles className='h-4 w-4 mr-2' />
            {actionText}
          </>
        )}
      </Button>

      {!canCreate && (
        <div className='text-center text-sm text-muted-foreground'>
          {upgradeMessage}
        </div>
      )}

      {progressMessage.includes('complete! Redirecting') && (
        <div className='flex items-center justify-center gap-2 text-sm text-green-600'>
          <CheckCircle className='h-4 w-4' />
          <span>Redirecting to video editor...</span>
        </div>
      )}
    </div>
  )
}
