import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Play } from 'lucide-react'

export function EpisodeListSkeleton() {
  return (
    <>
      {/* Selected podcast header skeleton */}
      <Card className='gap-1'>
        <CardHeader className='pb-3'>
          <div className='flex items-center gap-3'>
            <Skeleton className='w-16 h-16 rounded-lg' />
            <div className='flex-1 space-y-2'>
              <Skeleton className='h-5 w-3/4' />
              <Skeleton className='h-4 w-1/2' />
              <Skeleton className='h-5 w-24 rounded-full' />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Episode selection skeleton */}
      <Card>
        <CardHeader className='pb-3'>
          <CardTitle className='flex items-center gap-2 text-base'>
            <Play className='w-4 h-4' />
            Select Episode
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className='h-[350px] w-full'>
            <div className='space-y-2 pr-4'>
              {Array.from({ length: 6 }).map((_, index) => (
                <div
                  key={index}
                  className='flex items-start gap-3 p-3 rounded-lg border bg-card'
                >
                  {/* Episode image skeleton */}
                  <Skeleton className='w-10 h-10 rounded-md flex-shrink-0' />
                  
                  {/* Episode info skeleton */}
                  <div className='flex-1 space-y-2'>
                    <Skeleton className='h-4 w-full' />
                    <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                      <div className='flex items-center gap-1'>
                        <Skeleton className='h-3 w-3 rounded' />
                        <Skeleton className='h-3 w-16' />
                      </div>
                      <div className='flex items-center gap-1'>
                        <Skeleton className='h-3 w-3 rounded' />
                        <Skeleton className='h-3 w-12' />
                      </div>
                    </div>
                    <Skeleton className='h-3 w-full' />
                    <Skeleton className='h-3 w-3/4' />
                  </div>
                  
                  {/* Select button skeleton */}
                  <Skeleton className='h-8 w-16 rounded-md' />
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </>
  )
}
