import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent } from '@/components/ui/card'
import { AudioLines } from 'lucide-react'

export function AudioFormSkeleton() {
  return (
    <>
      {/* Audio Upload Section */}
      <div className='mb-4'>
        {/* Upload area skeleton */}
        <div className='p-10 rounded-lg border-2 border-dashed border-border'>
          <div className='flex flex-col items-center justify-center space-y-4'>
            {/* Audio icon */}
            <div className='relative'>
              <AudioLines className='w-16 h-16 text-muted-foreground/50' />
            </div>

            {/* Title skeleton */}
            <Skeleton className='h-6 w-40' />

            {/* Description skeleton */}
            <Skeleton className='h-4 w-64' />

            {/* Format badges skeleton */}
            <div className='flex gap-2'>
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className='h-6 w-12 rounded-full' />
              ))}
            </div>

            {/* File size info skeleton */}
            <Skeleton className='h-3 w-48' />
          </div>
        </div>
      </div>

      {/* Configuration Section */}
      <Card>
        <CardContent>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex space-x-4 space-y-2 flex-wrap'>
                {/* Three selector skeletons in a row */}
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className='flex-1 min-w-[120px] space-y-1'>
                    <Skeleton className='h-3 w-16' />
                    <Skeleton className='h-8 w-full rounded-md' />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generate Button skeleton */}
      <div className='flex justify-center pt-4'>
        <Skeleton className='h-10 w-full rounded-md' />
      </div>
    </>
  )
}
