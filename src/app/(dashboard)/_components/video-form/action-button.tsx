'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Loader2, <PERSON><PERSON>les, CheckCircle } from 'lucide-react'

interface ActionButtonProps {
  onClick: (e?: React.FormEvent) => void
  disabled: boolean
  isGenerating: boolean
  progressMessage: string
  actionText: string
  loadingText: string
}

export function ActionButton({
  onClick,
  disabled,
  isGenerating,
  progressMessage,
  actionText,
  loadingText,
}: ActionButtonProps) {
  return (
    <div className='space-y-2 mt-6'>
      <Button
        onClick={onClick}
        disabled={disabled || isGenerating}
        className='w-full h-10 text-sm'
        size='default'
      >
        {isGenerating ? (
          <>
            <Loader2 className='h-4 w-4 animate-spin mr-2' />
            {loadingText}
          </>
        ) : (
          <>
            <Sparkles className='h-4 w-4 mr-2' />
            {actionText}
          </>
        )}
      </Button>

      {progressMessage.includes('complete! Redirecting') && (
        <div className='flex items-center justify-center gap-2 text-sm text-green-600'>
          <CheckCircle className='h-4 w-4' />
          <span>Redirecting to video editor...</span>
        </div>
      )}
    </div>
  )
}
