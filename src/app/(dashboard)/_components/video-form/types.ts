import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'
import {
  Video,
  Image,
  Sparkles,
  Shuffle,
  Monitor,
  Smartphone,
  Square,
  type LucideIcon,
} from 'lucide-react'
import {
  YouTubeIcon,
  TikTokIcon,
  InstagramIcon,
  LinkedInIcon,
  TwitterIcon,
} from '@/components/icons/platform-icons'
import {
  CasualIcon,
  FormalIcon,
  FriendlyIcon,
  ProfessionalIcon,
  EnthusiasticIcon,
  EducationalIcon,
} from '@/components/icons/tone-icons'
import {
  GeneralAudienceIcon,
  BusinessProfessionalsIcon,
  StudentsIcon,
  TechSavvyIcon,
  CreativeProfessionalsIcon,
  EntrepreneursIcon,
} from '@/components/icons/audience-icons'

// Base configuration interface for all video creation forms
export interface BaseVideoConfig {
  tone: string
  audience: string
  platform: string
  duration: number
  includeHook: boolean
  includeCTA: boolean
  language: string
  orientation: 'landscape' | 'portrait' | 'square'
  autopick: string
  voice: ElevenVoice | null
  keywords: string
}

// Extended configurations for specific video types
export interface IdeaVideoConfig extends BaseVideoConfig {
  idea: string
  creativity: number
  blogUrl?: string
}

export interface BlogVideoConfig extends BaseVideoConfig {
  blogUrl: string
}

export interface TextVideoConfig extends BaseVideoConfig {
  text: string
  creativity: number
}

export interface PDFVideoConfig extends BaseVideoConfig {
  pdfUrl?: string
  files?: File[] // For file upload state
}

// Common form field options

// Tone option with icon
export interface ToneOption {
  value: string
  label: string
  icon: React.ComponentType<{ className?: string }>
}

export const TONE_OPTIONS = [
  { value: 'casual', label: 'Casual', icon: CasualIcon },
  { value: 'formal', label: 'Formal', icon: FormalIcon },
  { value: 'friendly', label: 'Friendly', icon: FriendlyIcon },
  { value: 'professional', label: 'Professional', icon: ProfessionalIcon },
  { value: 'enthusiastic', label: 'Enthusiastic', icon: EnthusiasticIcon },
  { value: 'educational', label: 'Educational', icon: EducationalIcon },
] as const

// Audience option with icon
export interface AudienceOption {
  value: string
  label: string
  icon: React.ComponentType<{ className?: string }>
}

export const AUDIENCE_OPTIONS = [
  { value: 'general', label: 'General Audience', icon: GeneralAudienceIcon },
  {
    value: 'business',
    label: 'Business Professionals',
    icon: BusinessProfessionalsIcon,
  },
  { value: 'students', label: 'Students', icon: StudentsIcon },
  { value: 'tech', label: 'Tech-Savvy Users', icon: TechSavvyIcon },
  {
    value: 'creatives',
    label: 'Creative Professionals',
    icon: CreativeProfessionalsIcon,
  },
  { value: 'entrepreneurs', label: 'Entrepreneurs', icon: EntrepreneursIcon },
] as const

// Platform option with icon
export interface PlatformOption {
  value: string
  label: string
  icon: React.ComponentType<{ className?: string }>
}

export const PLATFORM_OPTIONS = [
  { value: 'youtube', label: 'YouTube', icon: YouTubeIcon },
  { value: 'tiktok', label: 'TikTok Clip', icon: TikTokIcon },
  { value: 'instagram', label: 'Instagram Reel', icon: InstagramIcon },
  { value: 'linkedin', label: 'LinkedIn Post', icon: LinkedInIcon },
  { value: 'twitter', label: 'Twitter/X', icon: TwitterIcon },
] as const

export const LANGUAGE_OPTIONS = [
  { value: 'english', label: '🇺🇸 English' },
  { value: 'spanish', label: '🇪🇸 Spanish' },
  { value: 'french', label: '🇫🇷 French' },
  { value: 'german', label: '🇩🇪 German' },
  { value: 'italian', label: '🇮🇹 Italian' },
  { value: 'portuguese', label: '🇵🇹 Portuguese' },
  { value: 'dutch', label: '🇳🇱 Dutch' },
  { value: 'russian', label: '🇷🇺 Russian' },
  { value: 'japanese', label: '🇯🇵 Japanese' },
  { value: 'korean', label: '🇰🇷 Korean' },
  { value: 'chinese', label: '🇨🇳 Chinese' },
  { value: 'arabic', label: '🇸🇦 Arabic' },
  { value: 'hindi', label: '🇮🇳 Hindi' },
  { value: 'turkish', label: '🇹🇷 Turkish' },
  { value: 'polish', label: '🇵🇱 Polish' },
  { value: 'ukrainian', label: '🇺🇦 Ukrainian' },
  { value: 'vietnamese', label: '🇻🇳 Vietnamese' },
  { value: 'swedish', label: '🇸🇪 Swedish' },
  { value: 'norwegian', label: '🇳🇴 Norwegian' },
  { value: 'danish', label: '🇩🇰 Danish' },
  { value: 'finnish', label: '🇫🇮 Finnish' },
  { value: 'czech', label: '🇨🇿 Czech' },
  { value: 'hungarian', label: '🇭🇺 Hungarian' },
  { value: 'romanian', label: '🇷🇴 Romanian' },
  { value: 'bulgarian', label: '🇧🇬 Bulgarian' },
  { value: 'croatian', label: '🇭🇷 Croatian' },
  { value: 'slovak', label: '🇸🇰 Slovak' },
  { value: 'greek', label: '🇬🇷 Greek' },
  { value: 'indonesian', label: '🇮🇩 Indonesian' },
  { value: 'malay', label: '🇲🇾 Malay' },
  { value: 'tamil', label: '🇱🇰 Tamil' },
  { value: 'filipino', label: '🇵🇭 Filipino' },
] as const

// Orientation option with icon
export interface OrientationOption {
  value: string
  label: string
  icon: LucideIcon
}

export const ORIENTATION_OPTIONS = [
  { value: 'landscape', label: 'Landscape', icon: Monitor },
  { value: 'portrait', label: 'Portrait', icon: Smartphone },
  { value: 'square', label: 'Square', icon: Square },
] as const

// Autopick option with icon
export interface AutopickOption {
  value: string
  label: string
  icon: LucideIcon
}

export const AUTOPICK_OPTIONS = [
  { value: 'stock-videos', label: 'Stock Videos', icon: Video },
  { value: 'stock-images', label: 'Stock Images', icon: Image },
  { value: 'ai-images', label: 'AI Generated Images', icon: Sparkles },
  { value: 'mix', label: 'Mixed Media', icon: Shuffle },
] as const
