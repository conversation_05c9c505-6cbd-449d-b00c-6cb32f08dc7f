'use client'

import React from 'react'
import { LucideIcon } from 'lucide-react'

interface SectionHeaderProps {
  icon: LucideIcon
  title: string
  description: string
  className?: string
}

const SectionHeader = ({
  icon: Icon,
  title,
  description,
  className = '',
}: SectionHeaderProps) => {
  return (
    <div className={`mb-6 ${className}`}>
      <div className='flex items-center gap-3 mb-2'>
        <div className='p-2 rounded-lg bg-primary/10'>
          <Icon className='h-5 w-5 text-primary' />
        </div>
        <h1 className='text-2xl font-semibold tracking-tight'>{title}</h1>
      </div>
      <p className='text-muted-foreground'>{description}</p>
    </div>
  )
}

export default SectionHeader
