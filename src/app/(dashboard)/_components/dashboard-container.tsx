import React from 'react'

interface DashboardContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: string
}

const DashboardContainer = ({
  children,
  className = '',
  maxWidth = '1400px',
}: DashboardContainerProps) => {
  return (
    <div className={`w-full mx-auto px-4 ${className}`} style={{ maxWidth }}>
      {children}
    </div>
  )
}

export default DashboardContainer
