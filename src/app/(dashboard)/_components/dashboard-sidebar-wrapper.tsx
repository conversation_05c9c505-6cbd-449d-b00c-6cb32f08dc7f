'use client'

import { AppSidebar } from '@/components/app-sidebar'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { ModalProvider } from '@/providers/modal-provider'
import { ReactNode } from 'react'

interface DashboardSidebarWrapperProps {
  children: ReactNode
}

export function DashboardSidebarWrapper({ children }: DashboardSidebarWrapperProps) {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        {children}
      </SidebarInset>
      <ModalProvider />
    </SidebarProvider>
  )
}
