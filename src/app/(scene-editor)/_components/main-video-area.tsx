'use client'

import { Badge } from '@/components/ui/badge'
import { Monitor, Smartphone, Square } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useVideoStore, VideoOrientation } from '@/store/video-store'
import React, { useRef } from 'react'
import { PlayerRef } from '@remotion/player'
import { DynamicRemotionPreview } from './dynamic-remotion-preview'

export function MainVideoArea({
  playerRef,
  isFullscreen = false,
}: {
  playerRef?: React.RefObject<PlayerRef | null>
  isFullscreen?: boolean
}) {
  const { scenes, subtitlePosition, orientation, setOrientation, project } =
    useVideoStore()

  const videoContainerRef = useRef<HTMLDivElement>(null)

  // Filter valid scenes for preview
  const validScenes = scenes.filter(
    scene => typeof scene.duration === 'number' && scene.duration > 0
  )

  // For podcast flows, use project duration; for regular flows, use scene durations
  const isPodcastOrAudioFlow = !!project?.speech
  const totalDuration = isPodcastOrAudioFlow
    ? project?.duration
      ? parseFloat(project.duration.toString())
      : 0
    : validScenes.reduce((acc, scene) => acc + (scene.duration || 0), 0)

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className='h-full flex flex-col bg-background'>
      {/* Video Info Bar */}
      {!isFullscreen && (
        <div className='flex items-center justify-between px-2 sm:px-4 py-1.5 sm:py-2 border-b bg-card flex-shrink-0'>
          <div className='flex items-center gap-2 sm:gap-4'>
            <h2 className='font-semibold text-base sm:text-lg truncate max-w-[120px] sm:max-w-none'>
              Video Preview
            </h2>
          </div>

          <div className='flex items-center gap-1 sm:gap-2'>
            {/* Orientation Dropdown */}
            <Select
              value={orientation}
              onValueChange={value => setOrientation(value as VideoOrientation)}
            >
              <SelectTrigger className='w-auto gap-1 sm:gap-2 h-7 sm:h-8 text-xs sm:text-sm px-2 sm:px-3 min-w-0'>
                <SelectValue placeholder='Orientation' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='landscape'>
                  <div className='flex items-center gap-2'>
                    <Monitor className='h-4 w-4' />
                    <span>Landscape (16:9)</span>
                  </div>
                </SelectItem>
                <SelectItem value='square'>
                  <div className='flex items-center gap-2'>
                    <Square className='h-4 w-4' />
                    <span>Square (1:1)</span>
                  </div>
                </SelectItem>
                <SelectItem value='portrait'>
                  <div className='flex items-center gap-2'>
                    <Smartphone className='h-4 w-4' />
                    <span>Portrait (9:16)</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Scene count and duration - hidden on smallest screens */}
            <Badge variant='secondary' className='hidden sm:flex text-xs'>
              {validScenes.length} scenes • {formatDuration(totalDuration)}
            </Badge>
          </div>
        </div>
      )}

      {/* Video Preview Display - Takes all available space */}
      <div
        ref={videoContainerRef}
        className='flex-1 min-h-0 flex items-center justify-center p-3 sm:p-6 bg-muted/40'
      >
        <DynamicRemotionPreview
          scenes={scenes}
          subtitlePosition={subtitlePosition}
          orientation={orientation}
          playerRef={playerRef}
          isFullscreen={isFullscreen}
        />
      </div>
    </div>
  )
}
