'use client'

import { Cloud, CloudOff, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'

interface SyncStatusIndicatorProps {
  isSaving: boolean
  hasUnsavedChanges: boolean
  lastSaved: Date | null
  onSave?: () => void
  className?: string
  showText?: boolean
  showSaveButton?: boolean
}

export function SyncStatusIndicator({
  isSaving,
  hasUnsavedChanges,
  lastSaved,
  onSave,
  className,
  showText = true,
  showSaveButton = false,
}: SyncStatusIndicatorProps) {
  const formatLastSaved = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))

    if (diffMinutes < 1) {
      return 'just now'
    } else if (diffMinutes < 60) {
      return `${diffMinutes}m ago`
    } else {
      const diffHours = Math.floor(diffMinutes / 60)
      if (diffHours < 24) {
        return `${diffHours}h ago`
      } else {
        return date.toLocaleDateString()
      }
    }
  }

  const formatFullTimestamp = (date: Date) => {
    return date.toLocaleString(undefined, {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
  }

  const getStatusInfo = () => {
    if (isSaving) {
      return {
        icon: Loader2,
        text: 'Syncing...',
        className: 'text-blue-600 dark:text-blue-400',
        iconClassName: 'animate-spin',
        status: 'syncing',
        description: 'Your changes are being saved to the cloud.',
      }
    }

    if (hasUnsavedChanges) {
      return {
        icon: CloudOff,
        text: 'Unsaved changes',
        className: 'text-amber-600 dark:text-amber-400',
        iconClassName: '',
        status: 'unsaved',
        description: 'You have unsaved changes that need to be synced.',
      }
    }

    if (lastSaved) {
      return {
        icon: Cloud,
        text: `Synced ${formatLastSaved(lastSaved)}`,
        className: 'text-green-600 dark:text-green-400',
        iconClassName: '',
        status: 'synced',
        description: 'All changes are saved and synced to the cloud.',
      }
    }

    return {
      icon: CloudOff,
      text: 'Not synced',
      className: 'text-gray-500 dark:text-gray-400',
      iconClassName: '',
      status: 'not-synced',
      description: 'Project has not been synced yet.',
    }
  }

  const {
    icon: Icon,
    text,
    className: statusClassName,
    iconClassName,
    status,
    description,
  } = getStatusInfo()

  const getStatusBadgeVariant = () => {
    switch (status) {
      case 'syncing':
        return 'default'
      case 'synced':
        return 'secondary'
      case 'unsaved':
        return 'destructive'
      case 'not-synced':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const syncStatusContent = (
    <div
      className={cn('flex items-center gap-1.5', statusClassName, className)}
    >
      <Icon className={cn('h-4 w-4', iconClassName)} />
      {showText && <span className='text-sm font-medium'>{text}</span>}
      {showSaveButton && (hasUnsavedChanges || isSaving) && onSave && (
        <Button
          size='sm'
          variant='ghost'
          onClick={onSave}
          disabled={isSaving}
          className='h-6 w-6 p-0 ml-1'
          title={isSaving ? 'Syncing...' : 'Sync now'}
        >
          <Loader2
            className={cn('h-3 w-3', isSaving ? 'animate-spin' : 'hidden')}
          />
          <Cloud className={cn('h-3 w-3', isSaving ? 'hidden' : 'block')} />
        </Button>
      )}
    </div>
  )

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className='cursor-help'>{syncStatusContent}</div>
      </PopoverTrigger>
      <PopoverContent className='w-80' side='bottom' align='end'>
        <div className='space-y-3'>
          {/* Header */}
          <div className='flex items-center justify-between'>
            <h4 className='font-semibold text-sm'>Sync Status</h4>
            <Badge variant={getStatusBadgeVariant()}>
              {status === 'syncing' && 'Syncing'}
              {status === 'synced' && 'Synced'}
              {status === 'unsaved' && 'Unsaved'}
              {status === 'not-synced' && 'Not Synced'}
            </Badge>
          </div>

          {/* Description */}
          <p className='text-sm text-muted-foreground'>{description}</p>

          {/* Last Saved Info */}
          {lastSaved && (
            <div className='space-y-2'>
              <div className='flex items-center justify-between text-xs'>
                <span className='text-muted-foreground'>Last saved:</span>
                <span className='font-mono'>{formatLastSaved(lastSaved)}</span>
              </div>
              <div className='flex items-center justify-between text-xs'>
                <span className='text-muted-foreground'>Full timestamp:</span>
                <span className='font-mono text-xs'>
                  {formatFullTimestamp(lastSaved)}
                </span>
              </div>
            </div>
          )}

          {/* Action Needed */}
          {hasUnsavedChanges && (
            <div className='border-t pt-3'>
              <div className='flex items-center justify-between text-xs'>
                <span className='text-muted-foreground'>Action needed:</span>
                <span className='text-xs text-amber-600 dark:text-amber-400'>
                  Click cloud icon to save
                </span>
              </div>
            </div>
          )}

          {/* Manual Save Button */}
          {onSave && !isSaving && (
            <div className='border-t pt-3'>
              <Button
                onClick={onSave}
                size='sm'
                variant='outline'
                className='w-full'
                disabled={!hasUnsavedChanges}
              >
                <Cloud className='h-4 w-4 mr-2' />
                {hasUnsavedChanges ? 'Save Changes' : 'All Changes Saved'}
              </Button>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Compact version for use in headers/toolbars
export function CompactSyncStatus({
  isSaving,
  hasUnsavedChanges,
  lastSaved,
  onSave,
  className,
  showSaveButton = false,
}: Omit<SyncStatusIndicatorProps, 'showText'>) {
  return (
    <SyncStatusIndicator
      isSaving={isSaving}
      hasUnsavedChanges={hasUnsavedChanges}
      lastSaved={lastSaved}
      onSave={onSave}
      className={className}
      showText={false}
      showSaveButton={showSaveButton}
    />
  )
}
