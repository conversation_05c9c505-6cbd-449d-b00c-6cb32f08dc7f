'use client'

import React, { useRef, useEffect } from 'react'
import { MainVideoArea } from './main-video-area'
import { RemotionPreviewControls } from './remotion-preview-controls'
import { PlayerRef } from '@remotion/player'
import { useVideoStore } from '@/store/video-store'

interface VideoPlayerContainerProps {
  isFullscreen: boolean
  onFullscreenToggle: () => void
}

export function VideoPlayerContainer({
  isFullscreen,
  onFullscreenToggle,
}: VideoPlayerContainerProps) {
  const playerRef = useRef<PlayerRef>(null)
  const { setPlayerRef } = useVideoStore()

  // Set the player ref in the store when it changes
  useEffect(() => {
    setPlayerRef(playerRef)
    return () => setPlayerRef(null)
  }, [setPlayerRef])

  // Detect seekbar/frame stalls and recover
  useEffect(() => {
    let lastFrame = -1
    let stalledSince: number | null = null

    const interval = setInterval(() => {
      const player = playerRef.current
      const isPlaying = player?.isPlaying?.() ?? false
      const frame = player?.getCurrentFrame?.() ?? -1

      if (!isPlaying || frame < 0) {
        stalledSince = null
        lastFrame = frame
        return
      }

      if (frame === lastFrame) {
        if (stalledSince == null) stalledSince = Date.now()
        const elapsed = Date.now() - stalledSince
        if (elapsed > 3000) {
          const url = new URL(window.location.href)
          url.searchParams.set('_ts', String(Date.now()))
          window.location.replace(url.toString())
        }
      } else {
        stalledSince = null
      }

      lastFrame = frame
    }, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className='h-full flex flex-col overflow-hidden'>
      {/* Video area takes available space but leaves room for controls */}
      <div className='flex-1 min-h-0 overflow-hidden'>
        <MainVideoArea playerRef={playerRef} isFullscreen={isFullscreen} />
      </div>

      {/* Controls always visible at bottom - especially important in fullscreen */}
      <div className='flex-shrink-0'>
        <RemotionPreviewControls
          fullscreenToggle={onFullscreenToggle}
          playerRef={playerRef}
        />
      </div>
    </div>
  )
}
