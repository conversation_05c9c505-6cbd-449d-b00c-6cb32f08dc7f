'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Play,
  Pause,
  Maximize2,
  Volume2,
  VolumeX,
  SkipBack,
  Ski<PERSON>Forward,
} from 'lucide-react'
import { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { useVideoStore } from '@/store/video-store'
import { Slider } from '@/components/ui/slider'
import { PlayerRef } from '@remotion/player'

interface RemotionPreviewControlsProps {
  fullscreenToggle?: () => void
  playerRef?: React.RefObject<PlayerRef | null>
}

interface SceneMarker {
  id: string
  startTime: number
  endTime: number
  percentage: number
  width: number
}

export function RemotionPreviewControls({
  fullscreenToggle,
  playerRef,
}: RemotionPreviewControlsProps) {
  const { scenes, project } = useVideoStore()
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const controlsUpdateInterval = useRef<NodeJS.Timeout | null>(null)

  const isPodcastOrAudioFlow = !!project?.speech

  // Calculate total duration and scene markers
  const { sceneMarkers } = useMemo(() => {
    // For podcast flows, use project duration; for regular flows, use scene durations
    const totalDuration = isPodcastOrAudioFlow
      ? project?.duration
        ? parseFloat(project.duration.toString())
        : 0
      : scenes.reduce((acc, scene) => acc + (scene.duration || 0), 0)

    // Then calculate markers with accurate percentages based on the total
    let currentStartTime = 0
    const markers: SceneMarker[] = scenes.map(scene => {
      const sceneDuration = scene.duration || 0
      const startPercentage = (currentStartTime / totalDuration) * 100
      const width = (sceneDuration / totalDuration) * 100

      // Store current position before adding this scene's duration
      const startTime = currentStartTime
      // Update for next scene
      currentStartTime += sceneDuration

      return {
        id: scene.id,
        startTime: startTime,
        endTime: currentStartTime,
        percentage: startPercentage,
        width: width,
      }
    })

    return { totalDuration, sceneMarkers: markers }
  }, [scenes, isPodcastOrAudioFlow, project?.duration])

  // Set duration
  useEffect(() => {
    const totalDuration = isPodcastOrAudioFlow
      ? project?.duration
        ? parseFloat(project.duration.toString())
        : 0
      : scenes.reduce((acc, scene) => acc + (scene.duration || 0), 0)
    setDuration(totalDuration || 0)
  }, [scenes, project, isPodcastOrAudioFlow])

  // Function to format time as MM:SS.mm
  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60)
    const seconds = Math.floor(timeInSeconds % 60)
    const milliseconds = Math.floor((timeInSeconds % 1) * 100)
    return `${minutes.toString().padStart(2, '0')}:${seconds
      .toString()
      .padStart(2, '0')}.${milliseconds.toString().padStart(2, '0')}`
  }

  // Toggle play/pause using Remotion player
  const togglePlayPause = useCallback(() => {
    if (!playerRef?.current) return

    if (isPlaying) {
      playerRef.current.pause()
    } else {
      playerRef.current.play()
    }

    setIsPlaying(!isPlaying)
  }, [isPlaying, playerRef])

  // Update current time based on player
  useEffect(() => {
    if (!playerRef?.current) return

    const updatePlaybackState = () => {
      if (!playerRef?.current) return

      // Get current time from player - convert frames to seconds
      const currentFrame = playerRef.current.getCurrentFrame() || 0
      const fps = 30 // Same as defined in Player component
      setCurrentTime(currentFrame / fps)

      // Check if playing state has changed
      const playerIsPlaying = playerRef.current.isPlaying?.() || false
      if (playerIsPlaying !== isPlaying) {
        setIsPlaying(playerIsPlaying)
      }
    }

    // Start interval to update playback state
    controlsUpdateInterval.current = setInterval(updatePlaybackState, 100)

    return () => {
      if (controlsUpdateInterval.current) {
        clearInterval(controlsUpdateInterval.current)
      }
    }
  }, [playerRef, isPlaying])

  // Set current time and update video position
  const seekToTime = useCallback(
    (newTime: number) => {
      if (!playerRef?.current) return

      // Clamp time to valid range
      newTime = Math.max(0, Math.min(newTime, duration))

      // Convert time to frame and seek
      const fps = 30
      const frame = Math.round(newTime * fps)
      playerRef.current.seekTo(frame)

      // Update displayed time immediately for smoother UI
      setCurrentTime(newTime)
    },
    [playerRef, duration]
  )

  // Handle volume change
  const handleVolumeChange = (value: number[]) => {
    if (!playerRef?.current) return

    const newVolume = value[0]
    setVolume(newVolume)
    setIsMuted(newVolume === 0)

    // Set volume in player
    playerRef.current.setVolume(newVolume)
    if (newVolume === 0) {
      playerRef.current.mute()
    } else if (isMuted) {
      playerRef.current.unmute()
    }
  }

  // Toggle mute
  const toggleMute = () => {
    if (!playerRef?.current) return

    if (isMuted) {
      // Unmute (restore previous volume or set to 0.5)
      const newVolume = volume === 0 ? 0.5 : volume
      setVolume(newVolume)
      setIsMuted(false)
      playerRef.current.setVolume(newVolume)
      playerRef.current.unmute()
    } else {
      // Mute (keep track of current volume but set player to mute)
      setIsMuted(true)
      playerRef.current.mute()
    }
  }

  // Get current scene index
  const getCurrentSceneIndex = () => {
    let cumulativeTime = 0
    for (let i = 0; i < scenes.length; i++) {
      cumulativeTime += scenes[i].duration || 0
      if (currentTime < cumulativeTime) return i
    }
    return scenes.length - 1
  }

  // Current scene index
  const currentSceneIndex = getCurrentSceneIndex()

  // Handle timeline slider change
  const handleTimelineChange = (value: number[]) => {
    if (!playerRef?.current) return
    const newTime = value[0]
    seekToTime(newTime)
  }

  // Navigate to previous scene
  const goToPreviousScene = useCallback(() => {
    if (currentSceneIndex <= 0 || !playerRef?.current) return
    const prevMarker = sceneMarkers[currentSceneIndex - 1]
    if (prevMarker) {
      seekToTime(prevMarker.startTime + 0.1) // Small offset to ensure we're in the scene
    }
  }, [currentSceneIndex, sceneMarkers, seekToTime, playerRef])

  // Navigate to next scene
  const goToNextScene = useCallback(() => {
    if (currentSceneIndex >= sceneMarkers.length - 1 || !playerRef?.current)
      return
    const nextMarker = sceneMarkers[currentSceneIndex + 1]
    if (nextMarker) {
      seekToTime(nextMarker.startTime + 0.1) // Small offset to ensure we're in the scene
    }
  }, [currentSceneIndex, sceneMarkers, seekToTime, playerRef])

  // Don't show controls if there's no content
  if (duration <= 0 || scenes.length === 0) {
    return null
  }

  return (
    <div className='flex flex-col bg-background/95 backdrop-blur-sm border-t border-b z-[40] pb-2 md:pb-0'>
      {/* Sleek timeline with scene markers */}
      <div className='relative px-4 py-2'>
        {/* Slider with scene markers */}
        <div className='relative'>
          {/* Scene markers on slider */}
          <div className='absolute top-1/2 left-0 right-0 h-1 w-full -translate-y-1/2 z-10 pointer-events-none'>
            {sceneMarkers
              .filter((_, i) => i > 0) // Filter out the first marker
              .map(marker => (
                <div
                  key={marker.id}
                  className='absolute h-2 w-0.5 bg-white/30'
                  style={{
                    left: `${marker.percentage}%`,
                    transform: 'translateX(-50%)',
                    top: '-2px',
                  }}
                />
              ))}
          </div>

          <Slider
            value={[currentTime]}
            min={0}
            max={duration}
            step={0.01}
            onValueChange={handleTimelineChange}
            className='mb-1'
          />
        </div>

        {/* Time indicators and controls row */}
        <div className='flex items-center justify-between mt-1'>
          {/* Left group: Play/Pause and Volume */}
          <div className='flex items-center gap-2'>
            {/* Scene navigation */}
            <Button
              variant='ghost'
              size='icon'
              className='h-6 w-6 rounded-full flex-shrink-0'
              onClick={goToPreviousScene}
              disabled={currentSceneIndex <= 0}
            >
              <SkipBack className='h-3 w-3 text-muted-foreground' />
              <span className='sr-only'>Previous Scene</span>
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='h-7 w-7 rounded-full bg-primary/10 hover:bg-primary/20 flex-shrink-0'
              onClick={togglePlayPause}
            >
              {isPlaying ? (
                <Pause className='h-3.5 w-3.5 text-primary' />
              ) : (
                <Play className='h-3.5 w-3.5 text-primary' />
              )}
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='h-6 w-6 rounded-full flex-shrink-0'
              onClick={goToNextScene}
              disabled={currentSceneIndex >= sceneMarkers.length - 1}
            >
              <SkipForward className='h-3 w-3 text-muted-foreground' />
              <span className='sr-only'>Next Scene</span>
            </Button>

            {/* Volume control - directly next to play button */}
            <div className='flex items-center gap-1 w-28 ml-1'>
              <Button
                variant='ghost'
                size='icon'
                className='h-6 w-6 rounded-full flex-shrink-0'
                onClick={toggleMute}
              >
                {isMuted ? (
                  <VolumeX className='h-3 w-3 text-muted-foreground' />
                ) : (
                  <Volume2 className='h-3 w-3 text-muted-foreground' />
                )}
              </Button>
              <Slider
                className='w-20'
                value={[isMuted ? 0 : volume]}
                min={0}
                max={1}
                step={0.05}
                onValueChange={handleVolumeChange}
              />
            </div>
          </div>

          {/* Center: Time display */}
          <div className='flex items-center gap-1'>
            <span className='text-xs font-mono text-muted-foreground'>
              {formatTime(currentTime)}
            </span>
            <span className='text-xs font-mono text-muted-foreground/50 mx-1'>
              /
            </span>
            <span className='text-xs font-mono text-muted-foreground'>
              {formatTime(duration)}
            </span>
          </div>

          {/* Right: Fullscreen button */}
          <Button
            variant='ghost'
            size='icon'
            className='h-7 w-7 rounded-full flex-shrink-0'
            onClick={fullscreenToggle}
          >
            <Maximize2 className='h-3.5 w-3.5' />
          </Button>
        </div>
      </div>

      {/* Scene indicator (active scene) */}
      {currentSceneIndex !== -1 && (
        <div className='absolute top-0 left-0 right-0 h-[1px] bg-transparent'>
          <div
            className='absolute h-full bg-primary/20'
            style={{
              left: `${sceneMarkers[currentSceneIndex]?.percentage || 0}%`,
              width: `${sceneMarkers[currentSceneIndex]?.width || 0}%`,
            }}
          />
        </div>
      )}
    </div>
  )
}
