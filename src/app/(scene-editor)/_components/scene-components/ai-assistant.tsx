'use client'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON>rkles, ArrowUp, ArrowDown, CheckCircle } from 'lucide-react'
import { AI_ACTIONS } from '../constants/scene-options'

interface AIAssistantProps {
  sceneId: string
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  input: string
  onInputChange: (value: string) => void
  isLoading: boolean
  response: string
  showResponse: boolean
  onAction: (action: (typeof AI_ACTIONS)[number]['action']) => void
  onSubmit: () => void
  onInsertResponse: () => void
  onDiscardResponse: () => void
  onTryAgain: () => void
}

const getIcon = (iconName: string) => {
  switch (iconName) {
    case 'sparkles':
      return <Sparkles className='h-4 w-4' />
    case 'emoji':
      return <span className='text-yellow-500'>😊</span>
    case 'arrow-up':
      return <ArrowUp className='h-4 w-4 rotate-90' />
    case 'arrow-down':
      return <ArrowDown className='h-4 w-4 rotate-90' />
    case 'check-circle':
      return <CheckCircle className='h-4 w-4' />
    case 'diamond':
      return (
        <span className='h-4 w-4 flex items-center justify-center font-bold'>
          ◆
        </span>
      )
    default:
      return <Sparkles className='h-4 w-4' />
  }
}

export function AIAssistant({
  isOpen,
  onOpenChange,
  input,
  onInputChange,
  isLoading,
  response,
  showResponse,
  onAction,
  onSubmit,
  onInsertResponse,
  onDiscardResponse,
  onTryAgain,
}: AIAssistantProps) {
  return (
    <DropdownMenu open={isOpen} onOpenChange={onOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className='h-7 w-7 p-0 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground rounded-full shadow-md hover:shadow-lg transition-all duration-200 absolute bottom-3.5 right-2'
          onClick={e => e.stopPropagation()}
        >
          <Sparkles className='h-3.5 w-3.5' />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align='end'
        className='w-64 p-0'
        onClick={e => e.stopPropagation()}
      >
        <div className='px-3 py-2 text-sm font-medium text-muted-foreground border-b'>
          AI Assistant
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className='p-3 border-b bg-muted/30'>
            <div className='flex items-center gap-2 text-sm text-muted-foreground'>
              <div className='animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full'></div>
              Processing with AI...
            </div>
          </div>
        )}

        {/* AI Response */}
        {showResponse && !isLoading && (
          <div className='p-3 border-b bg-muted/30'>
            <div className='text-xs text-muted-foreground mb-2'>
              AI Response:
            </div>
            <div className='text-sm bg-background rounded-md p-2 mb-3 max-h-32 overflow-y-auto border'>
              {response}
            </div>
            <div className='flex gap-2'>
              <Button
                size='sm'
                onClick={e => {
                  e.stopPropagation()
                  onInsertResponse()
                }}
                className='flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground'
              >
                Insert
              </Button>
              <Button
                size='sm'
                variant='outline'
                onClick={e => {
                  e.stopPropagation()
                  onDiscardResponse()
                }}
                className='flex-1'
              >
                Discard
              </Button>
            </div>
            <Button
              size='sm'
              variant='ghost'
              onClick={e => {
                e.stopPropagation()
                onTryAgain()
              }}
              className='w-full mt-2 text-xs'
            >
              Try again
            </Button>
          </div>
        )}

        {/* Input Field */}
        {!showResponse && !isLoading && (
          <div className='p-3 border-b'>
            <Input
              placeholder='Ask AI anything...'
              value={input}
              onChange={e => onInputChange(e.target.value)}
              onKeyDown={e => {
                e.stopPropagation()
                if (e.key === 'Enter') {
                  onSubmit()
                }
              }}
              onClick={e => e.stopPropagation()}
              className='text-sm'
              autoFocus
            />
            <Button
              size='sm'
              onClick={e => {
                e.stopPropagation()
                onSubmit()
              }}
              className='w-full mt-2 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground'
              disabled={!input}
            >
              Apply
            </Button>
          </div>
        )}

        {/* Quick Actions */}
        {!showResponse && !isLoading && (
          <>
            {AI_ACTIONS.map(action => (
              <DropdownMenuItem
                key={action.action}
                onClick={e => {
                  e.stopPropagation()
                  e.preventDefault()
                  onAction(action.action)
                }}
                className='px-3 py-2 cursor-pointer'
                onSelect={e => e.preventDefault()}
              >
                <div className='flex items-center gap-2'>
                  <span className={action.color}>{getIcon(action.icon)}</span>
                  <span>{action.label}</span>
                </div>
              </DropdownMenuItem>
            ))}
            {AI_ACTIONS.length > 4 && <DropdownMenuSeparator />}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
