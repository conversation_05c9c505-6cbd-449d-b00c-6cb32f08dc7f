'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Plus, Trash2, ArrowUp, ArrowDown } from 'lucide-react'

interface SceneActionsProps {
  sceneIndex: number
  totalScenes: number
  onAddScene: () => void
  onDeleteScene: () => void
  onMoveUp: () => void
  onMoveDown: () => void
}

export function SceneActions({
  sceneIndex,
  totalScenes,
  onAddScene,
  onDeleteScene,
  onMoveUp,
  onMoveDown,
}: SceneActionsProps) {
  return (
    <div className='flex justify-center gap-2 md:gap-3 lg:gap-4 mt-3 md:mt-4 px-2'>
      <Button
        variant='ghost'
        size='icon'
        onClick={onAddScene}
        title='Add scene after'
        className='h-8 w-8 md:h-9 md:w-9'
      >
        <Plus className='h-4 w-4' />
      </Button>
      <Button
        variant='ghost'
        size='icon'
        onClick={onDeleteScene}
        title='Delete scene'
        disabled={totalScenes === 1}
        className='h-8 w-8 md:h-9 md:w-9'
      >
        <Trash2 className='h-4 w-4' />
      </Button>
      <Button
        variant='ghost'
        size='icon'
        onClick={onMoveUp}
        title='Move up'
        disabled={sceneIndex === 0}
        className='h-8 w-8 md:h-9 md:w-9'
      >
        <ArrowUp className='h-4 w-4' />
      </Button>
      <Button
        variant='ghost'
        size='icon'
        onClick={onMoveDown}
        title='Move down'
        disabled={sceneIndex === totalScenes - 1}
        className='h-8 w-8 md:h-9 md:w-9'
      >
        <ArrowDown className='h-4 w-4' />
      </Button>
    </div>
  )
}
