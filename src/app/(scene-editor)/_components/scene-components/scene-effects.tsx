'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  TRANSITION_OPTIONS,
  ANIMATION_OPTIONS,
} from '../constants/scene-options'
import { useDebounceSceneUpdate } from '@/hooks/use-debounced-scene-updates'
import type { Scene, TransitionType } from '@/types/video'

interface SceneEffectsProps {
  scene: Scene
  onUpdateScene: (updates: Partial<Scene>) => void
}

export function SceneEffects({ scene, onUpdateScene }: SceneEffectsProps) {
  // Local state for responsive UI
  const [localEffectDuration, setLocalEffectDuration] = useState(
    scene.media?.effectDuration || 1.5
  )

  // Debounced update hook
  const debouncedUpdateScene = useDebounceSceneUpdate(scene.id, 300)

  // Sync local state with scene changes
  useEffect(() => {
    setLocalEffectDuration(scene.media?.effectDuration || 1.5)
  }, [scene.media?.effectDuration])

  return (
    <div className='mt-4 space-y-4'>
      {/* Transition and Animation Row */}
      <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
        {/* Transition */}
        <div>
          <Label className='text-xs font-medium text-muted-foreground mb-2 block'>
            Transition
          </Label>
          <Select
            value={scene.media?.transition || 'fade'}
            onValueChange={value => {
              if (scene.media) {
                onUpdateScene({
                  media: {
                    ...scene.media,
                    transition: value as TransitionType,
                  },
                })
              }
            }}
          >
            <SelectTrigger className='h-10 text-sm'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {TRANSITION_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Animation */}
        <div>
          <Label className='text-xs font-medium text-muted-foreground mb-2 block'>
            Animation(s)
          </Label>
          <div className='flex gap-2'>
            <div className='flex-1'>
              <Select
                value={scene.media?.kenBurns || 'none'}
                onValueChange={value => {
                  if (scene.media) {
                    onUpdateScene({
                      media: {
                        ...scene.media,
                        kenBurns: value as 'none' | 'zoom-in' | 'zoom-out',
                      },
                    })
                  }
                }}
              >
                <SelectTrigger className='h-10 text-sm'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ANIMATION_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className='w-full'>
              <input
                type='number'
                min='0.5'
                max='5'
                step='0.1'
                value={localEffectDuration}
                onChange={e => {
                  const newDuration = parseFloat(e.target.value)
                  // Update local state immediately for responsive UI
                  setLocalEffectDuration(newDuration)
                  // Debounce the store update
                  if (scene.media) {
                    debouncedUpdateScene({
                      media: {
                        ...scene.media,
                        effectDuration: newDuration,
                      },
                    })
                  }
                }}
                className='w-full h-10 text-sm text-center border border-input rounded-md px-2 bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                onClick={e => e.stopPropagation()}
                placeholder='1.5'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
