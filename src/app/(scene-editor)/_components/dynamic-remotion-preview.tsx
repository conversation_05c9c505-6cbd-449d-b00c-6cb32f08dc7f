'use client'

import React, { useEffect, useRef, useState, Suspense } from 'react'
import { Scene } from '@/types/video'
import { PlayerRef } from '@remotion/player'
import { VideoOrientation, useVideoStore } from '@/store/video-store'
import { RemotionErrorBoundary } from '@/lib/remotion'

// Lazy load RemotionPreview for better performance
const RemotionPreview = React.lazy(() =>
  import('./remotion').then(module => ({ default: module.RemotionPreview }))
)

// Loading component for Suspense
const RemotionPreviewLoading: React.FC<{ width: number; height: number }> = ({
  width,
  height,
}) => (
  <div
    className='flex items-center justify-center bg-muted/30 rounded-lg border-2 border-dashed border-muted-foreground/25 animate-pulse'
    style={{ width, height }}
  >
    <div className='text-center'>
      <div className='text-lg font-medium mb-1 text-muted-foreground'>
        Loading preview...
      </div>
      <div className='text-sm text-muted-foreground/70'>
        Preparing video components
      </div>
    </div>
  </div>
)

interface DynamicRemotionPreviewProps {
  scenes: Scene[]
  subtitlePosition?: { x: number; y: number }
  orientation: VideoOrientation
  playerRef?: React.RefObject<PlayerRef | null>
  isFullscreen?: boolean
}

export function DynamicRemotionPreview({
  scenes,
  subtitlePosition,
  orientation,
  playerRef,
  isFullscreen,
}: DynamicRemotionPreviewProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const { project } = useVideoStore()
  const [dimensions, setDimensions] = useState<{
    width: number
    height: number
    compositionWidth: number
    compositionHeight: number
  }>({
    width: 800,
    height: 450,
    compositionWidth: 1920,
    compositionHeight: 1080,
  })

  const validScenes = React.useMemo(
    () =>
      scenes.filter(
        (scene): scene is Scene =>
          typeof scene.duration === 'number' && scene.duration > 0
      ),
    [scenes]
  )

  // For podcast flows, use project duration; for regular flows, use scene durations
  const isPodcastOrAudioFlow = !!project?.speech
  const totalSceneDuration = isPodcastOrAudioFlow
    ? project?.duration
      ? parseFloat(project.duration.toString())
      : 0
    : validScenes.reduce((acc, scene) => acc + (scene.duration || 0), 0)

  // Calculate responsive dimensions based on container size and orientation
  useEffect(() => {
    const calculateDimensions = () => {
      if (!containerRef.current) return

      const container = containerRef.current.parentElement
      if (!container) return

      const containerRect = container.getBoundingClientRect()
      // Allow more space when in fullscreen mode
      const padding = isFullscreen ? 24 : 48
      const availableWidth = containerRect.width - padding
      const availableHeight = containerRect.height - padding

      let aspectRatio: number
      let compositionWidth: number
      let compositionHeight: number

      switch (orientation) {
        case 'landscape':
          aspectRatio = 16 / 9
          compositionWidth = 1920
          compositionHeight = 1080
          break
        case 'square':
          aspectRatio = 1
          compositionWidth = 1080
          compositionHeight = 1080
          break
        case 'portrait':
          aspectRatio = 9 / 16
          compositionWidth = 1080
          compositionHeight = 1920
          break
        default:
          aspectRatio = 16 / 9
          compositionWidth = 1920
          compositionHeight = 1080
      }

      let width: number
      let height: number

      // If in fullscreen, prioritize using available space
      if (isFullscreen) {
        if (orientation === 'landscape') {
          height = availableHeight * 0.9
          width = height * aspectRatio
          if (width > availableWidth * 0.95) {
            width = availableWidth * 0.95
            height = width / aspectRatio
          }
        } else if (orientation === 'portrait') {
          width = availableWidth * 0.4
          height = width / aspectRatio
          if (height > availableHeight * 0.9) {
            height = availableHeight * 0.9
            width = height * aspectRatio
          }
        } else {
          // Square
          const size = Math.min(availableHeight * 0.9, availableWidth * 0.6)
          width = size
          height = size
        }
      } else {
        // For landscape, start with width constraint
        width = Math.min(availableWidth * 0.95, 1200)
        height = width / aspectRatio
        if (height > availableHeight * 0.85) {
          height = availableHeight * 0.85
          width = height * aspectRatio
        }
      }

      // Ensure minimum sizes
      const minWidth = Math.min(320, availableWidth * 0.6)
      const minHeight = minWidth / aspectRatio

      width = Math.max(width, minWidth)
      height = Math.max(height, minHeight)

      // Final safety bounds
      width = Math.min(width, availableWidth)
      height = Math.min(height, availableHeight)

      setDimensions({
        width: Math.round(width),
        height: Math.round(height),
        compositionWidth,
        compositionHeight,
      })
    }

    calculateDimensions()

    // Recalculate on window resize
    const handleResize = () => {
      setTimeout(calculateDimensions, 100) // Debounce
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [orientation, isFullscreen])

  if (totalSceneDuration === 0) {
    return (
      <div
        ref={containerRef}
        className='flex items-center justify-center text-gray-400 bg-muted/30 rounded-lg border-2 border-dashed border-muted-foreground/25'
        style={{
          width: dimensions.width,
          height: dimensions.height,
          maxWidth: '100%',
          maxHeight: '100%',
        }}
      >
        <div className='text-center'>
          <div className='text-lg font-medium mb-1'>No video content</div>
          <div className='text-sm'>Add scenes with voiceover to preview</div>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      style={{
        maxWidth: '100%',
        maxHeight: '100%',
        position: 'relative',
        width: dimensions.width,
        height: dimensions.height,
      }}
    >
      <RemotionErrorBoundary>
        <Suspense
          fallback={
            <RemotionPreviewLoading
              width={dimensions.width}
              height={dimensions.height}
            />
          }
        >
          <RemotionPreview
            scenes={validScenes}
            subtitlePosition={subtitlePosition}
            width={dimensions.width}
            height={dimensions.height}
            compositionWidth={dimensions.compositionWidth}
            compositionHeight={dimensions.compositionHeight}
            playerRef={playerRef}
            isFullscreen={isFullscreen}
          />
        </Suspense>
      </RemotionErrorBoundary>
    </div>
  )
}
