'use client'

import { useEffect } from 'react'
import { useVideoStore } from '@/store/video-store'

export function KeyboardShortcuts() {
  const { undo, redo, canUndo, canRedo } = useVideoStore()

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if we're in an input, textarea, or other editable element
      const target = e.target as HTMLElement
      const isEditableElement =
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.isContentEditable

      // Don't capture shortcuts in editable elements
      if (isEditableElement) return

      // Handle undo/redo shortcuts
      if (e.ctrlKey || e.metaKey) {
        // Undo: Ctrl+Z or Command+Z
        if (e.key === 'z' && !e.shiftKey) {
          e.preventDefault()
          if (canUndo()) undo()
        }

        // Redo: Ctrl+Y or Ctrl+Shift+Z or Command+Shift+Z
        if (e.key === 'y' || (e.key === 'z' && e.shiftKey)) {
          e.preventDefault()
          if (canRedo()) redo()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [undo, redo, canUndo, canRedo])

  // This component doesn't render anything, it just attaches keyboard handlers
  return null
}
