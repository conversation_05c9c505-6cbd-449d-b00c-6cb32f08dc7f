'use client'

import { useState } from 'react'
import { useOpenAITextTransform } from '@/hooks/useOpenAITextTransform'
import type { Scene } from '@/types/video'

export function useAIAssistant(
  updateScene: (sceneId: string, updates: Partial<Scene>) => void
) {
  const [aiInputs, setAiInputs] = useState<{ [sceneId: string]: string }>({})
  const [aiDropdownOpen, setAiDropdownOpen] = useState<{
    [sceneId: string]: boolean
  }>({})
  const [aiResponses, setAiResponses] = useState<{ [sceneId: string]: string }>(
    {}
  )
  const [showAiResponse, setShowAiResponse] = useState<{
    [sceneId: string]: boolean
  }>({})
  const [aiLoading, setAiLoading] = useState<{ [sceneId: string]: boolean }>({})

  const openaiTransform = useOpenAITextTransform()

  const handleAiInputChange = (sceneId: string, value: string) => {
    setAiInputs(prev => ({ ...prev, [sceneId]: value }))
  }

  const handleAiDropdownChange = (sceneId: string, open: boolean) => {
    setAiDropdownOpen(prev => ({ ...prev, [sceneId]: open }))
    if (!open) {
      setShowAiResponse(prev => ({ ...prev, [sceneId]: false }))
      setAiResponses(prev => ({ ...prev, [sceneId]: '' }))
      setAiLoading(prev => ({ ...prev, [sceneId]: false }))
    }
  }

  const handleAiAction = async (
    sceneId: string,
    scene: Scene,
    action: 'improve' | 'emojify' | 'longer' | 'shorter' | 'fix' | 'simplify'
  ) => {
    if (!scene.text) return

    setAiLoading(prev => ({ ...prev, [sceneId]: true }))
    setShowAiResponse(prev => ({ ...prev, [sceneId]: false }))

    try {
      const result = await openaiTransform.mutateAsync({
        text: scene.text,
        action,
        customInstruction: aiInputs[sceneId] || undefined,
      })

      setAiResponses(prev => ({ ...prev, [sceneId]: result.transformedText }))
      setShowAiResponse(prev => ({ ...prev, [sceneId]: true }))
    } catch (error) {
      console.error('AI transformation failed:', error)
      setAiResponses(prev => ({
        ...prev,
        [sceneId]: `Error: ${error instanceof Error ? error.message : 'Failed to process text'}`,
      }))
      setShowAiResponse(prev => ({ ...prev, [sceneId]: true }))
    } finally {
      setAiLoading(prev => ({ ...prev, [sceneId]: false }))
    }
  }

  const handleAiSubmit = async (sceneId: string, scene: Scene) => {
    const input = aiInputs[sceneId]
    if (!input || !scene.text) return

    setAiLoading(prev => ({ ...prev, [sceneId]: true }))
    setShowAiResponse(prev => ({ ...prev, [sceneId]: false }))

    try {
      const result = await openaiTransform.mutateAsync({
        text: scene.text,
        action: 'custom',
        customInstruction: input,
      })

      setAiResponses(prev => ({ ...prev, [sceneId]: result.transformedText }))
      setShowAiResponse(prev => ({ ...prev, [sceneId]: true }))
    } catch (error) {
      console.error('AI transformation failed:', error)
      setAiResponses(prev => ({
        ...prev,
        [sceneId]: `Error: ${error instanceof Error ? error.message : 'Failed to process text'}`,
      }))
      setShowAiResponse(prev => ({ ...prev, [sceneId]: true }))
    } finally {
      setAiLoading(prev => ({ ...prev, [sceneId]: false }))
    }
  }

  const handleInsertAiResponse = (sceneId: string) => {
    const response = aiResponses[sceneId]
    if (!response) return

    updateScene(sceneId, {
      text: response,
    })

    // Clear states and close dropdown
    setAiInputs(prev => ({ ...prev, [sceneId]: '' }))
    setAiResponses(prev => ({ ...prev, [sceneId]: '' }))
    setShowAiResponse(prev => ({ ...prev, [sceneId]: false }))
    setAiLoading(prev => ({ ...prev, [sceneId]: false }))
    setAiDropdownOpen(prev => ({ ...prev, [sceneId]: false }))
  }

  const handleDiscardAiResponse = (sceneId: string) => {
    setAiResponses(prev => ({ ...prev, [sceneId]: '' }))
    setShowAiResponse(prev => ({ ...prev, [sceneId]: false }))
    setAiLoading(prev => ({ ...prev, [sceneId]: false }))
  }

  const handleTryAgain = (sceneId: string, scene: Scene) => {
    setShowAiResponse(prev => ({ ...prev, [sceneId]: false }))
    setAiResponses(prev => ({ ...prev, [sceneId]: '' }))
    const input = aiInputs[sceneId]
    if (input) {
      handleAiSubmit(sceneId, scene)
    }
  }

  return {
    aiInputs,
    aiDropdownOpen,
    aiResponses,
    showAiResponse,
    aiLoading,
    handleAiInputChange,
    handleAiDropdownChange,
    handleAiAction,
    handleAiSubmit,
    handleInsertAiResponse,
    handleDiscardAiResponse,
    handleTryAgain,
  }
}
