'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Music2, Volume2, Play, Pause } from 'lucide-react'
import type { MusicTrack } from '@/store/video-store'

interface MusicSettingsProps {
  selectedMusic: MusicTrack | null
  musicVolume: number
  musicEnabled: boolean
  isMusicPlaying: boolean
  onOpenMusicPicker: () => void
  onSetMusicVolume: (volume: number) => void
  onSetMusicEnabled: (enabled: boolean) => void
  onMusicPlayPause: () => void
}

export function MusicSettings({
  selectedMusic,
  musicVolume,
  musicEnabled,
  isMusicPlaying,
  onOpenMusicPicker,
  onSetMusicVolume,
  onSetMusicEnabled,
  onMusicPlayPause,
}: MusicSettingsProps) {
  return (
    <div className='rounded-xl border bg-card p-4 space-y-4'>
      <div className='flex items-center justify-between'>
        <div className='font-semibold text-base'>Music</div>
        <div className='flex items-center gap-2'>
          <Checkbox
            id='disable-music'
            checked={!musicEnabled}
            onCheckedChange={checked =>
              typeof checked === 'boolean' && onSetMusicEnabled(!checked)
            }
          />
          <label
            htmlFor='disable-music'
            className='text-xs text-muted-foreground select-none'
          >
            Disable music
          </label>
        </div>
      </div>

      {/* Music Selection Card */}
      <div className='space-y-3'>
        <div className='flex items-center gap-2 mb-2'>
          <Label className='text-sm font-medium'>Background Music</Label>
          <Button
            variant='outline'
            size='sm'
            className='h-6 px-2 text-xs gap-1'
            onClick={onOpenMusicPicker}
          >
            <Music2 className='w-3 h-3' />
            <span className='w-2 h-2 bg-primary rounded-full'></span>
            {selectedMusic?.title || 'Happy to Be Happy - Instrumental Version'}
          </Button>
          {/* Play/Pause Button for Background Music */}
          {selectedMusic?.previewUrl && (
            <Button
              variant='ghost'
              size='sm'
              className='h-6 w-6 p-0'
              onClick={onMusicPlayPause}
              title={isMusicPlaying ? 'Pause music' : 'Play music'}
            >
              {isMusicPlaying ? (
                <Pause className='w-3 h-3' />
              ) : (
                <Play className='w-3 h-3' />
              )}
            </Button>
          )}
        </div>

        {/* Volume Control */}
        <div className='flex items-center gap-3'>
          <span className='text-muted-foreground flex items-center gap-1'>
            <span className='sr-only'>Music volume</span>
            <Volume2 className='h-3 w-3 text-muted-foreground' />
          </span>
          <input
            type='range'
            min='0'
            max='100'
            value={musicVolume}
            onChange={e => onSetMusicVolume(Number(e.target.value))}
            className='flex-1 accent-primary'
          />
          <span className='w-10 text-right text-xs text-muted-foreground'>
            {musicVolume}%
          </span>
        </div>
      </div>
    </div>
  )
}
