'use client'

import { useState } from 'react'
import { Tabs, TabsContent } from '@/components/ui/tabs'
import { StylesTab, CustomTab } from './caption-tabs'

export function CaptionSettings() {
  const [activeTab, setActiveTab] = useState('styles')

  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  const switchToStylesTab = () => {
    setActiveTab('styles')
  }

  return (
    <div className='h-full flex flex-col'>
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className='flex-1 flex flex-col'
      >
        {/* Compact Icon-Based Tabs */}
        {/* <div className='flex items-center gap-1 mb-4'>
          <TabsList className='p-1 h-8 w-fit bg-muted rounded-md'>
            <TabsTrigger
              value='styles'
              className='h-6 px-2 text-xs font-medium data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=inactive]:hover:bg-muted-foreground/10 transition-all flex items-center gap-1.5 rounded-sm border-0 data-[state=active]:shadow-sm'
            >
              <Palette className='w-3 h-3' />
              <span>Styles</span>
            </TabsTrigger>
            <TabsTrigger
              value='custom'
              className='h-6 px-2 text-xs font-medium data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=inactive]:hover:bg-muted-foreground/10 transition-all flex items-center gap-1.5 rounded-sm border-0 data-[state=active]:shadow-sm'
            >
              <Wrench className='w-3 h-3' />
              <span>Custom</span>
            </TabsTrigger>
          </TabsList>
        </div> */}

        <TabsContent value='styles' className='flex-1 mt-0'>
          <StylesTab />
        </TabsContent>

        <TabsContent value='custom' className='flex-1 mt-0'>
          <CustomTab onStyleCreated={switchToStylesTab} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
