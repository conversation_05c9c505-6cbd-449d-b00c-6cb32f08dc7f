'use client'

import { useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
// Select components are intentionally not imported until animations UI is enabled
import { Slider } from '@/components/ui/slider'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'

import {
  Bold,
  Italic,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Minus,
  Plus,
  Zap,
} from 'lucide-react'
import { FontCombobox } from '@/components/ui/font-combobox'
import { ColorPicker } from '@/components/ui/color-picker'
import { useCaptionStylesStore } from '@/store'

const FONT_FAMILIES = [
  { value: 'Inter', label: 'Inter (Sans-serif)', googleFont: 'Inter' },
  { value: 'Roboto', label: 'Roboto', googleFont: 'Roboto' },
  { value: 'Open Sans', label: 'Open Sans', googleFont: 'Open+Sans' },
  { value: 'Montserrat', label: 'Montserrat', googleFont: 'Montserrat' },
  { value: 'Poppins', label: 'Poppins', googleFont: 'Poppins' },
  {
    value: 'Playfair Display',
    label: 'Playfair Display (Serif)',
    googleFont: 'Playfair+Display',
  },
  { value: 'Oswald', label: 'Oswald', googleFont: 'Oswald' },
  { value: 'Lato', label: 'Lato', googleFont: 'Lato' },
  { value: 'Nunito', label: 'Nunito', googleFont: 'Nunito' },
  {
    value: 'Source Sans Pro',
    label: 'Source Sans Pro',
    googleFont: 'Source+Sans+Pro',
  },
]

// const ANIMATIONS = [
//   { value: 'none', label: 'No Animation' },
//   { value: 'fade', label: 'Fade In' },
//   { value: 'slide-up', label: 'Slide Up' },
//   { value: 'bounce', label: 'Bounce' },
//   { value: 'typewriter', label: 'Typewriter' },
//   { value: 'color-up', label: 'Color Up' },
//   { value: 'bounce-out', label: 'Bounce Out' },
// ]

interface CustomTabProps {
  onStyleCreated: () => void
}

export function CustomTab({}: CustomTabProps) {
  const { liveCustomStyle, updateLiveCustomStyle } = useCaptionStylesStore()

  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // Load Google Fonts dynamically
  useEffect(() => {
    const fontConfig = FONT_FAMILIES.find(
      f => f.value === liveCustomStyle.fontFamily
    )
    if (fontConfig?.googleFont) {
      // Remove existing font link if any
      const existingLink = document.querySelector('#dynamic-google-font')
      if (existingLink) {
        existingLink.remove()
      }

      // Add new font link
      const link = document.createElement('link')
      link.id = 'dynamic-google-font'
      link.rel = 'stylesheet'
      link.href = `https://fonts.googleapis.com/css2?family=${fontConfig.googleFont}:wght@400;700&display=swap`
      document.head.appendChild(link)
    }
  }, [liveCustomStyle.fontFamily])

  return (
    <div className='h-full flex flex-col'>
      <ScrollArea className='flex-1 w-full' ref={scrollAreaRef}>
        <div className='space-y-6 pr-4'>
          {/* Typography */}
          <div className='space-y-3'>
            <h4 className='text-sm font-semibold'>Typography</h4>

            {/* Typography Controls - Two Row Layout */}
            <div className='space-y-2'>
              {/* First Row: Font Family and Font Size */}
              <div className='grid grid-cols-2 gap-3'>
                <div className='space-y-1'>
                  <Label className='text-xs'>Font Family</Label>
                  <FontCombobox
                    selectedFont={liveCustomStyle.fontFamily}
                    onFontSelect={family =>
                      updateLiveCustomStyle({ fontFamily: family })
                    }
                    className='h-7'
                    placeholder='Search fonts...'
                  />
                </div>

                <div className='space-y-1'>
                  <Label className='text-xs'>Font Size</Label>
                  <div className='flex items-center gap-1 h-7 border rounded'>
                    <Button
                      variant='ghost'
                      size='sm'
                      className='h-5 w-5 p-0 hover:bg-muted rounded-none'
                      onClick={() =>
                        updateLiveCustomStyle({
                          fontSize: Math.max(20, liveCustomStyle.fontSize - 2),
                        })
                      }
                    >
                      <Minus className='w-3 h-3' />
                    </Button>
                    <Input
                      type='number'
                      value={liveCustomStyle.fontSize}
                      onChange={e => {
                        const value = parseInt(e.target.value) || 50
                        const clampedValue = Math.max(20, Math.min(120, value))
                        updateLiveCustomStyle({ fontSize: clampedValue })
                      }}
                      className='h-5 border-0 text-xs text-center px-1 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent'
                      min={20}
                      max={120}
                    />
                    <span className='text-xs text-muted-foreground pr-1'>
                      px
                    </span>
                    <Button
                      variant='ghost'
                      size='sm'
                      className='h-5 w-5 p-0 hover:bg-muted rounded-none'
                      onClick={() =>
                        updateLiveCustomStyle({
                          fontSize: Math.min(120, liveCustomStyle.fontSize + 2),
                        })
                      }
                    >
                      <Plus className='w-3 h-3' />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Second Row: Style, Alignment, and Shadow */}
              <div className='grid grid-cols-3 gap-3'>
                <div className='space-y-1'>
                  <Label className='text-xs'>Style</Label>
                  <div className='flex gap-1'>
                    <Button
                      variant={
                        liveCustomStyle.fontWeight === 'bold'
                          ? 'default'
                          : 'outline'
                      }
                      size='sm'
                      className={cn('h-7 w-7 p-0')}
                      onClick={() =>
                        updateLiveCustomStyle({
                          fontWeight:
                            liveCustomStyle.fontWeight === 'bold'
                              ? 'normal'
                              : 'bold',
                        })
                      }
                    >
                      <Bold className='w-3 h-3' />
                    </Button>
                    <Button
                      variant={
                        liveCustomStyle.fontStyle === 'italic'
                          ? 'default'
                          : 'outline'
                      }
                      size='sm'
                      className={cn('h-7 w-7 p-0')}
                      onClick={() =>
                        updateLiveCustomStyle({
                          fontStyle:
                            liveCustomStyle.fontStyle === 'italic'
                              ? 'normal'
                              : 'italic',
                        })
                      }
                    >
                      <Italic className='w-3 h-3' />
                    </Button>
                  </div>
                </div>

                <div className='space-y-1'>
                  <Label className='text-xs'>Alignment</Label>
                  <div className='flex gap-1'>
                    <Button
                      variant={
                        liveCustomStyle.textAlign === 'left'
                          ? 'default'
                          : 'outline'
                      }
                      size='sm'
                      className={cn('h-7 w-7 p-0')}
                      onClick={() =>
                        updateLiveCustomStyle({ textAlign: 'left' })
                      }
                    >
                      <AlignLeft className='w-3 h-3' />
                    </Button>
                    <Button
                      variant={
                        liveCustomStyle.textAlign === 'center'
                          ? 'default'
                          : 'outline'
                      }
                      size='sm'
                      className={cn('h-7 w-7 p-0')}
                      onClick={() =>
                        updateLiveCustomStyle({ textAlign: 'center' })
                      }
                    >
                      <AlignCenter className='w-3 h-3' />
                    </Button>
                    <Button
                      variant={
                        liveCustomStyle.textAlign === 'right'
                          ? 'default'
                          : 'outline'
                      }
                      size='sm'
                      className={cn('h-7 w-7 p-0')}
                      onClick={() =>
                        updateLiveCustomStyle({ textAlign: 'right' })
                      }
                    >
                      <AlignRight className='w-3 h-3' />
                    </Button>
                  </div>
                </div>

                <div className='space-y-1'>
                  <Label className='text-xs'>Shadow</Label>
                  <Button
                    variant={liveCustomStyle.textShadow ? 'default' : 'outline'}
                    size='sm'
                    className={cn('h-7 w-7 p-0')}
                    onClick={() =>
                      updateLiveCustomStyle({
                        textShadow: !liveCustomStyle.textShadow,
                      })
                    }
                  >
                    <Zap className='w-3 h-3' />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Colors */}
          <div className='space-y-3'>
            <h4 className='text-sm font-semibold'>Colors</h4>

            {/* Colors Grid - 2x2 */}
            <div className='grid grid-cols-2 gap-2'>
              <div className='space-y-1'>
                <Label className='text-xs'>Text</Label>
                <ColorPicker
                  value={liveCustomStyle.textColor}
                  onChange={color =>
                    updateLiveCustomStyle({ textColor: color })
                  }
                  className='h-7'
                />
              </div>

              <div className='space-y-1'>
                <Label className='text-xs'>Highlight</Label>
                <ColorPicker
                  value={liveCustomStyle.highlightColor}
                  onChange={color =>
                    updateLiveCustomStyle({ highlightColor: color })
                  }
                  className='h-7'
                />
              </div>

              <div className='space-y-1'>
                <Label className='text-xs'>Background</Label>
                <ColorPicker
                  value={liveCustomStyle.backgroundColor}
                  onChange={color =>
                    updateLiveCustomStyle({ backgroundColor: color })
                  }
                  className='h-7'
                />
              </div>

              <div className='space-y-1'>
                <Label className='text-xs'>
                  Opacity
                  <span className='ml-1 text-xs text-muted-foreground'>
                    {liveCustomStyle.backgroundOpacity}%
                  </span>
                </Label>
                <Slider
                  value={[liveCustomStyle.backgroundOpacity]}
                  onValueChange={([value]) =>
                    updateLiveCustomStyle({ backgroundOpacity: value })
                  }
                  max={100}
                  step={5}
                  className='h-7 py-2'
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Effects & Layout */}
          <div className='space-y-3'>
            <h4 className='text-sm font-semibold'>Effects & Layout</h4>

            {/* Animation */}
            {/* TODO: Re-enable when animations are ready */}
            {/* <div className='space-y-1'>
              <Label className='text-xs'>Animation</Label>
              <Select
                value={liveCustomStyle.animation}
                onValueChange={value =>
                  updateLiveCustomStyle({
                    animation: value as typeof liveCustomStyle.animation,
                  })
                }
              >
                <SelectTrigger className='h-7 text-xs'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ANIMATIONS.map(anim => (
                    <SelectItem key={anim.value} value={anim.value}>
                      {anim.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div> */}

            {/* Layout Controls - Grid */}
            <div className='grid grid-cols-3 gap-2'>
              <div className='space-y-1'>
                <Label className='text-xs'>
                  Padding
                  <span className='ml-1 text-xs text-muted-foreground'>
                    {liveCustomStyle.padding}px
                  </span>
                </Label>
                <Slider
                  value={[liveCustomStyle.padding]}
                  onValueChange={([value]) =>
                    updateLiveCustomStyle({ padding: value })
                  }
                  max={50}
                  step={2}
                  className='h-7 py-2'
                />
              </div>

              <div className='space-y-1'>
                <Label className='text-xs'>
                  Radius
                  <span className='ml-1 text-xs text-muted-foreground'>
                    {liveCustomStyle.borderRadius}px
                  </span>
                </Label>
                <Slider
                  value={[liveCustomStyle.borderRadius]}
                  onValueChange={([value]) =>
                    updateLiveCustomStyle({ borderRadius: value })
                  }
                  max={30}
                  step={2}
                  className='h-7 py-2'
                />
              </div>

              <div className='space-y-1'>
                <Label className='text-xs'>
                  Max Width
                  <span className='ml-1 text-xs text-muted-foreground'>
                    {liveCustomStyle.maxWidth}%
                  </span>
                </Label>
                <Slider
                  value={[liveCustomStyle.maxWidth]}
                  onValueChange={([value]) =>
                    updateLiveCustomStyle({ maxWidth: value })
                  }
                  min={50}
                  max={100}
                  step={5}
                  className='h-7 py-2'
                />
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
