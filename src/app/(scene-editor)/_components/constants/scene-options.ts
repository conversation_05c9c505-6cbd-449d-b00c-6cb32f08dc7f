export const TRANSITION_OPTIONS = [
  { value: 'none', label: 'None' },
  { value: 'random', label: 'Random' },

  // Fade Transitions
  { value: 'fade', label: 'Fade' },
  { value: 'fadeblack', label: 'Fade to Black' },
  { value: 'fadewhite', label: 'Fade to White' },

  // Slide Transitions
  { value: 'slideleft', label: 'Slide Left' },
  { value: 'slideright', label: 'Slide Right' },
  { value: 'slideup', label: 'Slide Up' },
  { value: 'slidedown', label: 'Slide Down' },

  // Smooth Slide Transitions
  { value: 'smoothleft', label: 'Smooth Left' },
  { value: 'smoothright', label: 'Smooth Right' },
  { value: 'smoothup', label: 'Smooth Up' },
  { value: 'smoothdown', label: 'Smooth Down' },

  // Wipe Transitions
  { value: 'wipeleft', label: 'Wipe Left' },
  { value: 'wiperight', label: 'Wipe Right' },
  { value: 'wipeup', label: 'Wipe Up' },
  { value: 'wipedown', label: 'Wipe Down' },

  // Circle Transitions
  { value: 'circleopen', label: 'Circle Open' },
  { value: 'circleclose', label: 'Circle Close' },
  { value: 'circlecrop', label: 'Circle Crop' },

  // Rectangle/Horizontal/Vertical
  { value: 'rectcrop', label: 'Rectangle Crop' },
  { value: 'horzopen', label: 'Horizontal Open' },
  { value: 'horzclose', label: 'Horizontal Close' },
  { value: 'vertopen', label: 'Vertical Open' },
  { value: 'vertclose', label: 'Vertical Close' },

  // Special Effects
  { value: 'distance', label: 'Distance/Scale' },
  { value: 'dissolve', label: 'Dissolve' },
  { value: 'pixelize', label: 'Pixelize' },
  { value: 'radial', label: 'Radial Wipe' },
] as const

export const ANIMATION_OPTIONS = [
  { value: 'none', label: 'None' },
  { value: 'zoom-in', label: 'Zoom In' },
  { value: 'zoom-out', label: 'Zoom Out' },
] as const

export const AI_ACTIONS = [
  {
    action: 'improve' as const,
    label: 'Improve writing',
    icon: 'sparkles',
    color: 'text-blue-500',
  },
  {
    action: 'emojify' as const,
    label: 'Emojify',
    icon: 'emoji',
    color: 'text-yellow-500',
  },
  {
    action: 'longer' as const,
    label: 'Make longer',
    icon: 'arrow-up',
    color: 'text-green-500',
  },
  {
    action: 'shorter' as const,
    label: 'Make shorter',
    icon: 'arrow-down',
    color: 'text-orange-500',
  },
  {
    action: 'fix' as const,
    label: 'Fix spelling & grammar',
    icon: 'check-circle',
    color: 'text-red-500',
  },
  {
    action: 'simplify' as const,
    label: 'Simplify language',
    icon: 'diamond',
    color: 'text-purple-500',
  },
] as const

export const CAPTION_ANIMATIONS = [
  { value: 'none', label: 'No animation', active: true },
  { value: 'colorup', label: 'Color up', active: false },
  { value: 'bounceout', label: 'Bounce out', active: false },
  { value: 'typewriter', label: 'Typewriter', active: false },
] as const

export const CAPTION_STYLES = [
  { label: 'Default', color: 'text-foreground', active: false, italic: false },
  { label: 'EXPERIENCE', color: 'text-red-500', active: true, italic: false },
  {
    label: 'New content',
    color: 'text-green-600',
    active: false,
    italic: false,
  },
  { label: 'SHOP NOW', color: 'text-yellow-600', active: false, italic: false },
  {
    label: 'Village Style',
    color: 'text-green-700',
    active: false,
    italic: true,
  },
  { label: 'Best life', color: 'text-blue-700', active: false, italic: false },
  {
    label: 'TRAVEL TIPS',
    color: 'text-teal-600',
    active: false,
    italic: false,
  },
  { label: 'I love it', color: 'text-pink-500', active: false, italic: false },
] as const

export const FONT_OPTIONS = [
  { value: 'bangers', label: 'Bangers' },
  { value: 'inter', label: 'Inter' },
  { value: 'roboto', label: 'Roboto' },
] as const

export const COLOR_PALETTE = [
  { value: '#ef4444', color: 'bg-red-500' },
  { value: '#fde047', color: 'bg-yellow-300' },
  { value: '#ffffff', color: 'bg-white' },
  { value: 'transparent', color: 'bg-transparent' },
] as const
