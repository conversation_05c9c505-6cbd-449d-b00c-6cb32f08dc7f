'use client'
import React, { useCallback } from 'react'
import { Player, PlayerRef } from '@remotion/player'
import { Scene } from '@/types/video'
import { UnifiedComposition } from '@/lib/remotion'
import { useCaptionStylesStore } from '@/store'
import { useVideoStore } from '@/store/video-store'
import { useWebAudioErrorHandler } from '@/lib/remotion/hooks/useWebAudioErrorHandler'

// For browser preview only
export const RemotionPreview: React.FC<{
  scenes: Scene[]
  subtitlePosition?: { x: number; y: number }
  width?: number
  height?: number
  compositionWidth?: number
  compositionHeight?: number
  playerRef?: React.RefObject<PlayerRef | null>
  isFullscreen?: boolean
}> = ({
  scenes,
  subtitlePosition: initialPosition,
  width = 480,
  height = 270,
  compositionWidth = 960,
  compositionHeight = 540,
  playerRef,
  isFullscreen = false,
}) => {
  const { getEffectiveStyle } = useCaptionStylesStore()
  const { project } = useVideoStore()
  const selectedCaptionStyle = getEffectiveStyle()

  // WebAudio error handling for production stability
  const { handleAudioError } = useWebAudioErrorHandler({
    onError: (error, context) => {
      console.error(`WebAudio error in Remotion preview (${context}):`, error)
      // Could integrate with error reporting service here
    },
    enableRecovery: true,
    maxRetryAttempts: 3,
  })

  const validScenes = React.useMemo(
    () =>
      scenes.filter(
        (scene): scene is Scene =>
          typeof scene.duration === 'number' && scene.duration > 0
      ),
    [scenes]
  )
  const validDurations = React.useMemo(
    () => validScenes.map(scene => scene.duration || 0),
    [validScenes]
  )

  // For podcast flows, use project duration; for regular flows, use scene durations
  const isPodcastOrAudioFlow = !!project?.speech
  const totalSceneDuration = isPodcastOrAudioFlow
    ? project?.duration
      ? parseFloat(project.duration.toString())
      : 0
    : validDurations.reduce((a, b) => a + b, 0)

  // Add state for subtitle position with default at bottom center
  const [subtitlePosition, setSubtitlePosition] = React.useState(
    initialPosition || { x: width / 2, y: height * 0.9 }
  )
  const [isDragging, setIsDragging] = React.useState(false)
  const [startDragPosition, setStartDragPosition] = React.useState({
    x: 0,
    y: 0,
  })
  const containerRef = React.useRef<HTMLDivElement>(null)

  // Update subtitle position if initialPosition changes
  React.useEffect(() => {
    if (initialPosition) {
      setSubtitlePosition(initialPosition)
    }
  }, [initialPosition])

  // Handle mouse down to start dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!containerRef.current) return

    setIsDragging(true)

    // Get the video container bounds for relative positioning
    const containerRect = containerRef.current.getBoundingClientRect()

    setStartDragPosition({
      x: e.clientX - containerRect.left - subtitlePosition.x,
      y: e.clientY - containerRect.top - subtitlePosition.y,
    })
  }

  // Handle mouse move to update position while dragging
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !containerRef.current) return

      // Get the video container bounds for relative positioning
      const containerRect = containerRef.current.getBoundingClientRect()

      // Calculate new position relative to the video container
      const newX = Math.min(
        Math.max(0, e.clientX - containerRect.left - startDragPosition.x),
        width
      )
      const newY = Math.min(
        Math.max(0, e.clientY - containerRect.top - startDragPosition.y),
        height
      )

      setSubtitlePosition({ x: newX, y: newY })
    },
    [
      isDragging,
      startDragPosition.x,
      startDragPosition.y,
      width,
      height,
      setSubtitlePosition,
    ]
  )

  // Handle mouse up to end dragging
  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Add event listeners for mouse move and up when dragging
  React.useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove)
      window.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, handleMouseMove])

  // Store subtitle position in localStorage when it changes
  React.useEffect(() => {
    localStorage.setItem('subtitlePosition', JSON.stringify(subtitlePosition))
  }, [subtitlePosition])

  // Load subtitle position from localStorage on initial render
  React.useEffect(() => {
    const savedPosition = localStorage.getItem('subtitlePosition')
    if (savedPosition && !initialPosition) {
      try {
        const position = JSON.parse(savedPosition)
        setSubtitlePosition(position)
      } catch (e) {
        console.error('Failed to parse saved subtitle position', e)
      }
    }
  }, [initialPosition])

  // Error handler for Player events
  const handlePlayerError = useCallback(
    (error: Error) => {
      handleAudioError(error, 'Remotion Player')
    },
    [handleAudioError]
  )

  // Set up error event listeners on the Player
  React.useEffect(() => {
    const player = playerRef?.current
    if (!player) return

    // Match Remotion Player callback signature which passes `{ detail: { error } }`
    const handleError = (evt: { detail?: { error?: Error } }) => {
      if (evt.detail?.error) {
        handlePlayerError(evt.detail.error)
      }
    }

    // Add error event listener
    ;(
      player as unknown as {
        addEventListener?: (
          type: string,
          listener: (evt: { detail?: { error?: Error } }) => void
        ) => void
      }
    ).addEventListener?.('error', handleError)

    return () => {
      ;(
        player as unknown as {
          removeEventListener?: (
            type: string,
            listener: (evt: { detail?: { error?: Error } }) => void
          ) => void
        }
      ).removeEventListener?.('error', handleError)
    }
  }, [playerRef, handlePlayerError])

  // Render UI depending on total scene duration
  if (totalSceneDuration === 0) {
    return (
      <div
        className='flex items-center justify-center w-full h-full text-gray-400 bg-muted/30 rounded-lg border-2 border-dashed border-muted-foreground/25'
        style={{
          width,
          height,
        }}
      >
        <div className='text-center'>
          <div className='text-lg font-medium mb-1'>No video content</div>
          <div className='text-sm'>Add scenes with voiceover to preview</div>
        </div>
      </div>
    )
  }

  return (
    <div ref={containerRef} style={{ position: 'relative', width, height }}>
      <Player
        ref={playerRef}
        component={UnifiedComposition}
        inputProps={{
          scenes: validScenes,
          subtitlePosition: subtitlePosition,
          captionStyle: selectedCaptionStyle,
          isRenderingContext: false,
        }}
        durationInFrames={Math.round(totalSceneDuration * 30)}
        fps={30}
        compositionWidth={compositionWidth}
        compositionHeight={compositionHeight}
        style={{
          width,
          height,
          borderRadius: 12,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          border: '1px solid hsl(var(--border))',
          zIndex: 1,
        }}
        controls={false}
        loop={false}
        spaceKeyToPlayOrPause={false}
        showPlaybackRateControl={false}
        allowFullscreen={false}
        clickToPlay={false}
        overrideInternalClassName='remotion-player-instance'
        autoPlay={false}
      />

      {/* Invisible draggable area positioned exactly over actual subtitles using same percentage system */}
      {!isFullscreen && (
        <div
          style={{
            position: 'absolute',
            left: `${(subtitlePosition.x / 480) * 100}%`,
            top: `${(subtitlePosition.y / 270) * 100}%`,
            transform: 'translate(-50%, -50%)',
            background: isDragging ? 'rgba(255, 255, 0, 0.2)' : 'transparent',
            border: isDragging ? '1px dashed rgba(255, 255, 0, 0.8)' : 'none',
            width: '200px',
            height: '60px',
            borderRadius: '8px',
            cursor: isDragging ? 'grabbing' : 'grab',
            userSelect: 'none',
            zIndex: 35, // Lower than controls but higher than player
            pointerEvents: 'all',
          }}
          onMouseDown={handleMouseDown}
        />
      )}
    </div>
  )
}
