import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

export interface GoogleFont {
  family: string
  variants: string[]
  subsets: string[]
  category: string
  files: {
    [key: string]: string
  }
}

export async function GET() {
  try {
    const apiKey = process.env.GOOGLE_FONT_API_KEY

    if (!apiKey) {
      return createErrorResponse('Google Fonts API key not configured', 500)
    }

    const url = `https://www.googleapis.com/webfonts/v1/webfonts?key=${apiKey}&sort=popularity`

    const response = await fetch(url, {
      next: { revalidate: 86400 }, // Cache for 24 hours
    })

    if (!response.ok) {
      throw new Error(`Google Fonts API error: ${response.status}`)
    }

    const data = await response.json()

    // Return the fonts with cached response
    return createCachedResponse(
      {
        fonts: data.items as GoogleFont[],
        total: data.items.length,
      },
      CACHE_DURATIONS.FONTS
    )
  } catch (error) {
    console.error('Error fetching Google Fonts:', error)
    return createErrorResponse('Failed to fetch Google Fonts', 500)
  }
}
