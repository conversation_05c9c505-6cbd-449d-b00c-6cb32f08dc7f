import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { projects } from '@/db/schema'
import { eq } from 'drizzle-orm'
import { z } from 'zod'
import { getUserSession } from '@/lib/user-utils'
// Validation schema for project updates
const updateProjectSchema = z.object({
  projectName: z.string().min(1).optional(),
  orientation: z.enum(['landscape', 'portrait', 'square']).optional(),
  coverColor: z.string().optional(),
  coverPic: z.string().nullable().optional(),
  duration: z.number().optional(),
  summary: z.string().optional(),
  music: z
    .object({
      enabled: z.boolean(),
      src: z.string(),
      volume: z.number(),
      duration: z.number(),
      name: z.string(),
    })
    .optional(),
  speech: z
    .object({
      enabled: z.boolean(),
      src: z.string(),
      name: z.string(),
      volume: z.number(),
      transcript: z.object({
        captions: z.array(
          z.object({
            start: z.number(),
            end: z.number(),
            sentence: z.string(),
            wordBoundries: z.array(
              z.object({
                start: z.number(),
                end: z.number(),
                word: z.string(),
              })
            ),
          })
        ),
        status: z.enum(['QUEUED', 'COMPLETED', 'FAILED']),
      }),
    })
    .nullable()
    .optional(),
  backgroundVideo: z
    .object({
      src: z.string(),
      muted: z.boolean(),
    })
    .nullable()
    .optional(),
  captionSettings: z
    .object({
      enabled: z.boolean(),
      fontFamily: z.string(),
      fontSize: z.number(),
      fontWeight: z.string(),
      fontStyle: z.string(),
      textColor: z.string(),
      highlightColor: z.string(),
      backgroundColor: z.string(),
      backgroundOpacity: z.number(),
      textAlign: z.string(),
      textShadow: z.boolean(),
      borderRadius: z.number(),
      padding: z.number(),
      maxWidth: z.number(),
      animation: z.string(),
    })
    .optional(),
  scenes: z.array(z.any()).optional(),
  blogImages: z.array(z.string()).optional(),
  organizationId: z.string().nullable().optional(),
  voiceRegenerations: z.number().optional(),
})

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    const { projectId } = await params

    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      )
    }

    // Fetch project from database
    const project = await db
      .select()
      .from(projects)
      .where(eq(projects.projectId, projectId))
      .limit(1)

    if (!project || project.length === 0) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    const projectData = project[0]

    // Check if user has access to this project based on active organization ID
    let hasAccess = false

    if (activeOrganizationId) {
      // If user has an active organization, they can access projects from that organization
      hasAccess = projectData.organizationId === activeOrganizationId
    } else {
      // If no active organization, they can only access their own projects
      hasAccess = projectData.userId === userId
    }

    if (!hasAccess) {
      return NextResponse.json(
        {
          error:
            'Access denied - You do not have permission to access this project',
        },
        { status: 403 }
      )
    }

    // Transform database format to API format expected by video store
    const apiFormatProject = {
      projectId: projectData.projectId,
      userId: projectData.userId,
      projectName: projectData.projectName,
      method: projectData.method,
      createdAt:
        projectData.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt:
        projectData.updatedAt?.toISOString() || new Date().toISOString(),
      coverColor: projectData.coverColor || '#FFE4E1',
      coverPic: projectData.coverPic,
      orientation: projectData.orientation as
        | 'landscape'
        | 'portrait'
        | 'square',
      duration: projectData.duration
        ? parseFloat(projectData.duration.toString())
        : 0,
      summary: projectData.summary || '',
      music: projectData.music || {
        enabled: false,
        src: '',
        volume: 0.5,
        duration: 0,
        name: '',
      },
      speech: projectData.speech ?? null,
      backgroundVideo: projectData.backgroundVideo ?? null,
      captionSettings: projectData.captionSettings || {
        enabled: true,
        fontFamily: 'Inter',
        fontSize: 32,
        fontWeight: 'bold',
        fontStyle: 'normal',
        textColor: '#ffffff',
        highlightColor: '#3b82f6',
        backgroundColor: '#000000',
        backgroundOpacity: 60,
        textAlign: 'center',
        textShadow: true,
        borderRadius: 8,
        padding: 16,
        maxWidth: 90,
        animation: 'fade',
      },
      scenes: projectData.scenes || [],
      blogImages: projectData.blogImages || [],
      voiceRegenerations: projectData.voiceRegenerations || 0,
    }

    return NextResponse.json(apiFormatProject)
  } catch (error) {
    console.error('Error fetching project:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    const { projectId } = await params

    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = updateProjectSchema.parse(body)

    // Check if project exists and user owns it
    const existingProject = await db
      .select()
      .from(projects)
      .where(eq(projects.projectId, projectId))
      .limit(1)

    if (!existingProject || existingProject.length === 0) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    const projectData = existingProject[0]

    // Check if user has access to this project based on active organization ID
    let hasAccess = false

    if (activeOrganizationId) {
      // If user has an active organization, they can update projects from that organization
      hasAccess = projectData.organizationId === activeOrganizationId
    } else {
      // If no active organization, they can only update their own projects
      hasAccess = projectData.userId === userId
    }

    if (!hasAccess) {
      return NextResponse.json(
        {
          error:
            'Access denied - You do not have permission to update this project',
        },
        { status: 403 }
      )
    }

    // Prepare update data - only include fields that were provided
    const updateData: Partial<typeof projects.$inferInsert> = {
      updatedAt: new Date(),
    }

    // Map API fields to database fields
    if (validatedData.projectName !== undefined) {
      updateData.projectName = validatedData.projectName
    }
    if (validatedData.orientation !== undefined) {
      updateData.orientation = validatedData.orientation
    }
    if (validatedData.coverColor !== undefined) {
      updateData.coverColor = validatedData.coverColor
    }
    if (validatedData.coverPic !== undefined) {
      updateData.coverPic = validatedData.coverPic
    }
    if (validatedData.duration !== undefined) {
      updateData.duration = validatedData.duration.toString()
    }
    if (validatedData.summary !== undefined) {
      updateData.summary = validatedData.summary
    }
    if (validatedData.music !== undefined) {
      updateData.music = validatedData.music
    }
    if (validatedData.speech !== undefined) {
      updateData.speech = validatedData.speech
    }
    if (validatedData.backgroundVideo !== undefined) {
      updateData.backgroundVideo = validatedData.backgroundVideo
    }
    if (validatedData.captionSettings !== undefined) {
      updateData.captionSettings = validatedData.captionSettings
    }
    if (validatedData.scenes !== undefined) {
      updateData.scenes = validatedData.scenes
    }
    if (validatedData.blogImages !== undefined) {
      updateData.blogImages = validatedData.blogImages
    }
    if (validatedData.organizationId !== undefined) {
      updateData.organizationId = validatedData.organizationId
    }
    if (validatedData.voiceRegenerations !== undefined) {
      updateData.voiceRegenerations = validatedData.voiceRegenerations
    }

    // Update the project in database
    const [updatedProject] = await db
      .update(projects)
      .set(updateData)
      .where(eq(projects.projectId, projectId))
      .returning()

    // Return the updated project in the same format as GET
    const apiFormatProject = {
      projectId: updatedProject.projectId,
      userId: updatedProject.userId,
      projectName: updatedProject.projectName,
      method: updatedProject.method,
      createdAt:
        updatedProject.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt:
        updatedProject.updatedAt?.toISOString() || new Date().toISOString(),
      coverColor: updatedProject.coverColor || '#FFE4E1',
      coverPic: updatedProject.coverPic,
      orientation: updatedProject.orientation as
        | 'landscape'
        | 'portrait'
        | 'square',
      duration: updatedProject.duration
        ? parseFloat(updatedProject.duration.toString())
        : 0,
      summary: updatedProject.summary || '',
      music: updatedProject.music || {
        enabled: false,
        src: '',
        volume: 0.5,
        duration: 0,
        name: '',
      },
      speech: updatedProject.speech ?? null,
      backgroundVideo: updatedProject.backgroundVideo ?? null,
      captionSettings: updatedProject.captionSettings || {
        enabled: true,
        fontFamily: 'Inter',
        fontSize: 32,
        fontWeight: 'bold',
        fontStyle: 'normal',
        textColor: '#ffffff',
        highlightColor: '#3b82f6',
        backgroundColor: '#000000',
        backgroundOpacity: 60,
        textAlign: 'center',
        textShadow: true,
        borderRadius: 8,
        padding: 16,
        maxWidth: 90,
        animation: 'fade',
      },
      scenes: updatedProject.scenes || [],
      blogImages: updatedProject.blogImages || [],
      voiceRegenerations: updatedProject.voiceRegenerations || 0,
    }

    return NextResponse.json(apiFormatProject)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Error updating project:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST method for sendBeacon compatibility (behaves same as PUT)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  // sendBeacon uses POST, so we delegate to PUT logic
  return PUT(request, { params })
}
