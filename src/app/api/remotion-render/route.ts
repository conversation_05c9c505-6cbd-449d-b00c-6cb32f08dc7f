import { NextRequest } from 'next/server'
import { renderMedia, getCompositions } from '@remotion/renderer'
import path from 'path'
import fs from 'fs'
import os from 'os'

export const runtime = 'nodejs'

export async function POST(req: NextRequest) {
  const body = await req.json()
  const {
    scenes,
    subtitlePosition,
    orientation = 'landscape',
    compositionWidth = 1920,
    compositionHeight = 1080,
    selectedMusic,
    musicVolume = 50,
    musicEnabled = true,
    captionStyle,
    speech,
  } = body

  // Debug parameters received from the client
  console.log('🔍 API received parameters:', {
    scenes: scenes?.length,
    subtitlePosition,
    orientation,
    compositionWidth,
    compositionHeight,
    selectedMusic: selectedMusic?.title || 'none',
    musicVolume,
    musicEnabled,
    speech: speech ? 'present' : 'none',
  })

  if (!Array.isArray(scenes)) {
    return new Response(
      JSON.stringify({ error: 'Missing scenes array in body' }),
      { status: 400 }
    )
  }

  // Calculate total duration in frames
  const fps = 30
  // For audio/podcast workflows, use speech duration; for text workflows, use scene durations
  const totalSeconds =
    speech?.transcript?.captions?.length > 0
      ? Math.max(
          ...speech.transcript.captions.map(
            (caption: { end: number }) => caption.end
          )
        )
      : scenes.reduce((acc, scene) => acc + (scene.duration || 0), 0)
  const durationInFrames = Math.round(totalSeconds * fps)

  const remotionEntry = path.join(process.cwd(), 'remotion', 'index.tsx')
  const outPath = path.join(
    os.tmpdir(),
    `remotion-render-${orientation}-${Date.now()}.mp4`
  )

  try {
    // Create a temporary file to pass the subtitle position to the renderer
    // This is needed because the subtitle position might not be accessible in the renderer context
    const positionDataPath = path.join(
      os.tmpdir(),
      `subtitle-position-${Date.now()}.json`
    )
    fs.writeFileSync(
      positionDataPath,
      JSON.stringify(subtitlePosition || { x: 240, y: 246 }),
      'utf8'
    )
    console.log(
      `🔍 Wrote subtitle position to ${positionDataPath}:`,
      subtitlePosition
    )

    // Dynamically import @remotion/bundler to avoid edge build issues
    const { bundle } = eval('require')(
      '@remotion/bundler'
    ) as typeof import('@remotion/bundler')

    // Set environment variables to pass subtitle position data and orientation
    process.env.SUBTITLE_POSITION_X = subtitlePosition?.x?.toString() || '50'
    process.env.SUBTITLE_POSITION_Y = subtitlePosition?.y?.toString() || '80'
    process.env.SUBTITLE_POSITION_FILE = positionDataPath
    process.env.VIDEO_ORIENTATION = orientation

    console.log('🔍 Set environment variables:', {
      SUBTITLE_POSITION_X: process.env.SUBTITLE_POSITION_X,
      SUBTITLE_POSITION_Y: process.env.SUBTITLE_POSITION_Y,
      SUBTITLE_POSITION_FILE: process.env.SUBTITLE_POSITION_FILE,
      VIDEO_ORIENTATION: process.env.VIDEO_ORIENTATION,
    })

    const bundleLocation = await bundle(remotionEntry)
    console.log('🔍 Bundle location:', bundleLocation)

    // Prepare input props with subtitle position and orientation
    const inputProps = {
      scenes,
      durationInFrames,
      subtitlePosition: subtitlePosition || { x: 240, y: 246 }, // Use passed position or default
      orientation,
      compositionWidth,
      compositionHeight,
      selectedMusic,
      musicVolume,
      musicEnabled,
      captionStyle,
      speech, // Include speech object for audio/podcast workflows
    }

    // Debug the input props being passed to Remotion
    console.log(
      '🔍 Passing inputProps to Remotion:',
      JSON.stringify({
        ...inputProps,
        scenes: '[scenes array]', // Don't log the entire scenes array
      })
    )

    // Get the composition config
    const comps = await getCompositions(bundleLocation, { inputProps })
    const comp = comps.find(c => c.id === 'main')
    if (!comp) {
      return new Response(JSON.stringify({ error: 'Composition not found' }), {
        status: 404,
      })
    }

    // Override composition dimensions with the passed values
    comp.durationInFrames = durationInFrames
    comp.width = compositionWidth
    comp.height = compositionHeight

    console.log('🔍 Rendering with composition:', {
      id: comp.id,
      durationInFrames: comp.durationInFrames,
      width: comp.width,
      height: comp.height,
      orientation: orientation,
    })

    await renderMedia({
      serveUrl: bundleLocation,
      composition: comp,
      codec: 'h264',
      inputProps,
      outputLocation: outPath,
      overwrite: true,
      // Add a way to pass environment variables to the render process
      envVariables: {
        SUBTITLE_POSITION_X: process.env.SUBTITLE_POSITION_X || '50',
        SUBTITLE_POSITION_Y: process.env.SUBTITLE_POSITION_Y || '80',
        SUBTITLE_POSITION_FILE: process.env.SUBTITLE_POSITION_FILE || '',
        VIDEO_ORIENTATION: process.env.VIDEO_ORIENTATION || 'landscape',
      },
    })

    // Clean up the temporary file
    try {
      fs.unlinkSync(positionDataPath)
    } catch (e) {
      console.error('Error deleting temporary position file:', e)
    }

    const fileBuffer = fs.readFileSync(outPath)
    fs.unlinkSync(outPath)
    return new Response(fileBuffer, {
      headers: {
        'Content-Type': 'video/mp4',
        'Content-Disposition': `attachment; filename="adori-video-${orientation}.mp4"`,
      },
    })
  } catch (err: unknown) {
    let message = 'Unknown error'
    if (err instanceof Error) message = err.message
    console.error('🔍 Render error:', message)
    return new Response(JSON.stringify({ error: message }), { status: 500 })
  }
}
