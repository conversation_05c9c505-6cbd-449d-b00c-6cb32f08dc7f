import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import {
  getActiveOrganizationForUser,
  setActiveOrganizationForUser,
} from '@/lib/organization-utils'

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organization = await getActiveOrganizationForUser(session.user.id)

    if (organization) {
      return NextResponse.json({
        success: true,
        organization: {
          id: organization.organizationId,
          name: organization.organizationName,
          slug: organization.organizationSlug,
          logo: organization.organizationLogo,
          role: organization.role,
        },
      })
    } else {
      return NextResponse.json({
        success: false,
        message: 'No organization found',
      })
    }
  } catch (error) {
    console.error('Error getting active organization:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = await setActiveOrganizationForUser(session.user.id)

    if (organizationId) {
      const organization = await getActiveOrganizationForUser(session.user.id)
      return NextResponse.json({
        success: true,
        organizationId,
        organization: organization
          ? {
              id: organization.organizationId,
              name: organization.organizationName,
              slug: organization.organizationSlug,
              logo: organization.organizationLogo,
              role: organization.role,
            }
          : null,
        message: 'Active organization set successfully',
      })
    } else {
      return NextResponse.json(
        {
          error: 'No organization found for user',
        },
        { status: 404 }
      )
    }
  } catch (error) {
    console.error('Error setting active organization:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}
