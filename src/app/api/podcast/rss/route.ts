import { NextRequest } from 'next/server'
import crypto from 'crypto'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const PODCAST_INDEX_API_KEY = process.env.PODCAST_INDEX_API_KEY
const PODCAST_INDEX_API_SECRET = process.env.PODCAST_INDEX_API_SECRET
const PODCAST_INDEX_BASE_URL = 'https://api.podcastindex.org/api/1.0'

function generateAuthHeaders() {
  if (!PODCAST_INDEX_API_KEY || !PODCAST_INDEX_API_SECRET) {
    console.error('Missing Podcast Index API credentials')
    throw new Error('Podcast Index API credentials not configured')
  }

  const apiHeaderTime = Math.floor(Date.now() / 1000)
  const hashString =
    PODCAST_INDEX_API_KEY + PODCAST_INDEX_API_SECRET + apiHeaderTime.toString()
  const hash = crypto
    .createHash('sha1')
    .update(hashString, 'utf8')
    .digest('hex')

  return {
    'X-Auth-Date': apiHeaderTime.toString(),
    'X-Auth-Key': PODCAST_INDEX_API_KEY,
    Authorization: hash,
    'User-Agent': 'AdoriAI/1.0',
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const url = searchParams.get('url')

    if (!url || url.trim().length === 0) {
      return createErrorResponse('RSS feed URL is required', 400)
    }

    // Validate URL format
    try {
      new URL(url)
    } catch {
      return createErrorResponse('Invalid URL format', 400)
    }

    const headers = generateAuthHeaders()
    const lookupUrl = `${PODCAST_INDEX_BASE_URL}/podcasts/byfeedurl?url=${encodeURIComponent(url)}`

    console.log('Making RSS request to:', lookupUrl)

    const response = await fetch(lookupUrl, {
      method: 'GET',
      headers,
    })

    console.log('RSS Response status:', response.status)

    if (!response.ok) {
      const responseText = await response.text()
      console.error('Podcast Index API error:', {
        status: response.status,
        statusText: response.statusText,
        body: responseText,
      })

      return createErrorResponse(
        `Failed to lookup RSS feed: ${response.status} ${response.statusText}`,
        response.status
      )
    }

    const data = await response.json()

    if (!data.feed) {
      return createErrorResponse('Podcast not found for this RSS URL', 404)
    }

    // Transform the response to match our frontend needs
    const transformedFeed = {
      id: data.feed.id,
      title: data.feed.title,
      author: data.feed.author || data.feed.ownerName,
      description: data.feed.description,
      image: data.feed.image || data.feed.artwork,
      url: data.feed.url,
      link: data.feed.link,
      episodeCount: data.feed.episodeCount,
      language: data.feed.language,
      explicit: data.feed.explicit,
      lastUpdateTime: data.feed.lastUpdateTime,
      categories: data.feed.categories,
    }

    return createCachedResponse(
      {
        status: data.status,
        podcast: transformedFeed,
      },
      CACHE_DURATIONS.PODCAST_RSS
    )
  } catch (error) {
    console.error('RSS lookup error:', error)
    return createErrorResponse('Internal server error', 500)
  }
}
