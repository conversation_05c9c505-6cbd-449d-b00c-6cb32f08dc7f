import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'
import { createErrorResponse } from '@/lib/api-cache'

const PODCAST_INDEX_API_KEY = process.env.PODCAST_INDEX_API_KEY
const PODCAST_INDEX_API_SECRET = process.env.PODCAST_INDEX_API_SECRET
const PODCAST_INDEX_BASE_URL = 'https://api.podcastindex.org/api/1.0'

interface Episode {
  id: number
  title: string
  link: string
  description: string
  guid: string
  datePublished: number
  datePublishedPretty: string
  dateCrawled: number
  enclosureUrl: string
  enclosureType: string
  enclosureLength: number
  duration: number
  explicit: number
  episode: number
  episodeType: string
  season: number
  image: string
  feedItunesId: number
  feedImage: string
  feedId: number
  feedLanguage: string
  chaptersUrl: string
  transcriptUrl: string
}

interface EpisodesResponse {
  status: string
  items: Episode[]
  count: number
  query: string
  description: string
}

function generateAuthHeaders() {
  if (!PODCAST_INDEX_API_KEY || !PODCAST_INDEX_API_SECRET) {
    console.error('Missing Podcast Index API credentials')
    throw new Error('Podcast Index API credentials not configured')
  }

  const apiHeaderTime = Math.floor(Date.now() / 1000)
  const hashString =
    PODCAST_INDEX_API_KEY + PODCAST_INDEX_API_SECRET + apiHeaderTime.toString()
  const hash = crypto
    .createHash('sha1')
    .update(hashString, 'utf8')
    .digest('hex')

  return {
    'X-Auth-Date': apiHeaderTime.toString(),
    'X-Auth-Key': PODCAST_INDEX_API_KEY,
    Authorization: hash,
    'User-Agent': 'AdoriAI/1.0',
  }
}

function formatDuration(seconds: number): string {
  if (!seconds || seconds <= 0) return '0:00'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const feedId = searchParams.get('feedId')
    const max = searchParams.get('max') || '50'
    const start = searchParams.get('start') || '0'
    const since = searchParams.get('since') // Unix timestamp

    if (!feedId) {
      return NextResponse.json(
        { error: 'Feed ID is required' },
        { status: 400 }
      )
    }

    const headers = generateAuthHeaders()
    let episodesUrl = `${PODCAST_INDEX_BASE_URL}/episodes/byfeedid?id=${feedId}&max=${max}&start=${start}`

    if (since) {
      episodesUrl += `&since=${since}`
    }

    console.log('Making episodes request to:', episodesUrl)

    const response = await fetch(episodesUrl, {
      method: 'GET',
      headers,
    })

    console.log('Episodes Response status:', response.status)

    if (!response.ok) {
      const responseText = await response.text()
      console.error('Podcast Index API error:', {
        status: response.status,
        statusText: response.statusText,
        body: responseText,
      })

      return createErrorResponse(
        `Failed to fetch episodes: ${response.status} ${response.statusText}`,
        response.status
      )
    }

    const data: EpisodesResponse = await response.json()

    // Transform episodes to match our frontend needs
    const transformedEpisodes = data.items.map(episode => ({
      id: episode.id,
      title: episode.title,
      description: episode.description,
      link: episode.link,
      guid: episode.guid,
      datePublished: episode.datePublished,
      datePublishedPretty: episode.datePublishedPretty,
      enclosureUrl: episode.enclosureUrl,
      enclosureType: episode.enclosureType,
      enclosureLength: episode.enclosureLength,
      duration: episode.duration,
      durationFormatted: formatDuration(episode.duration),
      explicit: episode.explicit === 1,
      episode: episode.episode,
      season: episode.season,
      image: episode.image || episode.feedImage,
      transcriptUrl: episode.transcriptUrl,
      chaptersUrl: episode.chaptersUrl,
    }))

    return NextResponse.json({
      status: data.status,
      episodes: transformedEpisodes,
      count: data.count,
    })
  } catch (error) {
    console.error('Episodes fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
