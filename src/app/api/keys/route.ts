// import { auth } from '@clerk/nextjs/server'
import { nanoid } from 'nanoid'
import { NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { apiKeys } from '@/db/schema'
import { eq, desc } from 'drizzle-orm'
import { getUserSession } from '@/lib/user-utils'

// GET /api/keys - List all API keys for the current user
export async function GET() {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const userId = session?.user?.id

    const keys = await db.query.apiKeys.findMany({
      where: eq(apiKeys.userId, userId),
      orderBy: [desc(apiKeys.createdAt)],
    })

    return NextResponse.json(keys)
  } catch (error) {
    console.error('[API_KEYS_GET]', error)
    return new NextResponse('Internal Error', { status: 500 })
  }
}

// POST /api/keys - Create a new API key
export async function POST(req: Request) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const userId = session?.user?.id

    const body = await req.json()
    const { name, permissions = [] } = body

    if (!name) {
      return new NextResponse('Name is required', { status: 400 })
    }

    // Ensure permissions is an array
    const permissionsArray = Array.isArray(permissions) ? permissions : []

    const key = nanoid(32) // Generate a 32-character unique key
    const apiKey = await db
      .insert(apiKeys)
      .values({
        id: nanoid(),
        name,
        key,
        userId,
        permissions: permissionsArray,
      })
      .returning()

    return NextResponse.json(apiKey[0])
  } catch (error) {
    console.error('[API_KEYS_POST]', error)
    return new NextResponse('Internal Error', { status: 500 })
  }
}

// DELETE /api/keys - Delete an API key
export async function DELETE(req: Request) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const userId = session?.user?.id

    const { searchParams } = new URL(req.url)
    const id = searchParams.get('id')
    if (!id) {
      return new NextResponse('API key ID is required', { status: 400 })
    }

    // Verify the API key belongs to the user
    const apiKey = await db.query.apiKeys.findFirst({
      where: eq(apiKeys.id, id),
    })

    if (!apiKey || apiKey.userId !== userId) {
      return new NextResponse('Not found', { status: 404 })
    }

    await db.delete(apiKeys).where(eq(apiKeys.id, id))

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error('[API_KEYS_DELETE]', error)
    return new NextResponse('Internal Error', { status: 500 })
  }
}
