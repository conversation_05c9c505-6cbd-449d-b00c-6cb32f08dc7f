import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { text, action, customInstruction } = body

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 })
    }

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 })
    }

    // Define prompts for different actions
    const prompts = {
      improve: `Please improve the following text to make it more engaging, clear, and professional while maintaining its original meaning and tone. ${customInstruction ? `Additional instruction: ${customInstruction}` : ''}

Text to improve: "${text}"

Please respond with only the improved text, no additional commentary.`,

      emojify: `Add appropriate emojis to the following text to make it more engaging and fun. Use emojis that enhance the meaning and make the text more visually appealing. ${customInstruction ? `Additional instruction: ${customInstruction}` : ''}

Text to emojify: "${text}"

Please respond with only the text with emojis added, no additional commentary.`,

      longer: `Expand the following text to make it longer and more detailed while maintaining its original meaning and tone. Add relevant context, examples, or elaboration. ${customInstruction ? `Additional instruction: ${customInstruction}` : ''}

Text to expand: "${text}"

Please respond with only the expanded text, no additional commentary.`,

      shorter: `Make the following text more concise and to the point while preserving its core message and meaning. Remove unnecessary words and phrases. ${customInstruction ? `Additional instruction: ${customInstruction}` : ''}

Text to shorten: "${text}"

Please respond with only the shortened text, no additional commentary.`,

      fix: `Fix any spelling, grammar, punctuation, and syntax errors in the following text. Make it grammatically correct and well-written. ${customInstruction ? `Additional instruction: ${customInstruction}` : ''}

Text to fix: "${text}"

Please respond with only the corrected text, no additional commentary.`,

      simplify: `Simplify the following text to make it easier to understand. Use simpler words, shorter sentences, and clearer language while maintaining the original meaning. ${customInstruction ? `Additional instruction: ${customInstruction}` : ''}

Text to simplify: "${text}"

Please respond with only the simplified text, no additional commentary.`,

      custom: `${customInstruction || 'Please process the following text as requested:'}

Text: "${text}"

Please respond with only the processed text, no additional commentary.`,
    }

    const prompt = prompts[action as keyof typeof prompts] || prompts.custom

    console.log('🤖 OpenAI Request:', {
      action,
      textLength: text.length,
      hasCustomInstruction: !!customInstruction,
    })

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini', // Using the faster, more cost-effective model for text transformations
      messages: [
        {
          role: 'system',
          content:
            'You are a helpful AI assistant that transforms text according to user instructions. Always respond with only the transformed text, no additional explanations or commentary.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      max_tokens: 500,
      temperature: 0.7,
    })

    const transformedText = completion.choices[0]?.message?.content?.trim()

    if (!transformedText) {
      return NextResponse.json(
        { error: 'Failed to generate response' },
        { status: 500 }
      )
    }

    // Clean the response text - remove surrounding quotes and extra whitespace
    const cleanedText = transformedText
      .replace(/^["']|["']$/g, '') // Remove quotes at start and end
      .replace(/^`{1,3}|`{1,3}$/g, '') // Remove backticks at start and end
      .trim() // Remove extra whitespace

    console.log('✅ OpenAI Success:', {
      action,
      originalLength: text.length,
      transformedLength: cleanedText.length,
    })

    return NextResponse.json({
      originalText: text,
      transformedText: cleanedText,
      action,
      customInstruction,
    })
  } catch (error) {
    console.error('❌ OpenAI API Error:', error)
    return NextResponse.json(
      {
        error: 'Failed to process text transformation',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
