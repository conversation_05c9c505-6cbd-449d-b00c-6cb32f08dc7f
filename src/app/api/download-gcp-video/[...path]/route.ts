import { NextRequest } from 'next/server'
import { Storage } from '@google-cloud/storage'

const storage = new Storage({
  projectId: process.env.REMOTION_GCP_PROJECT_ID,
  credentials: {
    type: 'service_account',
    client_email: process.env.REMOTION_GCP_CLIENT_EMAIL,
    private_key: process.env.REMOTION_GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    project_id: process.env.REMOTION_GCP_PROJECT_ID,
  },
})

function sanitizeFilename(filename: string): string {
  // Replace Unicode dashes with ASCII dash
  let sanitized = filename.replace(/–/g, '-')
  // Normalize to NFKD and remove non-ASCII characters
  sanitized = sanitized.normalize('NFKD').replace(/[^\x00-\x7F]/g, '')
  return sanitized
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params
    const filePath = path.join('/')
    const filename = request.nextUrl.searchParams.get('filename') || 'video.mp4'
    const bucketName = 'remotioncloudrun-iexx606ijk'

    const bucket = storage.bucket(bucketName)
    const file = bucket.file(filePath)

    // Check if file exists
    const [exists] = await file.exists()
    if (!exists) {
      return new Response('Video not found', { status: 404 })
    }

    // Generate signed URL with response disposition for download
    const [signedUrl] = await file.getSignedUrl({
      version: 'v4',
      action: 'read',
      expires: Date.now() + 2 * 60 * 60 * 1000, // 2 hours
      responseDisposition: `attachment; filename="${sanitizeFilename(filename)}"`,
    })

    return Response.json({ downloadUrl: signedUrl })
  } catch (error) {
    console.error('Signed URL generation error:', error)
    return new Response('Download failed', { status: 500 })
  }
}
