import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { setActiveOrganizationForUser } from '@/lib/organization-utils'

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = await setActiveOrganizationForUser(session.user.id)

    if (organizationId) {
      return NextResponse.json({
        success: true,
        organizationId,
        message: 'Active organization set successfully',
      })
    } else {
      return NextResponse.json(
        {
          error: 'No organization found for user',
        },
        { status: 404 }
      )
    }
  } catch (error) {
    console.error('Error setting active organization:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}
