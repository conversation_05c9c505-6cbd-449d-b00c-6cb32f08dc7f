import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'
import { getUserSession } from '@/lib/user-utils'
import { incrementUsage } from '@/lib/usage-utils'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Define allowed file types and their configurations
const ALLOWED_FILE_TYPES = {
  'application/pdf': {
    bucket: 'pdfs',
    maxSize: 10 * 1024 * 1024, // 10MB
    contentType: 'application/pdf',
    label: 'PDF',
  },
  'audio/mpeg': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/mpeg',
    label: 'Audio',
  },
  'audio/wav': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/wav',
    label: 'Audio',
  },
  'audio/flac': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/flac',
    label: 'Audio',
  },
  'audio/aac': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/aac',
    label: 'Audio',
  },
  'audio/ogg': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/ogg',
    label: 'Audio',
  },
  'audio/mp4': {
    bucket: 'audios',
    maxSize: 50 * 1024 * 1024, // 50MB
    contentType: 'audio/mp4',
    label: 'Audio',
  },
} as const

// Initiate upload with signed URLs (no file body)
export async function POST(request: NextRequest) {
  try {
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id

    const { fileName, fileType, fileSize } = await request.json()

    if (!fileName || !fileType || typeof fileSize !== 'number') {
      return NextResponse.json(
        { error: 'Missing file info (fileName, fileType, fileSize)' },
        { status: 400 }
      )
    }

    // Check if file type is allowed
    const fileConfig =
      ALLOWED_FILE_TYPES[fileType as keyof typeof ALLOWED_FILE_TYPES]
    if (!fileConfig) {
      return NextResponse.json(
        {
          error: `Invalid file type. Allowed types: ${Object.keys(
            ALLOWED_FILE_TYPES
          )
            .map(
              type =>
                ALLOWED_FILE_TYPES[type as keyof typeof ALLOWED_FILE_TYPES]
                  .label
            )
            .join(', ')}`,
        },
        { status: 400 }
      )
    }

    // Validate file size
    if (fileSize > fileConfig.maxSize) {
      const maxSizeMB = fileConfig.maxSize / (1024 * 1024)
      return NextResponse.json(
        { error: `File too large. Maximum size is ${maxSizeMB}MB.` },
        { status: 400 }
      )
    }

    const filePath = `${userId}/${Date.now()}-${fileName}`

    // Generate signed upload URL
    const { data: signedData, error: signedErr } = await supabase.storage
      .from(fileConfig.bucket)
      .createSignedUploadUrl(filePath)

    if (signedErr || !signedData) {
      return NextResponse.json(
        { error: 'Failed to create signed upload URL' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      uploadUrl: signedData.signedUrl,
      uploadToken: signedData.token,
      filePath,
      filename: fileName,
      size: fileSize,
      type: fileType,
      bucket: fileConfig.bucket,
    })
  } catch (error) {
    console.error('Upload initiation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Complete upload and get public URL
export async function PUT(request: NextRequest) {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId
    const referenceId = activeOrganizationId || userId

    const { filePath, bucket, fileSize } = await request.json()

    if (!filePath || !bucket || typeof fileSize !== 'number') {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath)

    // Update storage usage (non-blocking)
    const fileSizeInMB = Math.ceil(fileSize / (1024 * 1024))
    incrementUsage(referenceId, 'storage', fileSizeInMB).catch(error => {
      console.warn('Failed to track storage usage:', error)
    })

    return NextResponse.json({
      success: true,
      url: urlData.publicUrl,
      path: filePath,
      filename: filePath.split('/').pop() || '',
      size: fileSize,
      type: 'audio/mpeg', // Default for audio files
      bucket,
    })
  } catch (error) {
    console.error('Upload completion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
