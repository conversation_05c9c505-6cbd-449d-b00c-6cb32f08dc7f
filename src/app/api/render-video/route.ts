import { NextRequest, NextResponse } from 'next/server'
import { inngest } from '@/inngest/client'

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Extract key information from the request
    const {
      projectId,
      userId,
      organizationId,
      exportResolution,
      duration,
      inputProps,
      userEmail,
      userName,
    } = data

    console.log('🚀 Starting render for project:', projectId)

    // Send minimal event to Inngest - no large payload needed!
    await inngest.send({
      name: 'render-video',
      data: {
        projectId,
        userId,
        organizationId,
        exportResolution,
        duration,
        subtitlePosition: inputProps.subtitlePosition,
        compositionHeight: inputProps.compositionHeight,
        compositionWidth: inputProps.compositionWidth,
        durationInFrames: inputProps.durationInFrames,
        exportName: inputProps.exportName,
        userEmail,
        userName,
        // All other data will be loaded from projects database in Inngest
      },
    })

    console.log('✅ Render job queued successfully')
    return NextResponse.json({ ok: true })
  } catch (error) {
    console.error('Error in render-video API:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    )
  }
}
