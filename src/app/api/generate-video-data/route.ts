import { NextRequest, NextResponse } from 'next/server'
import { inngest } from '@/inngest/client'
import { onRequestError } from '../../../../instrumentation'

export async function POST(req: NextRequest) {
  try {
    const data = await req.json()
    // Trigger the inngest function and return the eventId for polling
    const { ids } = await inngest.send({
      name: 'generate-video-data',
      data,
    })
    console.log(ids)
    return NextResponse.json({ eventId: ids[0] })
  } catch (error) {
    await onRequestError(error, req)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
