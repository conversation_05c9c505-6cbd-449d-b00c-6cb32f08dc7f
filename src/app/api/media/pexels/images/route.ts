import { NextRequest } from 'next/server'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const PEXELS_API_KEY = process.env.PEXELS_API_KEY

export async function GET(request: NextRequest) {
  if (!PEXELS_API_KEY) {
    return createErrorResponse('Pexels API key not configured', 500)
  }

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  const orientation = searchParams.get('orientation')
  const perPage = searchParams.get('per_page') || '24'
  const page = searchParams.get('page') || '1'

  if (!query) {
    return createErrorResponse('Query parameter is required', 400)
  }

  try {
    let url = `https://api.pexels.com/v1/search?query=${encodeURIComponent(query)}&per_page=${perPage}&page=${page}`

    if (orientation && orientation !== 'all') {
      url += `&orientation=${orientation}`
    }

    const response = await fetch(url, {
      headers: {
        Authorization: PEXELS_API_KEY,
      },
    })

    if (!response.ok) {
      throw new Error(`Pexels API error: ${response.status}`)
    }

    const data = await response.json()

    return createCachedResponse(data, CACHE_DURATIONS.PEXELS_SEARCH)
  } catch (error) {
    console.error('Pexels images API error:', error)
    return createErrorResponse('Failed to fetch images from Pexels', 500)
  }
}
