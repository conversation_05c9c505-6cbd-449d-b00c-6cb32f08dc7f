import { NextRequest } from 'next/server'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const GCS_DEVELOPER_KEY = process.env.GCS_DEVELOPER_KEY
const GCS_CX = process.env.GCS_CX

export async function GET(request: NextRequest) {
  if (!GCS_DEVELOPER_KEY || !GCS_CX) {
    return createErrorResponse('Google Custom Search API not configured', 500)
  }

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  // Respect Google limit of max 10 per request – never exceed it
  const perPage = searchParams.get('per_page') || '24'
  const page = searchParams.get('page') || '1'
  const rights = searchParams.get('rights') || undefined

  if (!query) {
    return createErrorResponse('Query parameter is required', 400)
  }

  // Calculate start index safely (Google Custom Search uses 1-based indexing)
  const toInt = (val: string, fallback: number) => {
    const n = Number(val)
    return Number.isFinite(n) && n > 0 ? Math.floor(n) : fallback
  }
  const requested = toInt(perPage, 10)
  const effectivePerPage = Math.min(Math.max(requested, 1), 10)
  const pageNum = toInt(page, 1)
  const startIndex = (pageNum - 1) * effectivePerPage + 1

  // Google Custom Search API has a limit of 100 results total
  if (startIndex > 100) {
    return createErrorResponse('Page limit exceeded (max 100 results)', 400)
  }

  try {
    // Build the API URL with parameters
    const params = new URLSearchParams({
      key: GCS_DEVELOPER_KEY,
      cx: GCS_CX,
      q: query,
      searchType: 'image',
      num: effectivePerPage.toString(), // Max 10 per request
      start: startIndex.toString(),
      safe: 'active', // Enable safe search
    })

    // Optional filters to match caller expectations
    if (rights) {
      params.set('rights', rights)
    }

    const url = `https://www.googleapis.com/customsearch/v1?${params}`

    const response = await fetch(url)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      console.error(
        'Google Custom Search API error:',
        response.status,
        errorData
      )

      if (response.status === 429) {
        return createErrorResponse('Rate limit exceeded', 429)
      }

      throw new Error(`Google Custom Search API error: ${response.status}`)
    }

    const data = await response.json()

    // Post-filter: allow only common raster formats and valid URLs
    const allowed = ['image/jpeg', 'image/jpg', 'image/png']
    type GoogleItem = {
      link?: string
      mime?: string
      fileFormat?: string
      image?: { thumbnailLink?: string; width?: number; height?: number }
      title?: string
    }
    const items = ((data.items as GoogleItem[] | undefined) || []).filter(
      (item: GoogleItem) => {
        const link: string = item.link || ''
        const mime: string = (item.mime || item.fileFormat || '').toLowerCase()
        const endsOk = /\.(jpg|jpeg|png)(\?.*)?$/i.test(link)
        const mimeOk = allowed.some(t => mime.includes(t))
        return !!link && (endsOk || mimeOk)
      }
    )

    const transformedData = {
      items,
      searchInformation: data.searchInformation || {},
      queries: data.queries || {},
    }

    return createCachedResponse(transformedData, CACHE_DURATIONS.GOOGLE_SEARCH)
  } catch (error) {
    console.error('Google Custom Search API error:', error)
    return createErrorResponse('Failed to fetch images from Google', 500)
  }
}
