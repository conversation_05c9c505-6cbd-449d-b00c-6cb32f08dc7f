import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'
import { mediaAssets } from '@/db/schema'
import { eq, and, like, desc } from 'drizzle-orm'
import { getUserSession } from '@/lib/user-utils'

export async function GET(request: NextRequest) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Query audio files for the user
    const audioFiles = await db
      .select({
        id: mediaAssets.id,
        fileName: mediaAssets.fileName,
        originalName: mediaAssets.originalName,
        mimeType: mediaAssets.mimeType,
        fileSize: mediaAssets.fileSize,
        originalUrl: mediaAssets.originalUrl,
        duration: mediaAssets.duration,
        createdAt: mediaAssets.createdAt,
      })
      .from(mediaAssets)
      .where(
        and(
          eq(mediaAssets.userId, userId),
          like(mediaAssets.mimeType, 'audio/%')
        )
      )
      .orderBy(desc(mediaAssets.createdAt))
      .limit(limit)
      .offset(offset)

    // Get total count for pagination
    const totalCount = await db
      .select({ count: mediaAssets.id })
      .from(mediaAssets)
      .where(
        and(
          eq(mediaAssets.userId, userId),
          like(mediaAssets.mimeType, 'audio/%')
        )
      )

    const count = totalCount.length

    // Transform to MusicTrack format
    const tracks = audioFiles.map(file => ({
      id: file.id,
      title: file.originalName.replace(/\.[^/.]+$/, ''), // Remove file extension
      genre: 'UPLOADED',
      mood: 'CUSTOM',
      artistName: 'Uploaded',
      provider: 'Local',
      licenseId: 'CUSTOM',
      previewUrl: file.originalUrl,
      durationMillis: file.duration ? parseInt(file.duration) * 1000 : 0,
    }))

    return NextResponse.json({
      data: tracks,
      count,
      page,
      limit,
      totalPages: Math.ceil(count / limit),
    })
  } catch (error) {
    console.error('Error fetching user audio files:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
