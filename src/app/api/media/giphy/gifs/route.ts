import { NextRequest } from 'next/server'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const GIPHY_API_KEY = process.env.GIPHY_KEY

export async function GET(request: NextRequest) {
  if (!GIPHY_API_KEY) {
    return createErrorResponse('Giphy API key not configured', 500)
  }

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  const orientation = searchParams.get('orientation')
  const perPage = searchParams.get('per_page') || '24'
  const page = searchParams.get('page') || '1'

  if (!query) {
    return createErrorResponse('Query parameter is required', 400)
  }

  try {
    // Calculate offset for pagination (Giphy uses offset, not page)
    const limit = parseInt(perPage)
    const offset = (parseInt(page) - 1) * limit

    const url = `https://api.giphy.com/v1/gifs/search?api_key=${GIPHY_API_KEY}&q=${encodeURIComponent(query)}&limit=${limit}&offset=${offset}&rating=pg-13&lang=en`

    // Giphy doesn't have direct orientation filters, but we can simulate with aspect ratio
    // We'll handle this in the response processing instead of URL parameters

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Giphy API error: ${response.status}`)
    }

    const data = await response.json()

    // Filter results by orientation if specified
    let filteredResults = data.data
    if (orientation && orientation !== 'all') {
      filteredResults = data.data.filter(
        (gif: { images: { original: { width: string; height: string } } }) => {
          const aspectRatio =
            parseInt(gif.images.original.width) /
            parseInt(gif.images.original.height)

          switch (orientation) {
            case 'landscape':
              return aspectRatio > 1.2
            case 'portrait':
              return aspectRatio < 0.8
            case 'square':
              return aspectRatio >= 0.8 && aspectRatio <= 1.2
            default:
              return true
          }
        }
      )
    }

    // Transform to match expected format
    const transformedData = {
      data: filteredResults,
      pagination: data.pagination,
      meta: data.meta,
    }

    return createCachedResponse(transformedData, CACHE_DURATIONS.GIPHY_SEARCH)
  } catch (error) {
    console.error('Giphy API error:', error)
    return createErrorResponse('Failed to fetch GIFs from Giphy', 500)
  }
}
