import { NextRequest, NextResponse } from 'next/server'

const UNSPLASH_CLIENT_ID = process.env.UNSPLASH_CLIENT_ID

export async function GET(request: NextRequest) {
  if (!UNSPLASH_CLIENT_ID) {
    return NextResponse.json(
      { error: 'Unsplash client ID not configured' },
      { status: 500 }
    )
  }

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  const orientation = searchParams.get('orientation') || 'landscape'
  const perPage = searchParams.get('per_page') || '24'
  const page = searchParams.get('page') || '1'

  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter is required' },
      { status: 400 }
    )
  }

  try {
    const url = `https://api.unsplash.com/search/photos?query=${encodeURIComponent(query)}&per_page=${perPage}&page=${page}&orientation=${orientation}`

    const response = await fetch(url, {
      headers: {
        Authorization: `Client-ID ${UNSPLASH_CLIENT_ID}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Unsplash API error: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Unsplash images API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch images from Unsplash' },
      { status: 500 }
    )
  }
}
