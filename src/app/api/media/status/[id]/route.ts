import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'
import { mediaAssets } from '@/db/schema'
import { eq, and } from 'drizzle-orm'
import { getUserSession } from '@/lib/user-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id

    const { id } = await params

    if (!id) {
      return NextResponse.json({ error: 'Upload ID required' }, { status: 400 })
    }

    // Fetch the upload record
    const [record] = await db
      .select()
      .from(mediaAssets)
      .where(and(eq(mediaAssets.id, id), eq(mediaAssets.userId, userId)))
      .limit(1)

    if (!record) {
      return NextResponse.json({ error: 'Upload not found' }, { status: 404 })
    }

    // For now, assume complete if record exists with URLs
    const status = record.originalUrl ? 'complete' : 'processing'
    const progress = record.originalUrl ? 100 : 50
    const message = record.originalUrl ? 'Upload complete' : 'Processing...'

    // If upload is complete, return full media data
    if (status === 'complete') {
      return NextResponse.json({
        status: 'complete',
        progress: 100,
        message: 'Upload complete',
        data: {
          id: record.id,
          width: record.width,
          height: record.height,
          duration: record.duration ? parseFloat(record.duration) : undefined,
          file_size: record.fileSize,
          quality: record.quality,
          tags: record.metadata?.tags || [],
          url: record.originalUrl,
          thumbnail: record.thumbnailUrl,
          user: {
            id: record.userId,
            name: 'User',
          },
          metadata: {
            aspectRatio: record.metadata?.aspectRatio,
            orientation: record.metadata?.orientation,
            dominantColors: record.metadata?.dominantColors,
            mimeType: record.mimeType,
            fileName: record.originalName,
            uploadedAt: record.createdAt?.toISOString(),
          },
          original_url: record.originalUrl,
          thumbnail_url: record.thumbnailUrl,
          low_res_url: record.thumbnailUrl,
        },
      })
    }

    // Note: Error handling could be added here if needed

    // Return current status for ongoing uploads
    return NextResponse.json({
      status,
      progress,
      message,
    })
  } catch (error) {
    console.error('Status check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
