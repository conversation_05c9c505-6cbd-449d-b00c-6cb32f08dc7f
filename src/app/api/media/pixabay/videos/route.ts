import { NextRequest } from 'next/server'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const PIXABAY_API_KEY = process.env.PIXABAY_API_KEY

export async function GET(request: NextRequest) {
  if (!PIXABAY_API_KEY) {
    return createErrorResponse('Pixabay API key not configured', 500)
  }

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  const perPage = searchParams.get('per_page') || '24'
  const page = searchParams.get('page') || '1'

  if (!query) {
    return createErrorResponse('Query parameter is required', 400)
  }

  try {
    const url = `https://pixabay.com/api/videos/?key=${PIXABAY_API_KEY}&q=${encodeURIComponent(query)}&per_page=${perPage}&page=${page}&safesearch=true`

    // Note: Pixabay videos API doesn't support orientation filter
    // but we keep the parameter for consistency

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Pixabay API error: ${response.status}`)
    }

    const data = await response.json()
    return createCachedResponse(data, CACHE_DURATIONS.PIXABAY_SEARCH)
  } catch (error) {
    console.error('Pixabay videos API error:', error)
    return createErrorResponse('Failed to fetch videos from Pixabay', 500)
  }
}
