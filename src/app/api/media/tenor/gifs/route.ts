import { NextRequest } from 'next/server'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const TENOR_API_KEY = process.env.TENOR_KEY

export async function GET(request: NextRequest) {
  if (!TENOR_API_KEY) {
    return createErrorResponse('Tenor API key not configured', 500)
  }

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  const orientation = searchParams.get('orientation')
  const perPage = searchParams.get('per_page') || '24'
  const page = searchParams.get('page') || '1'

  if (!query) {
    return createErrorResponse('Query parameter is required', 400)
  }

  try {
    // Calculate position for pagination (Tenor uses pos, not page)
    const limit = parseInt(perPage)

    // Build the URL with proper parameter encoding for Google Tenor API v2
    const params = new URLSearchParams({
      key: TENOR_API_KEY,
      q: query,
      limit: limit.toString(),
      contentfilter: 'medium',
      media_filter: 'gif,tinygif,mp4,tinymp4',
      client_key: 'adori_ai_v2', // Required for v2 API
      locale: 'en_US',
      country: 'US',
    })

    if (page !== '1') {
      params.append('pos', ((parseInt(page) - 1) * limit).toString())
    }

    // Tenor v2 supports ar_range parameter for aspect ratio filtering
    if (orientation && orientation !== 'all') {
      switch (orientation) {
        case 'landscape':
          params.append('ar_range', 'wide') // 0.42 <= aspect ratio <= 2.36
          break
        case 'portrait':
          // Tenor doesn't have a specific portrait range, so we'll filter client-side
          params.append('ar_range', 'all')
          break
        case 'square':
          params.append('ar_range', 'standard') // 0.56 <= aspect ratio <= 1.78
          break
        default:
          params.append('ar_range', 'all')
      }
    }

    const url = `https://tenor.googleapis.com/v2/search?${params.toString()}`

    const response = await fetch(url)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Tenor API error details:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText,
        url: url,
      })

      if (response.status === 401) {
        throw new Error(
          `Tenor API authentication failed (401). Please check your TENOR_KEY environment variable. You need a Google API key with Tenor API access enabled. Get one at: https://developers.google.com/tenor/guides/quickstart`
        )
      }

      throw new Error(
        `Tenor API error: ${response.status} - ${response.statusText}`
      )
    }

    const data = await response.json()

    // Additional client-side filtering for portrait orientation
    // Note: Google Tenor API v2 uses media_formats instead of media
    let filteredResults = data.results
    if (orientation === 'portrait') {
      filteredResults = data.results.filter(
        (gif: { media_formats?: { gif?: { dims: [number, number] } } }) => {
          // Get dimensions from the gif format in v2 API
          const gifMedia = gif.media_formats?.gif
          if (gifMedia && gifMedia.dims) {
            const aspectRatio = gifMedia.dims[0] / gifMedia.dims[1]
            return aspectRatio < 0.8
          }
          return true
        }
      )
    }

    // Transform to match expected format
    const transformedData = {
      results: filteredResults,
      next: data.next || '0',
    }

    return createCachedResponse(transformedData, CACHE_DURATIONS.TENOR_SEARCH)
  } catch (error) {
    console.error('Tenor API error:', error)
    return createErrorResponse('Failed to fetch GIFs from Tenor', 500)
  }
}
