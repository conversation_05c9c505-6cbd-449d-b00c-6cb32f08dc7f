import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { db } from '@/lib/db'
import { mediaAssets } from '@/db/schema'
import { getUserSession } from '@/lib/user-utils'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Define allowed media types
const ALLOWED_MEDIA_TYPES = {
  // Images
  'image/jpeg': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'JPEG Image',
  },
  'image/jpg': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'JPG Image',
  },
  'image/png': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'PNG Image',
  },
  'image/webp': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'WebP Image',
  },
  'image/gif': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'GIF Image',
  },

  // Videos
  'video/mp4': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'MP4 Video',
  },
  'video/webm': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'WebM Video',
  },
  'video/quicktime': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'QuickTime Video',
  },
  'video/x-msvideo': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'AVI Video',
  },

  // Audio
  'audio/mpeg': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'MP3 Audio',
  },
  'audio/mp3': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'MP3 Audio',
  },
  'audio/wav': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'WAV Audio',
  },
  'audio/wave': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'WAV Audio',
  },
  'audio/x-wav': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'WAV Audio',
  },
  'audio/aac': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'AAC Audio',
  },
  'audio/ogg': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'OGG Audio',
  },
  'audio/webm': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'WebM Audio',
  },
}

// Initiate upload with pre-processed metadata (JSON only)
export async function POST(request: NextRequest) {
  try {
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id

    const { fileName, fileType, fileSize, metadata, includeThumbnail } =
      await request.json()

    if (!fileName || !fileType || typeof fileSize !== 'number') {
      return NextResponse.json(
        { error: 'Missing file info (fileName, fileType, fileSize)' },
        { status: 400 }
      )
    }

    // Validate type and size
    const fileConfig =
      ALLOWED_MEDIA_TYPES[fileType as keyof typeof ALLOWED_MEDIA_TYPES]
    if (!fileConfig) {
      return NextResponse.json(
        {
          error: `Invalid file type. Allowed types: ${Object.keys(
            ALLOWED_MEDIA_TYPES
          )
            .map(
              type =>
                ALLOWED_MEDIA_TYPES[type as keyof typeof ALLOWED_MEDIA_TYPES]
                  .label
            )
            .join(', ')}`,
        },
        { status: 400 }
      )
    }

    if (fileSize > fileConfig.maxSize) {
      const maxSizeMB = fileConfig.maxSize / (1024 * 1024)
      return NextResponse.json(
        { error: `File too large. Maximum size is ${maxSizeMB}MB.` },
        { status: 400 }
      )
    }

    // Determine folder
    const isImage = fileType.startsWith('image/')
    const isVideo = fileType.startsWith('video/')
    const isAudio = fileType.startsWith('audio/')

    let folder: string
    if (isImage) {
      folder = 'images'
    } else if (isVideo) {
      folder = 'videos'
    } else if (isAudio) {
      folder = 'musics'
    } else {
      folder = 'others'
    }

    // Build paths
    const timestamp = Date.now()
    const sanitizedFileName = String(fileName).replace(/[^a-zA-Z0-9.-]/g, '_')
    const filePath = `${userId}/${folder}/${timestamp}-${sanitizedFileName}`
    const thumbnailPath = includeThumbnail
      ? `${userId}/thumbnails/${timestamp}-thumb-${sanitizedFileName.replace(/\.[^/.]+$/, '.jpg')}`
      : null

    // Create DB record in processing state
    const [insertedRecord] = await db
      .insert(mediaAssets)
      .values({
        userId,
        fileName: `${timestamp}-${sanitizedFileName}`,
        originalName: fileName,
        mimeType: fileType,
        fileSize,
        originalUrl: '',
        thumbnailUrl: null,
        width: metadata?.width || null,
        height: metadata?.height || null,
        duration: metadata?.duration ? String(metadata.duration) : null,
        quality: metadata?.quality || null,
        metadata: {
          aspectRatio: metadata?.aspectRatio || 1,
          orientation: metadata?.orientation || 'square',
          dominantColors: metadata?.dominantColors || [],
          tags: [],
          description: undefined,
          alt: undefined,
        },
      })
      .returning()

    // Generate signed upload URLs (tokens)
    const { data: signedOriginal, error: signedErr } = await supabase.storage
      .from(fileConfig.bucket)
      .createSignedUploadUrl(filePath)

    if (signedErr || !signedOriginal) {
      return NextResponse.json(
        { error: 'Failed to create signed upload URL' },
        { status: 500 }
      )
    }

    let signedThumb: { signedUrl: string; token: string } | null = null
    if (thumbnailPath) {
      const { data: s, error: e } = await supabase.storage
        .from(fileConfig.bucket)
        .createSignedUploadUrl(thumbnailPath)
      if (!e && s) signedThumb = s
    }

    return NextResponse.json({
      id: insertedRecord.id,
      bucket: fileConfig.bucket,
      filePath,
      thumbnailPath,
      uploadToken: signedOriginal.token,
      uploadUrl: signedOriginal.signedUrl,
      thumbnailUploadToken: signedThumb?.token,
      thumbnailUploadUrl: signedThumb?.signedUrl,
    })
  } catch (error) {
    console.error('Upload initiation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
