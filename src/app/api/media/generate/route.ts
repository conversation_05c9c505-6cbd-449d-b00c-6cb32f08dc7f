import { NextRequest, NextResponse } from 'next/server'
import { createErrorResponse } from '@/lib/api-cache'
import { getUserSession } from '@/lib/user-utils'
import { incrementUsage, checkUsageLimit } from '@/lib/usage-utils'
import { createClient } from '@supabase/supabase-js'

// Supabase client for uploads
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Helper function to upload AI-generated image to Supabase storage
async function uploadAIImageToSupabase(
  base64Image: string,
  userId: string,
  prompt: string
): Promise<string> {
  try {
    // Remove data URL prefix to get just the base64 data
    const base64Data = base64Image.replace(/^data:image\/jpeg;base64,/, '')

    // Convert base64 to buffer
    const imageBuffer = Buffer.from(base64Data, 'base64')

    // Create a unique filename based on prompt and timestamp
    const timestamp = Date.now()
    const promptHash = prompt.replace(/[^a-zA-Z0-9]/g, '').substring(0, 20)
    const fileName = `ai-${promptHash}-${timestamp}.jpg`
    const filePath = `${userId}/images/${fileName}`

    // Upload to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('assets')
      .upload(filePath, imageBuffer, {
        contentType: 'image/jpeg',
        upsert: false,
      })

    if (uploadError) {
      console.error('Supabase AI image upload error:', uploadError)
      throw new Error(`Upload failed: ${uploadError.message}`)
    }

    // Construct and return the public URL
    const SUPABASE_URL =
      process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
    const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/assets/${filePath}`

    console.log('✅ AI image uploaded to Supabase:', publicUrl)
    return publicUrl
  } catch (error) {
    console.error('Error uploading AI image to Supabase:', error)
    throw error
  }
}

const GETIMG_API_KEY = process.env.GETIMG_API_KEY

export async function POST(request: NextRequest) {
  if (!GETIMG_API_KEY) {
    return createErrorResponse('GetImg.ai API key not configured', 500)
  }

  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    // Use active organization ID if available, otherwise use user ID
    const referenceId = activeOrganizationId || userId

    // Check usage limits before generating image
    const usageCheck = await checkUsageLimit(referenceId, 'aiImages')
    if (!usageCheck.allowed) {
      return NextResponse.json(
        {
          error: 'AI image generation limit reached',
          current: usageCheck.current,
          limit: usageCheck.limit,
        },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { prompt, model = 'flux-1-schnell', orientation = 'landscape' } = body

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 })
    }

    // Map model to GetImg.ai endpoint
    const modelEndpoints: Record<string, string> = {
      'stable-diffusion-xl':
        'https://api.getimg.ai/v1/stable-diffusion-xl/text-to-image',
      'stable-diffusion-v1-6':
        'https://api.getimg.ai/v1/stable-diffusion/text-to-image',
      'flux-1-schnell': 'https://api.getimg.ai/v1/flux-schnell/text-to-image',
    }

    const endpoint = modelEndpoints[model] || modelEndpoints['flux-1-schnell']

    // Handle different model parameter requirements
    // All models use width/height according to GetImg.ai documentation
    let dimensionMap: Record<string, { width: number; height: number }>

    if (model === 'flux-1-schnell') {
      // FLUX models use width/height with standard 16:9 ratios
      dimensionMap = {
        landscape: { width: 1024, height: 576 }, // 16:9 landscape
        portrait: { width: 576, height: 1024 }, // 9:16 portrait
        square: { width: 1024, height: 1024 }, // 1:1 square
      }
    } else {
      // Stable Diffusion models use standard dimensions
      dimensionMap = {
        landscape: { width: 1024, height: 576 },
        portrait: { width: 576, height: 1024 },
        square: { width: 1024, height: 1024 },
      }
    }

    const dimensions = dimensionMap[orientation] || dimensionMap.landscape

    // Build request body based on model
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let requestBody: Record<string, any>

    if (model === 'flux-1-schnell') {
      // FLUX.1 schnell - only supports basic parameters
      requestBody = {
        prompt: prompt,
        output_format: 'jpeg',
        width: dimensions.width,
        height: dimensions.height,
      }
    } else {
      // Stable Diffusion models - support steps parameter
      requestBody = {
        prompt: prompt,
        width: dimensions.width,
        height: dimensions.height,
        output_format: 'jpeg',
        steps: 20, // Standard for Stable Diffusion models
      }
    }

    console.log(
      `Model: ${model} Orientation: ${orientation} Request body:`,
      requestBody
    )

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${GETIMG_API_KEY}`,
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`GetImg.ai API error: ${errorText}`)
    }

    const data = await response.json()
    console.log(data.cost)

    // Increment usage after successful image generation
    try {
      await incrementUsage(referenceId, 'aiImages', 1)
      console.log('✅ Usage incremented for AI image generation')
    } catch (usageError) {
      console.error(
        '❌ Failed to increment usage for AI image generation:',
        usageError
      )
      // Don't fail the entire operation if usage tracking fails
    }

    // Upload to Supabase and return the URL
    const base64ImageUrl = `data:image/jpeg;base64,${data.image}`
    let finalImageUrl = base64ImageUrl

    try {
      finalImageUrl = await uploadAIImageToSupabase(
        base64ImageUrl,
        userId,
        prompt
      )
    } catch (uploadError) {
      console.warn(
        'Failed to upload AI image to Supabase, using base64:',
        uploadError
      )
      // Continue with base64 if upload fails
    }

    // Return the image URL with dimensions
    return NextResponse.json({
      imageUrl: finalImageUrl,
      width: dimensions.width,
      height: dimensions.height,
    })
  } catch (error) {
    console.error('Image generation API error:', error)
    return createErrorResponse('Failed to generate image', 500)
  }
}
