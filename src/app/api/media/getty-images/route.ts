import { NextRequest } from 'next/server'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const GETTY_CLIENT_ID = process.env.GETTY_CLIENT_ID
const GETTY_CLIENT_SECRET = process.env.GETTY_CLIENT_SECRET

// Cache for access tokens (in memory for simplicity)
let accessTokenCache: {
  token: string
  expiresAt: number
} | null = null

async function getAccessToken(): Promise<string> {
  // Check if we have a valid cached token
  if (accessTokenCache && accessTokenCache.expiresAt > Date.now()) {
    return accessTokenCache.token
  }

  if (!GETTY_CLIENT_ID || !GETTY_CLIENT_SECRET) {
    throw new Error('Getty Images API credentials not configured')
  }

  try {
    const response = await fetch('https://authentication.gettyimages.com/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: GETTY_CLIENT_ID,
        client_secret: GETTY_CLIENT_SECRET,
      }),
    })

    if (!response.ok) {
      throw new Error(`Getty Images auth error: ${response.status}`)
    }

    const data = await response.json()
    
    // Cache the token with expiration (subtract 60 seconds for safety)
    accessTokenCache = {
      token: data.access_token,
      expiresAt: Date.now() + (data.expires_in - 60) * 1000,
    }

    return data.access_token
  } catch (error) {
    console.error('Getty Images authentication error:', error)
    throw error
  }
}

export async function GET(request: NextRequest) {
  if (!GETTY_CLIENT_ID || !GETTY_CLIENT_SECRET) {
    return createErrorResponse('Getty Images API not configured', 500)
  }

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  const orientation = searchParams.get('orientation') || 'landscape'
  const perPage = searchParams.get('per_page') || '24'
  const page = searchParams.get('page') || '1'

  if (!query) {
    return createErrorResponse('Query parameter is required', 400)
  }

  try {
    const accessToken = await getAccessToken()

    // Build the API URL with parameters
    const params = new URLSearchParams({
      phrase: query,
      page_size: Math.min(parseInt(perPage), 100).toString(), // Max 100 per request
      page: page,
      sort_order: 'best_match',
      fields: 'id,title,thumb,preview,comp,display_sizes',
    })

    // Add orientation filter
    if (orientation === 'landscape') {
      params.append('orientations', 'horizontal')
    } else if (orientation === 'portrait') {
      params.append('orientations', 'vertical')
    } else if (orientation === 'square') {
      params.append('orientations', 'square')
    }

    const url = `https://api.gettyimages.com/v3/search/images/creative?${params}`

    const response = await fetch(url, {
      headers: {
        'Api-Key': GETTY_CLIENT_ID,
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      console.error('Getty Images API error:', response.status, errorData)
      
      if (response.status === 401) {
        // Clear cached token and retry once
        accessTokenCache = null
        return createErrorResponse('Authentication failed', 401)
      }
      
      if (response.status === 429) {
        return createErrorResponse('Rate limit exceeded', 429)
      }
      
      throw new Error(`Getty Images API error: ${response.status}`)
    }

    const data = await response.json()

    return createCachedResponse(data, CACHE_DURATIONS.GETTY_SEARCH)
  } catch (error) {
    console.error('Getty Images API error:', error)
    return createErrorResponse('Failed to fetch images from Getty Images', 500)
  }
}
