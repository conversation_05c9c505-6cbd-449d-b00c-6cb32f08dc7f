import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { db } from '@/lib/db'
import { mediaAssets } from '@/db/schema'
import { eq } from 'drizzle-orm'
import { getUserSession } from '@/lib/user-utils'
import { incrementUsage } from '@/lib/usage-utils'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id
    const activeOrganizationId = session?.session?.activeOrganizationId
    const referenceId = activeOrganizationId || userId

    const {
      id,
      bucket,
      filePath,
      thumbnailPath,
      fileSize,
      thumbnailSize,
      clientMetadata,
    } = await request.json()

    if (!id || !bucket || !filePath || typeof fileSize !== 'number') {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const { data: originalUrlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath)

    let thumbnailUrl: string | null = null
    if (thumbnailPath) {
      const { data: thumbUrlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(thumbnailPath)
      thumbnailUrl = thumbUrlData.publicUrl
    }

    await db
      .update(mediaAssets)
      .set({
        originalUrl: originalUrlData.publicUrl,
        thumbnailUrl,
        metadata: {
          aspectRatio: clientMetadata?.aspectRatio || 1,
          orientation: clientMetadata?.orientation || 'square',
          dominantColors: clientMetadata?.dominantColors || [],
          tags: [],
          description: undefined,
          alt: undefined,
        },
        updatedAt: new Date(),
      })
      .where(eq(mediaAssets.id, id))

    try {
      const originalSizeInMB = Math.ceil(fileSize / (1024 * 1024))
      incrementUsage(referenceId, 'storage', originalSizeInMB).catch(() => {})
      if (typeof thumbnailSize === 'number') {
        const thumbSizeInMB = Math.ceil(thumbnailSize / (1024 * 1024))
        incrementUsage(referenceId, 'storage', thumbSizeInMB).catch(() => {})
      }
    } catch {}

    // Fetch the record to return transformed response
    const [asset] = await db
      .select()
      .from(mediaAssets)
      .where(eq(mediaAssets.id, id))
      .limit(1)

    if (!asset) {
      return NextResponse.json({ error: 'Asset not found' }, { status: 404 })
    }

    const response = {
      id: asset.id,
      width: asset.width,
      height: asset.height,
      duration: asset.duration ? parseFloat(asset.duration) : undefined,
      file_size: asset.fileSize,
      quality: asset.quality,
      tags: asset.metadata?.tags || [],
      url: asset.originalUrl,
      thumbnail: asset.thumbnailUrl,
      user: {
        id: asset.userId,
        name: 'User',
      },
      metadata: {
        aspectRatio: asset.metadata?.aspectRatio,
        orientation: asset.metadata?.orientation,
        dominantColors: asset.metadata?.dominantColors,
        mimeType: asset.mimeType,
        fileName: asset.originalName,
        uploadedAt: asset.createdAt?.toISOString(),
      },
      original_url: asset.originalUrl,
      thumbnail_url: asset.thumbnailUrl,
      low_res_url: asset.thumbnailUrl,
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Upload completion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
