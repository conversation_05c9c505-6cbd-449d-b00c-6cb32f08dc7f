import { NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { getUserSession } from '@/lib/user-utils'
import { db } from '@/lib/db'
import { youtubeConnections } from '@/db/schema'
import { eq, and, lt } from 'drizzle-orm'
import { decryptTokens, encryptTokens } from '@/lib/encryption'
import { refreshAccessToken, TokenRefreshError } from '@/lib/youtube-auth'
import { withPerformanceMonitoring } from '@/lib/performance'

async function handler() {
  try {
    // Check if user is authenticated
    // const { userId } = await auth()
    const session = await getUserSession()
    const userId = session?.user?.id
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Get expired but active YouTube connections
    const now = new Date()
    const expiredConnections = await db
      .select()
      .from(youtubeConnections)
      .where(
        and(
          eq(youtubeConnections.userId, userId),
          eq(youtubeConnections.isActive, true),
          lt(youtubeConnections.expiresAt, now) // Only expired tokens
        )
      )

    if (!expiredConnections.length) {
      return NextResponse.json({
        success: true,
        message: 'No expired connections to refresh',
        refreshedCount: 0,
      })
    }

    interface RefreshResult {
      connectionId: string
      status: 'success' | 'failed' | 'deleted'
      error?: string
    }

    const refreshResults: RefreshResult[] = []
    let successCount = 0
    let deletedCount = 0

    // Process expired connections in parallel for better performance
    await Promise.allSettled(
      expiredConnections.map(async conn => {
        try {
          // Decrypt tokens
          const tokens = decryptTokens({
            encryptedAccessToken: conn.accessToken,
            encryptedRefreshToken: conn.refreshToken,
          })

          // Try to refresh the token
          const refreshedTokens = await refreshAccessToken(tokens.refresh_token)

          // Encrypt and update the new tokens
          const encryptedTokens = encryptTokens({
            access_token: refreshedTokens.access_token,
            refresh_token: tokens.refresh_token, // Keep the same refresh token
          })

          await db
            .update(youtubeConnections)
            .set({
              accessToken: encryptedTokens.encryptedAccessToken,
              expiresAt:
                refreshedTokens.expires_at ||
                new Date(Date.now() + 3600 * 1000),
              updatedAt: new Date(),
            })
            .where(eq(youtubeConnections.id, conn.id))

          refreshResults.push({ connectionId: conn.id, status: 'success' })
          successCount++
        } catch (error) {
          console.error(
            'Failed to refresh YouTube token for connection:',
            conn.id,
            error
          )

          // Handle invalid_grant errors by deleting the connection
          if (
            error instanceof TokenRefreshError &&
            error.shouldDeleteConnection
          ) {
            console.log(`Deleting invalid YouTube connection: ${conn.id}`)

            // Delete the invalid connection
            await db
              .delete(youtubeConnections)
              .where(eq(youtubeConnections.id, conn.id))

            refreshResults.push({
              connectionId: conn.id,
              status: 'deleted',
              error: 'Connection was revoked and has been removed',
            })
            deletedCount++
          } else {
            // Mark connection as inactive for other errors
            await db
              .update(youtubeConnections)
              .set({ isActive: false, updatedAt: new Date() })
              .where(eq(youtubeConnections.id, conn.id))

            refreshResults.push({
              connectionId: conn.id,
              status: 'failed',
              error: error instanceof Error ? error.message : 'Unknown error',
            })
          }
        }
      })
    )

    const message = [
      `Refreshed ${successCount} of ${expiredConnections.length} expired connections`,
      deletedCount > 0 ? `Deleted ${deletedCount} revoked connections` : null,
    ]
      .filter(Boolean)
      .join(', ')

    return NextResponse.json({
      success: true,
      message,
      refreshedCount: successCount,
      deletedCount,
      results: refreshResults,
    })
  } catch (error) {
    console.error('Error refreshing YouTube tokens:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

export const POST = withPerformanceMonitoring(
  handler,
  '/api/youtube/auth/refresh'
)
