import { NextResponse } from 'next/server'
import { getUserSession } from '@/lib/user-utils'
import { db } from '@/lib/db'
import { youtubeConnections } from '@/db/schema'
import { eq, and } from 'drizzle-orm'
import { withPerformanceMonitoring } from '@/lib/performance'
import { ensureValidAccessToken, getChannelInfo } from '@/lib/youtube-auth'

async function handler() {
  console.log('=== YouTube Status API Called ===')

  try {
    // Check if user is authenticated
    const session = await getUserSession()
    const userId = session?.user?.id
    const activeOrganizationId = session?.session?.activeOrganizationId
    console.log('User ID:', userId)
    console.log('Active Organization ID:', activeOrganizationId)

    if (!userId) {
      console.log('No user ID found, returning 401')
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Build the where condition based on active organization ID
    let whereCondition
    if (activeOrganizationId) {
      // If user has an active organization, show YouTube connections from that organization
      whereCondition = and(
        eq(youtubeConnections.organizationId, activeOrganizationId),
        eq(youtubeConnections.isActive, true)
      )
      console.log(
        'Querying for organization YouTube connections:',
        activeOrganizationId
      )
    } else {
      // If no active organization, show only user's own YouTube connections
      whereCondition = and(
        eq(youtubeConnections.userId, userId),
        eq(youtubeConnections.isActive, true)
      )
      console.log('Querying for user YouTube connections:', userId)
    }

    // Get all active YouTube connections
    console.log('Querying database for YouTube connections...')
    const connections = await db
      .select()
      .from(youtubeConnections)
      .where(whereCondition)

    console.log(`Found ${connections.length} connections in database`)

    if (!connections.length) {
      console.log('No connections found, returning empty response')
      const response = {
        connected: false,
        connections: [],
      }
      console.log('Returning response:', response)
      return NextResponse.json(response)
    }

    // Fetch enhanced channel info with capabilities for each connection
    const enhancedConnections = await Promise.all(
      connections.map(async conn => {
        try {
          // Get valid access token and fetch channel capabilities
          const accessToken = await ensureValidAccessToken(conn)
          const channelInfo = await getChannelInfo(accessToken)

          console.log(`Channel capabilities for ${conn.channelTitle}:`, {
            isVerified: channelInfo.isVerified,
            longUploadsStatus: channelInfo.longUploadsStatus,
            customUrl: channelInfo.customUrl,
          })

          return {
            id: conn.id,
            channelInfo: {
              id: conn.channelId,
              title: conn.channelTitle,
              thumbnailUrl: conn.channelThumbnailUrl,
              description: conn.channelDescription,
              isVerified: channelInfo.isVerified,
              canUploadCustomThumbnails: channelInfo.canUploadCustomThumbnails,
              subscriberCount: channelInfo.subscriberCount,
              videoCount: channelInfo.videoCount,
              longUploadsStatus: channelInfo.longUploadsStatus,
              customUrl: channelInfo.customUrl,
            },
            connectedAt: conn.connectedAt,
            scopes: conn.scopes,
            isExpired: false,
            expiresAt: conn.expiresAt,
          }
        } catch (error) {
          console.error(
            `Failed to fetch capabilities for channel ${conn.channelTitle}:`,
            error
          )

          // Check if this is a revoked connection error
          if (
            error instanceof Error &&
            error.message === 'YOUTUBE_CONNECTION_REVOKED'
          ) {
            console.log(
              `Connection ${conn.id} was revoked, marking for deletion`
            )
            // Return null to indicate this connection should be removed
            return null
          }

          // For other errors, return basic info as fallback
          return {
            id: conn.id,
            channelInfo: {
              id: conn.channelId,
              title: conn.channelTitle,
              thumbnailUrl: conn.channelThumbnailUrl,
              description: conn.channelDescription,
              isVerified: false, // Default fallback
              canUploadCustomThumbnails: false,
              subscriberCount: '0',
              videoCount: '0',
              longUploadsStatus: 'disallowed', // Default fallback
              customUrl: null,
            },
            connectedAt: conn.connectedAt,
            scopes: conn.scopes,
            isExpired: false,
            expiresAt: conn.expiresAt,
          }
        }
      })
    )

    // Filter out revoked connections (null values) and get valid connections
    const validConnections = enhancedConnections.filter(conn => conn !== null)

    console.log(
      `Returning ${validConnections.length} valid connections out of ${connections.length} total`
    )

    // If no valid connections remain, return disconnected state
    if (validConnections.length === 0) {
      const response = {
        connected: false,
        connections: [],
      }
      console.log(
        'No valid connections remaining, returning disconnected state'
      )
      return NextResponse.json(response)
    }

    const response = {
      connected: true,
      connections: validConnections,
    }

    console.log('Returning response with connections:', response)
    return NextResponse.json(response)
  } catch (error) {
    console.error('=== ERROR in Status API ===')
    console.error('Error:', error)
    console.error(
      'Error message:',
      error instanceof Error ? error.message : 'Unknown error'
    )
    console.error(
      'Error stack:',
      error instanceof Error ? error.stack : undefined
    )

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

export const GET = withPerformanceMonitoring(
  handler,
  '/api/youtube/auth/status'
)
