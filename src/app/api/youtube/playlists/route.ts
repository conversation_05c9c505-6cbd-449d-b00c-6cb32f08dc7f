import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { getUserSession } from '@/lib/user-utils'
import { db } from '@/lib/db'
import { youtubeConnections } from '@/db/schema'
import { eq, and } from 'drizzle-orm'
import { google } from 'googleapis'
import { createOAuth2Client, ensureValidAccessToken } from '@/lib/youtube-auth'

export async function GET(request: NextRequest) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()
    const userId = session?.user?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const connectionId = searchParams.get('connectionId')

    if (!connectionId) {
      return NextResponse.json(
        { error: 'Connection ID is required' },
        { status: 400 }
      )
    }

    // Get the YouTube connection
    const connection = await db
      .select()
      .from(youtubeConnections)
      .where(
        and(
          eq(youtubeConnections.id, connectionId),
          eq(youtubeConnections.userId, userId),
          eq(youtubeConnections.isActive, true)
        )
      )
      .limit(1)

    if (connection.length === 0) {
      return NextResponse.json(
        { error: 'YouTube connection not found' },
        { status: 404 }
      )
    }

    const conn = connection[0]

    // Ensure we have a valid access token (refresh if necessary)
    let accessToken: string
    try {
      accessToken = await ensureValidAccessToken(conn)
    } catch (error) {
      // Handle connection revoked error specifically
      if (
        error instanceof Error &&
        error.message === 'YOUTUBE_CONNECTION_REVOKED'
      ) {
        return NextResponse.json(
          {
            error:
              'YouTube connection has been revoked. Please reconnect your account.',
            code: 'CONNECTION_REVOKED',
          },
          { status: 410 } // 410 Gone - resource no longer available
        )
      }

      // Handle other refresh errors
      return NextResponse.json(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to refresh YouTube connection',
        },
        { status: 401 }
      )
    }

    // Set up YouTube API client
    const oauth2Client = createOAuth2Client()
    oauth2Client.setCredentials({ access_token: accessToken })

    const youtube = google.youtube({ version: 'v3', auth: oauth2Client })

    // Fetch playlists for the channel
    const playlistsResponse = await youtube.playlists.list({
      part: ['snippet', 'status'],
      channelId: conn.channelId,
      maxResults: 50, // Get up to 50 playlists
    })

    const playlists =
      playlistsResponse.data.items?.map(playlist => ({
        id: playlist.id,
        title: playlist.snippet?.title,
        description: playlist.snippet?.description,
        thumbnailUrl: playlist.snippet?.thumbnails?.default?.url,
        itemCount: playlist.contentDetails?.itemCount || 0,
        privacy: playlist.status?.privacyStatus,
        publishedAt: playlist.snippet?.publishedAt,
      })) || []

    return NextResponse.json({
      playlists,
      channelId: conn.channelId,
      channelTitle: conn.channelTitle,
    })
  } catch (error) {
    console.error('YouTube playlists fetch error:', error)

    // Handle specific YouTube API errors
    if (error instanceof Error) {
      if (error.message.includes('quotaExceeded')) {
        return NextResponse.json(
          { error: 'YouTube API quota exceeded. Please try again later.' },
          { status: 429 }
        )
      }
      if (error.message.includes('forbidden')) {
        return NextResponse.json(
          {
            error:
              'YouTube channel does not have permission to access playlists.',
          },
          { status: 403 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to fetch YouTube playlists' },
      { status: 500 }
    )
  }
}
