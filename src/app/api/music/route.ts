import { NextRequest } from 'next/server'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'
import { db } from '@/lib/db'
import { stockMusic } from '@/db/schema'
import { desc, count } from 'drizzle-orm'

export interface MusicTrack {
  id: string
  title: string
  genre: string
  mood: string
  artistName: string
  artistUrl?: string
  provider: string
  licenseId: string
  sourceUrl?: string
  previewUrl: string
  durationMillis: number
}

export interface MusicApiResponse {
  data: MusicTrack[]
  count: number
  page: number
  limit: number
  hasMore: boolean
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    // Calculate offset for database query
    const offset = (page - 1) * limit

    // Get total count
    const countQuery = await db.select({ count: count() }).from(stockMusic)

    const totalCount = Number(countQuery[0]?.count || 0)

    // Get paginated data
    const tracks = await db
      .select()
      .from(stockMusic)
      .orderBy(desc(stockMusic.createdAt))
      .limit(limit)
      .offset(offset)

    // Transform database records to API response format
    const transformedTracks: MusicTrack[] = tracks.map(track => ({
      id: track.id,
      title: track.title,
      genre: track.genre,
      mood: track.mood,
      artistName: track.artistName,
      artistUrl: track.artistUrl || undefined,
      provider: track.provider,
      licenseId: track.licenseId,
      sourceUrl: track.sourceUrl || undefined,
      previewUrl: track.previewUrl,
      durationMillis: track.durationMillis,
    }))

    const response: MusicApiResponse = {
      data: transformedTracks,
      count: totalCount,
      page,
      limit,
      hasMore: offset + limit < totalCount,
    }

    return createCachedResponse(response, CACHE_DURATIONS.MUSIC_SEARCH)
  } catch (error) {
    console.error('Music API error:', error)
    return createErrorResponse('Failed to fetch music tracks', 500)
  }
}
