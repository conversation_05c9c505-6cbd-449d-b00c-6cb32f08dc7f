import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { supabase } from '@/lib/supabase-client'
import { getUserSession } from '@/lib/user-utils'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user from Clerk
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      console.error(
        '❌ Unauthorized access attempt to paginated render jobs API'
      )
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '8')

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      console.error('❌ Invalid pagination parameters:', { page, limit })
      return NextResponse.json(
        { error: 'Invalid pagination parameters' },
        { status: 400 }
      )
    }

    console.log('🔧 API: Fetching paginated render jobs:', {
      userId,
      page,
      limit,
      organizationId: activeOrganizationId,
    })

    try {
      // Build base query for count
      let countQuery = supabase
        .from('render_jobs')
        .select('*', { count: 'exact', head: true })

      // Apply filtering based on active organization ID
      if (activeOrganizationId) {
        // If user has an active organization, count render jobs from that organization
        countQuery = countQuery.eq('organization_id', activeOrganizationId)
      } else {
        // If no active organization, count only user's own render jobs
        countQuery = countQuery.eq('user_id', userId)
      }

      // Get total count first
      const { count, error: countError } = await countQuery

      if (countError) {
        console.error('❌ Database error getting count:', countError)
        return NextResponse.json(
          { error: 'Database error', details: countError.message },
          { status: 500 }
        )
      }

      // Get paginated jobs
      const from = (page - 1) * limit
      const to = from + limit - 1

      // Build base query for data
      let dataQuery = supabase
        .from('render_jobs')
        .select('*')
        .order('created_at', { ascending: false })
        .range(from, to)

      // Apply filtering based on active organization ID
      if (activeOrganizationId) {
        // If user has an active organization, show render jobs from that organization
        dataQuery = dataQuery.eq('organization_id', activeOrganizationId)
      } else {
        // If no active organization, show only user's own render jobs
        dataQuery = dataQuery.eq('user_id', userId)
      }

      const { data, error } = await dataQuery

      if (error) {
        console.error('❌ Database error in paginated render jobs API:', error)
        return NextResponse.json(
          { error: 'Database error', details: error.message },
          { status: 500 }
        )
      }

      console.log('✅ API: Successfully fetched paginated render jobs:', {
        count: (data || []).length,
        total: count || 0,
        page,
        limit,
      })

      return NextResponse.json({
        data: data || [],
        total: count || 0,
        page,
        limit,
        success: true,
      })
    } catch (dbError) {
      console.error('❌ Database query error:', dbError)
      return NextResponse.json(
        { error: 'Database query failed' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('❌ API error in paginated render jobs route:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
