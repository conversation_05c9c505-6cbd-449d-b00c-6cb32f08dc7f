import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import {
  getDisplayName,
  getOrganizationLogo,
  generateOrganizationSlug,
} from '@/lib/email-utils'

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user already has an organization
    const existingOrg = await auth.api.getFullOrganization({
      headers: await request.headers,
    })

    if (existingOrg) {
      return NextResponse.json({
        success: true,
        organization: existingOrg,
        message: 'User already has an organization',
      })
    }

    // Create organization using Better Auth API
    const orgName = getDisplayName(session.user.name || '', session.user.email)
    const orgSlug = generateOrganizationSlug(
      session.user.name || '',
      session.user.email
    )
    const orgLogo = getOrganizationLogo(
      session.user.name || '',
      session.user.email
    )

    const organization = await auth.api.createOrganization({
      body: {
        name: orgName,
        slug: orgSlug,
        logo: orgLogo,
        userId: session.user.id,
        keepCurrentActiveOrganization: true,
      },
      headers: await request.headers,
    })

    console.log(
      `Organization "${orgName}" created for user ${session.user.email}`
    )

    return NextResponse.json({
      success: true,
      organization,
      message: 'Organization created successfully',
    })
  } catch (error) {
    console.error('Error creating organization:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}
