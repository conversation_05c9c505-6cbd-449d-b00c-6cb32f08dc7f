import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const ELEVEN_API_KEY = process.env.ELEVEN_API_KEY

if (!ELEVEN_API_KEY) {
  console.error('Missing ELEVEN_API_KEY environment variable')
}

export interface ElevenVoice {
  voice_id: string
  name: string
  category?: string
  labels?: Record<string, string>
  description?: string
  preview_url?: string
}

export interface ElevenVoicesResponse {
  voices: ElevenVoice[]
  has_more?: boolean
  total_count?: number
  next_page_token?: string
}

async function fetchAllVoices(apiKey: string): Promise<ElevenVoice[]> {
  let voices: ElevenVoice[] = []
  let nextPageToken: string | undefined = undefined

  do {
    const url = new URL('https://api.elevenlabs.io/v2/voices')
    url.searchParams.set('page_size', '100')
    url.searchParams.set('sort', 'name')
    url.searchParams.set('sort_direction', 'asc')
    if (nextPageToken) url.searchParams.set('next_page_token', nextPageToken)

    const res = await fetch(url.toString(), {
      headers: {
        'xi-api-key': apiKey,
      },
    })

    if (!res.ok) {
      throw new Error(`Failed to fetch voices: ${res.status} ${res.statusText}`)
    }

    const data: ElevenVoicesResponse = await res.json()
    voices = voices.concat(data.voices)
    nextPageToken = data.next_page_token
  } while (nextPageToken)

  return voices
}

export async function GET() {
  try {
    if (!ELEVEN_API_KEY) {
      return createErrorResponse('ElevenLabs API key not configured', 500)
    }

    const voices = await fetchAllVoices(ELEVEN_API_KEY)

    return createCachedResponse({ voices }, CACHE_DURATIONS.VOICES)
  } catch (error) {
    console.error('ElevenLabs voices API error:', error)
    return createErrorResponse('Failed to fetch voices', 500)
  }
}
