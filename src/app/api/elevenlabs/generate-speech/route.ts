import { NextRequest, NextResponse } from 'next/server'
import {
  createErrorResponse,
  createCachedResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'
import * as hash from 'object-hash'
import FormData from 'form-data'
import axios from 'axios'
import { createClient } from '@supabase/supabase-js'

// Supabase client for uploads
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Helper function to upload audio buffer to Supabase storage
async function uploadAudioBufferToSupabase(
  audioBuffer: Buffer,
  filePath: string,
  bucket: string
): Promise<string> {
  try {
    const { error: uploadError } = await supabase.storage
      .from(bucket)
      .upload(filePath, audioBuffer, {
        contentType: 'audio/mpeg',
        upsert: false,
      })

    if (uploadError) {
      console.error('Supabase audio upload error:', uploadError)
      throw new Error(`Upload failed: ${uploadError.message}`)
    }

    // Construct and return the public URL
    const SUPABASE_URL =
      process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
    const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/${bucket}/${filePath}`

    console.log('✅ Audio uploaded to Supabase:', publicUrl)
    return publicUrl
  } catch (error) {
    console.error('Error uploading audio buffer to Supabase:', error)
    throw error
  }
}

const ELEVEN_API_KEY = process.env.ELEVEN_API_KEY

if (!ELEVEN_API_KEY) {
  console.error('Missing ELEVEN_API_KEY environment variable')
}

export interface GenerateSpeechParams {
  voice_id: string
  text: string
  model_id?: string
}

export interface GenerateSpeechResult {
  audioUrl: string // data:audio/mp3;base64,...
  alignment: {
    characters: string[]
    character_start_times_seconds: number[]
    character_end_times_seconds: number[]
  } // alignment object from API with separate arrays
}

// Helper function to generate TTS with timestamps
async function generateTTSWithTimestamps({
  text,
  voice_id,
  model_id,
  ELEVEN_API_KEY,
}: {
  text: string
  voice_id: string
  model_id?: string
  ELEVEN_API_KEY: string
}) {
  const url = `https://api.elevenlabs.io/v1/text-to-speech/${voice_id}/with-timestamps`
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'xi-api-key': ELEVEN_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      text,
      ...(model_id ? { model_id } : {}),
    }),
  })
  if (!response.ok) {
    const errorText = await response.text()
    console.error('ElevenLabs API error:', response.status, errorText)
    throw new Error(`Failed to generate speech: ${response.status}`)
  }
  return response.json()
}

export async function POST(request: NextRequest) {
  try {
    if (!ELEVEN_API_KEY) {
      return NextResponse.json(
        { error: 'ElevenLabs API key not configured' },
        { status: 500 }
      )
    }

    const {
      voice_id,
      text,
      model_id,
      userId,
    }: GenerateSpeechParams & { userId?: string } = await request.json()

    if (!voice_id || !text) {
      return NextResponse.json(
        { error: 'voice_id and text are required' },
        { status: 400 }
      )
    }

    // --- HASHED MP3 CACHE CHECK ---
    console.log('check1')
    if (userId) {
      console.log('check2')
      const requestHash = hash.MD5({ text, voiceId: voice_id })
      const bucket = 'assets'
      const filePath = `${userId}/tts/${requestHash}.mp3`
      const SUPABASE_URL =
        process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
      const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/${bucket}/${filePath}`
      const storageRes = await fetch(publicUrl)
      if (storageRes.ok) {
        // Run forced alignment
        const formData = new FormData()
        formData.append('file', Buffer.from(await storageRes.arrayBuffer()), {
          filename: 'audio.mp3',
          contentType: 'audio/mpeg',
        })
        formData.append('text', text)
        const alignmentRes = await axios.post(
          'https://api.elevenlabs.io/v1/forced-alignment',
          formData,
          {
            headers: {
              'xi-api-key': ELEVEN_API_KEY,
              ...formData.getHeaders(),
            },
          }
        )
        if (alignmentRes.status !== 200) {
          // Fallback: run old TTS generation logic
          try {
            const { audio_base64, alignment } = await generateTTSWithTimestamps(
              {
                text,
                voice_id,
                model_id,
                ELEVEN_API_KEY,
              }
            )
            const result: GenerateSpeechResult = {
              audioUrl: `data:audio/mp3;base64,${audio_base64}`,
              alignment,
            }
            return createCachedResponse(
              result,
              CACHE_DURATIONS.SPEECH_GENERATION
            )
          } catch (err) {
            return createErrorResponse(
              err instanceof Error ? err.message : 'Failed to generate speech',
              500
            )
          }
        }
        const alignment = alignmentRes.data
        return NextResponse.json({
          audioUrl: publicUrl,
          alignment,
        })
      }
    }

    try {
      const { audio_base64, alignment } = await generateTTSWithTimestamps({
        text,
        voice_id,
        model_id,
        ELEVEN_API_KEY,
      })

      // Upload to Supabase if userId is provided
      let audioUrl = `data:audio/mp3;base64,${audio_base64}`
      if (userId) {
        try {
          const audioBuffer = Buffer.from(audio_base64, 'base64')
          const requestHash = hash.MD5({ text, voiceId: voice_id })
          const bucket = 'assets'
          const filePath = `${userId}/tts/${requestHash}.mp3`

          audioUrl = await uploadAudioBufferToSupabase(
            audioBuffer,
            filePath,
            bucket
          )
        } catch (uploadError) {
          console.warn(
            'Failed to upload audio to Supabase, using base64:',
            uploadError
          )
          // Continue with base64 if upload fails
        }
      }

      const result: GenerateSpeechResult = {
        audioUrl,
        alignment,
      }
      // Don't cache speech generation responses (real-time data)
      return createCachedResponse(result, CACHE_DURATIONS.SPEECH_GENERATION)
    } catch (error) {
      console.error('ElevenLabs speech generation error:', error)
      return createErrorResponse('Failed to generate speech', 500)
    }
  } catch (error) {
    console.error('ElevenLabs speech generation error:', error)
    return createErrorResponse('Failed to generate speech', 500)
  }
}
