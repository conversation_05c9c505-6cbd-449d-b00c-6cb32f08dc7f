@import 'tailwindcss';
@import 'tw-animate-css';
@import 'flag-icons/css/flag-icons.min.css';
@import '../components/editor/themes/editor-theme.css';

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.99 0 0);
  --foreground: oklch(0 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0 0 0);
  --popover: oklch(0.99 0 0);
  --popover-foreground: oklch(0 0 0);
  --primary: oklch(0.5594 0.19 25.8625);
  --primary-foreground: oklch(0.9851 0 0);
  --secondary: oklch(0.94 0 0);
  --secondary-foreground: oklch(0.258 0.092 26.042);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.44 0 0);
  --accent: oklch(0.94 0 0);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.63 0.19 23.03);
  --destructive-foreground: oklch(1 0 0);
  --warning: oklch(0.81 0.17 75.35);
  --warning-foreground: oklch(0.2 0.05 85);
  --border: oklch(0.92 0 0);
  --input: oklch(0.94 0 0);
  --ring: oklch(0 0 0);
  --chart-1: oklch(0.81 0.17 75.35);
  --chart-2: oklch(0.55 0.22 264.53);
  --chart-3: oklch(0.72 0 0);
  --chart-4: oklch(0.92 0 0);
  --chart-5: oklch(0.56 0 0);
  --sidebar: oklch(0.99 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0 0 0);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.94 0 0);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0 0 0);
  --font-sans: Geist, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Geist Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
  --shadow:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
  --shadow-md:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18);
  --shadow-lg:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18);
  --shadow-xl:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1776 0 0);
  --foreground: oklch(0.9067 0 0);
  --card: oklch(0.2264 0 0);
  --card-foreground: oklch(0.9067 0 0);
  --popover: oklch(0.1776 0 0);
  --popover-foreground: oklch(0.9067 0 0);
  --primary: oklch(0.5594 0.19 25.8625);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.2931 0 0);
  --secondary-foreground: oklch(0.9791 0 0);
  --muted: oklch(0.2931 0 0);
  --muted-foreground: oklch(0.7058 0 0);
  --accent: oklch(0.3211 0 0);
  --accent-foreground: oklch(0.9791 0 0);
  --destructive: oklch(0.4271 0.1407 25.4523);
  --destructive-foreground: oklch(0.9791 0 0);
  --warning: oklch(0.81 0.17 75.35);
  --warning-foreground: oklch(0.95 0.02 85);
  --border: oklch(0.2931 0 0);
  --input: oklch(0.2931 0 0);
  --ring: oklch(0.6003 0.1859 24.6969);
  --chart-1: oklch(0.6003 0.1859 24.6969);
  --chart-2: oklch(0.5122 0.1679 25.3899);
  --chart-3: oklch(0.4271 0.1407 25.4523);
  --chart-4: oklch(0.3444 0.1086 24.9036);
  --chart-5: oklch(0.2571 0.0734 23.838);
  --sidebar: oklch(0.1913 0 0);
  --sidebar-foreground: oklch(0.7058 0 0);
  --sidebar-primary: oklch(0.6003 0.1859 24.6969);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.3211 0 0);
  --sidebar-accent-foreground: oklch(0.9791 0 0);
  --sidebar-border: oklch(0.2931 0 0);
  --sidebar-ring: oklch(0.6003 0.1859 24.6969);
  --font-sans: Roboto Flex, sans-serif;
  --font-serif: serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  --shadow-sm:
    0px 2px 4px 0px hsl(0 0% 0% / 0.5), 0px 1px 2px -1px hsl(0 0% 0% / 0.5);
  --shadow:
    0px 2px 4px 0px hsl(0 0% 0% / 0.5), 0px 1px 2px -1px hsl(0 0% 0% / 0.5);
  --shadow-md:
    0px 2px 4px 0px hsl(0 0% 0% / 0.5), 0px 2px 4px -1px hsl(0 0% 0% / 0.5);
  --shadow-lg:
    0px 2px 4px 0px hsl(0 0% 0% / 0.5), 0px 4px 6px -1px hsl(0 0% 0% / 0.5);
  --shadow-xl:
    0px 2px 4px 0px hsl(0 0% 0% / 0.5), 0px 8px 10px -1px hsl(0 0% 0% / 0.5);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 1.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-warning: var(--warning);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utility classes */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Elegant thin scrollbars */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.2) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.3);
}

.scrollbar-thin::-webkit-scrollbar-corner {
  background: transparent;
}

@layer utilities {
  .animation-delay-150 {
    animation-delay: 150ms;
  }

  .animation-delay-300 {
    animation-delay: 300ms;
  }

  .animation-delay-500 {
    animation-delay: 500ms;
  }

  /* Page transition shimmer effect */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
      opacity: 0.6;
    }
    50% {
      opacity: 1;
      transform: translateX(0%);
    }
    100% {
      transform: translateX(100%);
      opacity: 0.6;
    }
  }

  .animate-shimmer {
    animation: shimmer 1.5s ease-in-out infinite;
  }

  /* Alternative progress bar animation */
  @keyframes progressFill {
    0% {
      width: 0%;
      opacity: 1;
    }
    70% {
      width: 85%;
      opacity: 1;
    }
    90% {
      width: 95%;
      opacity: 1;
    }
    100% {
      width: 100%;
      opacity: 0;
    }
  }

  .animate-progress {
    animation: progressFill 3s ease-out forwards;
  }

  /* Performance-optimized animations */
  @keyframes fadeInWithDelay {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes progress {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes pulse-subtle {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .animate-fade-in-delayed {
    opacity: 0;
    animation: fadeInWithDelay 300ms 100ms forwards;
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-progress {
    animation: progress 1.5s infinite;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 2s infinite;
  }

  /* Hardware acceleration for smooth animations */
  .animate-shimmer,
  .animate-progress,
  .animate-pulse-subtle,
  .animate-fade-in-delayed {
    will-change: transform, opacity;
    transform: translateZ(0);
  }

  /* Clerk pricing table specific optimizations */
  @keyframes skeleton-wave {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .clerk-skeleton-wave {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    background-size: 200px 100%;
    animation: skeleton-wave 1.5s infinite;
  }

  /* Smooth transitions for Clerk pricing table loading */
  .clerk-pricing-transition {
    transition:
      opacity 0.3s ease-in-out,
      transform 0.3s ease-in-out;
  }

  /* Reduce motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .animate-shimmer,
    .animate-progress,
    .animate-pulse-subtle,
    .animate-fade-in-delayed {
      animation: none;
    }
  }
}
