export default function YouTubeAuthLoadingPage() {
  return (
    <html lang='en'>
      <head>
        <meta charSet='utf-8' />
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        <title>Connecting to YouTube...</title>
        <script src='https://cdn.tailwindcss.com' async></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              tailwind.config = {
                darkMode: 'class',
                theme: {
                  extend: {
                    colors: {
                      primary: {
                        DEFAULT: 'hsl(262.1 83.3% 57.8%)',
                        foreground: 'hsl(210 20% 98%)',
                      },
                      background: 'hsl(0 0% 100%)',
                      foreground: 'hsl(222.2 84% 4.9%)',
                      muted: {
                        DEFAULT: 'hsl(210 40% 96%)',
                        foreground: 'hsl(215.4 16.3% 46.9%)',
                      },
                      border: 'hsl(214.3 31.8% 91.4%)',
                    }
                  }
                }
              }
            `,
          }}
        />
      </head>
      <body className='bg-background text-foreground min-h-screen flex items-center justify-center p-4'>
        <div className='text-center space-y-6 max-w-sm mx-auto'>
          {/* YouTube Icon */}
          <div className='mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center'>
            <svg
              className='w-8 h-8 text-primary'
              viewBox='0 0 24 24'
              fill='currentColor'
            >
              <path d='M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z' />
            </svg>
          </div>

          {/* Loading Animation */}
          <div className='flex items-center justify-center space-x-2'>
            <svg
              className='w-5 h-5 animate-spin text-primary'
              viewBox='0 0 24 24'
              fill='none'
            >
              <circle
                className='opacity-25'
                cx='12'
                cy='12'
                r='10'
                stroke='currentColor'
                strokeWidth='4'
              ></circle>
              <path
                className='opacity-75'
                fill='currentColor'
                d='m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
              ></path>
            </svg>
            <span className='text-lg font-medium'>
              Connecting to YouTube...
            </span>
          </div>

          {/* Description */}
          <div className='space-y-2 text-sm text-muted-foreground'>
            <p>Please wait while we prepare your YouTube connection.</p>
            <p>
              You&apos;ll be redirected to Google&apos;s authorization page
              shortly.
            </p>
          </div>

          {/* Progress Indicator */}
          <div className='w-full bg-muted rounded-full h-2'>
            <div
              className='bg-primary h-2 rounded-full animate-pulse'
              style={{ width: '60%' }}
            ></div>
          </div>

          {/* Footer */}
          <div className='text-xs text-muted-foreground border-t pt-4'>
            <p>Secured by Google OAuth 2.0</p>
          </div>
        </div>

        {/* Script to handle navigation */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Listen for navigation messages from parent window
              window.addEventListener('message', function(event) {
                if (event.data.type === 'NAVIGATE_TO_OAUTH') {
                  window.location.href = event.data.url;
                }
              });

              // Notify parent that loading page is ready
              if (window.opener) {
                window.opener.postMessage({
                  type: 'YOUTUBE_LOADING_READY'
                }, window.location.origin);
              }
            `,
          }}
        />
      </body>
    </html>
  )
}
