// src/inngest/utils/content-extractor.ts
import jsdom from 'jsdom'
import { Readability } from '@mozilla/readability'
import TurndownService from 'turndown'
import url from 'url'
import { extractText, getDocumentProxy } from 'unpdf'
import { createClient } from '@supabase/supabase-js'

// Top-level supabase client for all helpers
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// --- Blog to Video Extraction Utility ---
export async function extractBlogMarkdownAndImages(
  blogUrl: string
): Promise<{ markdown: string; blog_images: string[] }> {
  try {
    // 1. Fetch HTML from browserless.io
    const browserlessKey = process.env.BROWSERLESS_IO_KEY
    if (!browserlessKey) throw new Error('Missing BROWSERLESS_IO_KEY')
    const endpoint = `https://production-sfo.browserless.io/content?token=${browserlessKey}`
    const blogdata = {
      url: blogUrl,
      gotoOptions: { waitUntil: 'networkidle2' },
    }
    const res = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
      body: JSON.stringify(blogdata),
    })
    console.log('res', res)
    if (!res.ok) throw new Error('Failed to fetch blog HTML')
    const html = await res.text()
    // If the response is not HTML, throw a clear error
    if (!html)
      throw new Error(
        'Browserless did not return valid HTML: ' + html.slice(0, 200)
      )

    // 2. Parse and extract main article with Readability
    const { JSDOM } = jsdom
    const dom = new JSDOM(html, { url: blogUrl })
    // Remove unwanted elements before Readability
    const unwantedTags = ['style', 'script', 'iframe', 'noscript']
    unwantedTags.forEach(tag => {
      dom.window.document.querySelectorAll(tag).forEach(el => el.remove())
    })
    const reader = new Readability(dom.window.document)
    const article = reader.parse()
    console.log('article', article)
    if (!article || !article.content)
      throw new Error('Readability failed to extract article')

    // 3. Extract and resolve all image URLs
    const tempDom = new JSDOM(article.content, { url: blogUrl })
    const imgNodes = tempDom.window.document.querySelectorAll('img')
    const blog_images: string[] = []
    imgNodes.forEach((img: HTMLImageElement) => {
      let src = img.getAttribute('src') || ''
      if (src) {
        // Resolve relative URLs
        src = url.resolve(blogUrl, src)
        blog_images.push(src)
      }
    })

    // 4. Convert to Markdown with turndown, removing image markdown
    const turndownService = new TurndownService()
    // Remove images from markdown
    turndownService.addRule('noImages', {
      filter: 'img',
      replacement: () => '',
    })
    let markdown = turndownService.turndown(article.content)

    // 5. Trim Markdown for LLM context window (GPT-4o mini ~12k tokens)
    const maxChars = 16000 // ~4 tokens per word, safe margin
    if (markdown.length > maxChars) {
      markdown = markdown.slice(0, maxChars) + '\n\n...[truncated]'
    }
    console.log('markdown', markdown)

    return { markdown, blog_images }
  } catch (err) {
    console.error('Blog extraction failed:', err)
    throw new Error('Blog extraction failed')
  }
}

// --- PDF to Video Extraction Utility ---
export async function extractPDFMarkdownAndImages(
  pdfUrl: string
): Promise<{ markdown: string; pdf_images: string[] }> {
  try {
    console.log('Starting PDF extraction from:', pdfUrl)

    // Validate the PDF URL
    if (!pdfUrl || !pdfUrl.startsWith('http')) {
      throw new Error(`Invalid PDF URL: ${pdfUrl}`)
    }

    // Debug: Analyze the URL structure
    const urlParts = pdfUrl.split('/')
    console.log('URL parts:', urlParts)
    console.log(
      'Checking for public endpoint:',
      pdfUrl.includes('/storage/v1/object/public/')
    )

    // 1. Download PDF from Supabase storage
    console.log('Fetching PDF from URL:', pdfUrl)
    const response = await fetch(pdfUrl)
    console.log('PDF fetch response status:', response.status)
    console.log(
      'PDF fetch response headers:',
      Object.fromEntries(response.headers.entries())
    )

    if (!response.ok) {
      throw new Error(
        `Failed to fetch PDF: ${response.status} ${response.statusText}`
      )
    }

    const pdfBuffer = Buffer.from(await response.arrayBuffer())
    console.log('PDF downloaded, size:', pdfBuffer.length, 'bytes')

    if (pdfBuffer.length === 0) {
      throw new Error('Downloaded PDF is empty')
    }

    // 2. Parse PDF content using unpdf (serverless-optimized)
    // unpdf is designed for serverless environments and doesn't have test file issues
    const pdf = await getDocumentProxy(new Uint8Array(pdfBuffer))
    const { totalPages, text } = await extractText(pdf, {
      mergePages: true,
    })

    console.log('PDF parsed - pages:', totalPages, 'text length:', text.length)

    if (!text || text.trim().length === 0) {
      throw new Error('No extractable text found in PDF')
    }

    // 3. Extract images (basic implementation - pdf-parse doesn't extract images directly)
    // For now, we'll collect any embedded image references or use an empty array
    const pdf_images: string[] = []
    // Note: Advanced PDF image extraction would require additional libraries
    // like pdf2pic or pdf-poppler, which we can add later if needed

    // 4. Clean and prepare text content
    const textContent = text
      .replace(/\r\n/g, '\n') // Normalize line endings
      .replace(/\n{3,}/g, '\n\n') // Reduce excessive line breaks
      .trim()

    // 5. Convert text to Markdown format for better LLM processing
    const turndownService = new TurndownService()

    // Create a simple HTML structure from the text to convert to markdown
    const htmlContent = textContent
      .split('\n\n')
      .map((paragraph: string) => {
        if (paragraph.trim()) {
          // Simple heuristic for headings (lines with fewer than 60 chars and no periods)
          if (
            paragraph.length < 60 &&
            !paragraph.includes('.') &&
            paragraph.trim().length > 0
          ) {
            return `<h3>${paragraph.trim()}</h3>`
          }
          return `<p>${paragraph.trim()}</p>`
        }
        return ''
      })
      .filter(Boolean)
      .join('\n')

    let markdown = turndownService.turndown(htmlContent)

    // 7. Trim Markdown for LLM context window (GPT-4o mini ~12k tokens)
    const maxChars = 16000 // ~4 tokens per word, safe margin
    if (markdown.length > maxChars) {
      markdown = markdown.slice(0, maxChars) + '\n\n...[truncated]'
    }

    console.log('PDF extraction complete - markdown length:', markdown.length)

    // 8. Delete PDF from storage to save bandwidth and costs
    try {
      // Extract the file path from the Supabase public URL
      // URL format: https://project.supabase.co/storage/v1/object/public/pdfs/timestamp-filename.pdf
      const urlParts = pdfUrl.split('/')

      // Find the index where 'public' appears followed by 'pdfs'
      const publicIndex = urlParts.findIndex(part => part === 'public')
      const pdfsIndex = urlParts.findIndex(
        (part, index) => part === 'pdfs' && index > publicIndex
      )

      if (publicIndex === -1 || pdfsIndex === -1) {
        console.error('Invalid Supabase URL format:', pdfUrl)
        throw new Error('Could not parse PDF URL for deletion')
      }

      // Get ONLY the filename (everything after 'public/pdfs/')
      // The path should NOT include the bucket name 'pdfs'
      const filePath = urlParts.slice(pdfsIndex + 1).join('/')

      const decodedFilePath = decodeURIComponent(filePath)
      console.log('Extracted file path for deletion:', decodedFilePath)
      // Import Supabase client for deletion (using policy-based permissions)
      const { data, error: deleteError } = await supabase.storage
        .from('pdfs')
        .remove([decodedFilePath])

      if (deleteError) {
        console.error('Failed to delete PDF from storage:', deleteError)
        console.error(
          'Delete error details:',
          JSON.stringify(deleteError, null, 2)
        )
        console.error('Attempted file path:', decodedFilePath)
      } else {
        console.log('PDF successfully deleted from storage:', decodedFilePath)
        console.log('Delete operation result:', data)
      }
    } catch (deleteError) {
      console.error('Failed to delete PDF from storage:', deleteError)
      // Don't throw here as the main process should continue
    }

    return { markdown, pdf_images }
  } catch (err) {
    console.error('PDF extraction failed:', err)
    throw new Error(
      `PDF extraction failed: ${err instanceof Error ? err.message : 'Unknown error'}`
    )
  }
}
