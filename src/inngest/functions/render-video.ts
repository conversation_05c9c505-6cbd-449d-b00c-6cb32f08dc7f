import { inngest } from '../client'
import { createClient } from '@supabase/supabase-js'
import {
  updateRenderStatus,
  renderWithLambda,
  renderWithCloudRun,
} from '../utils/render-utils'
import { incrementUsage } from '@/lib/usage-utils'
import { transformProjectDataToInputProps } from '@/lib/project-data-transformer'
import { sendEmail, createVideoCompletionEmailTemplate } from '@/lib/email'

type RenderInputProps = {
  durationInFrames: number
  exportName?: string | null
  exportResolution?: string | null
} & Record<string, unknown>

type RenderEventPayload = {
  inputProps: RenderInputProps
  projectId?: string
  userId: string
  organizationId?: string | null
  userEmail?: string
  userName?: string
}

// Top-level supabase client for all helpers
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export const renderVideo = inngest.createFunction(
  {
    id: 'render-video',
    retries: 1, // Cap retries to only 1 attempt
  },
  { event: 'render-video' },
  async ({ event }) => {
    let renderRecord: string | null = null

    try {
      console.log('🚀 Starting render-video function execution')

      // Load project data and reconstruct payload
      console.log('📁 Loading project data...')
      const {
        projectId,
        userId,
        organizationId,
        exportResolution,
        duration,
        subtitlePosition,
        compositionHeight,
        compositionWidth,
        durationInFrames,
        exportName,
        userEmail: eventUserEmail,
        userName: eventUserName,
      } = event.data

      let fullPayload: RenderEventPayload

      if (projectId) {
        // New format: load project data from database
        console.log('Loading project data from database:', projectId)

        const { data: project, error } = await supabase
          .from('projects')
          .select('*')
          .eq('project_id', projectId)
          .single()

        if (error) {
          throw new Error(`Failed to load project: ${error.message}`)
        }

        if (!project) {
          throw new Error(`Project not found: ${projectId}`)
        }

        console.log('✅ Project data loaded from database')

        // Transform project data to match expected input props format
        const inputProps: RenderInputProps = transformProjectDataToInputProps(
          project,
          {
            exportResolution,
            duration,
            subtitlePosition,
            compositionHeight,
            compositionWidth,
            durationInFrames,
            exportName,
          }
        ) as RenderInputProps

        fullPayload = {
          inputProps,
          projectId,
          userId,
          organizationId,
          userEmail: eventUserEmail,
          userName: eventUserName,
        }
      } else {
        // Legacy format: payload is directly in event.data
        console.log('Using legacy payload format')
        fullPayload = event.data as RenderEventPayload
      }

      const {
        inputProps,
        userId: finalUserId,
        organizationId: finalOrgId,
        userEmail: finalUserEmail,
        userName: finalUserName,
      } = fullPayload
      const finalProjectId = fullPayload.projectId || 'legacy-project'

      // Initialize render record
      console.log('🏗️ Initializing render record...')
      const renderData = {
        project_id: finalProjectId,
        user_id: finalUserId,
        organization_id: finalOrgId || null,
        status: 'initializing',
        progress: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        export_name: inputProps.exportName || null,
        export_resolution: inputProps.exportResolution || null,
        render_method: null, // will update after method is chosen
      }

      const { data: renderJobData, error: createError } = await supabase
        .from('render_jobs')
        .insert(renderData)
        .select('id')
        .single()

      if (createError) {
        console.error('❌ Error creating render job:', createError)
        throw createError
      }

      renderRecord = renderJobData.id
      console.log('✅ Render record created:', renderRecord)

      // Determine render method based on duration
      console.log('🎯 Determining render method...')
      const estimatedDuration = inputProps.durationInFrames / 30 // Assuming 30fps
      // const renderMethod = estimatedDuration > 1800 ? 'cloudrun' : 'lambda' // 30 minutes
      const renderMethod = 'lambda' // for now lambda only, there is gpu support in cloudrun but that is not working as expected
      console.log(
        `📊 Estimated duration: ${estimatedDuration}s, using method: ${renderMethod}`
      )

      // Update renderMethod in DB
      const { error: methodError } = await supabase
        .from('render_jobs')
        .update({ render_method: renderMethod })
        .eq('id', renderRecord!)

      if (methodError) {
        console.error('❌ Error updating render method:', methodError)
        throw methodError
      }

      console.log('✅ Render method updated in database')

      // Update status to rendering
      console.log('📈 Updating status to rendering...')
      await updateRenderStatus(renderRecord!, 'rendering', 1)
      console.log('✅ Status updated to rendering')

      // Render video
      console.log('🎬 Starting video render process...')
      let result: { publicUrl: string }

      if (!renderRecord) throw new Error('renderRecord is null')

      if (renderMethod === 'lambda') {
        console.log('☁️ Rendering with Lambda...')
        result = await renderWithLambda(
          inputProps,
          renderRecord,
          finalUserId,
          finalProjectId
        )
      } else {
        console.log('🏃 Rendering with CloudRun...')
        result = await renderWithCloudRun(
          inputProps,
          renderRecord,
          finalUserId,
          finalProjectId
        )
      }

      console.log('✅ Video render completed successfully')

      // Update final status
      console.log('🏁 Finalizing render status...')
      await updateRenderStatus(
        renderRecord!,
        'completed',
        100,
        result.publicUrl
      )
      console.log('✅ Final status updated')

      // Increment usage for video export
      console.log('📊 Updating usage tracking...')
      try {
        // Use organization ID if available, otherwise use user ID
        const referenceId = finalOrgId || finalUserId
        await incrementUsage(referenceId, 'videoExports', 1)
        console.log('✅ Usage incremented for video export')
      } catch (usageError) {
        console.error(
          '❌ Failed to increment usage for video export:',
          usageError
        )
        // Don't fail the entire operation if usage tracking fails
      }

      // Send completion email to user
      console.log('📧 Sending video completion email...')
      try {
        if (finalUserEmail) {
          const videoName = inputProps.exportName || 'Your Video'

          const { html, text } = createVideoCompletionEmailTemplate({
            userName: finalUserName || undefined,
            videoName,
            projectId: finalProjectId,
            // downloadUrl: result.publicUrl,
          })

          await sendEmail({
            to: finalUserEmail,
            subject: '🎬 Your video is ready! - Adori AI',
            text,
            html,
          })

          console.log('✅ Video completion email sent to:', finalUserEmail)
        } else {
          // Fallback: try to get user email from database
          console.log(
            '⚠️ No user email in payload, trying database fallback...'
          )
          try {
            const { data: user, error: userError } = await supabase
              .from('user')
              .select('email, name')
              .eq('id', finalUserId)
              .single()

            if (userError) {
              console.error('❌ Error fetching user data for email:', userError)
            } else if (user?.email) {
              const videoName = inputProps.exportName || 'Your Video'

              const { html, text } = createVideoCompletionEmailTemplate({
                userName: user.name || undefined,
                videoName,
                projectId: finalProjectId,
                // downloadUrl: result.publicUrl,
              })

              await sendEmail({
                to: user.email,
                subject: '🎬 Your video is ready! - Adori AI',
                text,
                html,
              })

              console.log('✅ Video completion email sent to:', user.email)
            } else {
              console.log(
                '⚠️ No user email found in database either, skipping email notification'
              )
            }
          } catch (fallbackError) {
            console.error(
              '❌ Failed to send email via database fallback:',
              fallbackError
            )
          }
        }
      } catch (emailError) {
        console.error('❌ Failed to send video completion email:', emailError)
        // Don't fail the entire operation if email fails
      }

      console.log('🎉 Render-video function completed successfully')

      return {
        renderId: renderRecord,
        publicUrl: result.publicUrl,
        status: 'completed',
      }
    } catch (error) {
      console.error('💥 Render failed:', error)
      if (typeof renderRecord === 'string') {
        console.log('🔄 Updating render status to failed...')
        try {
          await updateRenderStatus(
            renderRecord,
            'failed',
            0,
            undefined,
            (error as Error).message
          )
          console.log('✅ Error status updated in database')
        } catch (statusError) {
          console.error('❌ Failed to update error status:', statusError)
        }
      }

      throw error
    }
  }
)
