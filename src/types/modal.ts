/**
 * Modal Types
 *
 * This file contains type definitions related to the modal system.
 */
import { BillingPeriod } from './pricing'

// Modal sizes used for responsive design
export type ModalSize =
  | 'sm'
  | 'md'
  | 'lg'
  | 'xl'
  | '2xl'
  | '3xl'
  | '4xl'
  | '5xl'
  | '6xl'
  | '7xl'

// Modal types that can be opened in the application
export type ModalType =
  | 'createVideo'
  | 'settings'
  | 'pricing'
  | 'confirmation'
  | 'upgrade'

// Data that can be passed to a modal
export interface ModalData {
  title?: string
  description?: string
  confirmLabel?: string
  cancelLabel?: string
  onConfirm?: () => void
  initialBillingPeriod?: BillingPeriod
  // Upgrade modal specific data
  feature?: string
  featureName?: string
  upgradeMessage?: string
  currentPlan?: string
  // Allow for additional properties
  [key: string]: unknown
}

// Configuration for different modal types
export type ModalConfigType = {
  [key in ModalType]?: {
    title: string
    size: ModalSize
  }
}

// Default modal configuration
export const DEFAULT_MODAL_CONFIG = {
  title: '',
  size: 'md' as ModalSize,
}
