/**
 * Pricing Types
 *
 * This file contains type definitions related to the pricing system.
 */

// Billing period options
export type BillingPeriod = 'monthly' | 'annual'

// Subscription plan structure
export interface Plan {
  name: string
  price: string
  annualPrice?: string
  annualSavings?: string
  billingPeriod: string
  description: string
  highlightTag: string | null
  videoCreation: number
  videoDownloads: number
  maxVideoLength: string
  resolution: string
  aiImageGenerator: string
  storage: string
  publishToYoutube: boolean
  proVoices: boolean
  voiceCloning: boolean
  curationSupport: string | false
  buttonText: string
  buttonVariant: 'default' | 'outline'
}
