export interface Text {
  id: string
  content: string
  style?: {
    fontFamily?: string
    fontSize?: number
    color?: string
    alignment?: 'left' | 'center' | 'right'
  }
}

export interface Media {
  id: string
  type: 'image' | 'video'
  url: string
  position: {
    x: number
    y: number
  }
  size: {
    width: number
    height: number
  }
  startTime: number
  endTime: number
  thumbnail?: string
  duration?: number | string
  fit?: 'blur' | 'crop' | 'contain'
  transition?: TransitionType
  kenBurns?:
    | 'none'
    | 'zoom-in'
    | 'zoom-out'
    | 'pan-left'
    | 'pan-right'
    | 'pan-up'
    | 'pan-down'
  effectDuration?: number
}

export type TransitionType =
  | 'none'
  | 'random'
  | 'fade'
  | 'fadeblack'
  | 'fadewhite'
  | 'distance'
  | 'wipeleft'
  | 'wiperight'
  | 'wipeup'
  | 'wipedown'
  | 'slideleft'
  | 'slideright'
  | 'slideup'
  | 'slidedown'
  | 'smoothleft'
  | 'smoothright'
  | 'smoothup'
  | 'smoothdown'
  | 'rectcrop'
  | 'circlecrop'
  | 'circleclose'
  | 'circleopen'
  | 'horzclose'
  | 'horzopen'
  | 'vertclose'
  | 'vertopen'
  | 'dissolve'
  | 'pixelize'
  | 'radial'

export interface Scene {
  id: string
  text: string
  name: string
  duration: number
  originalDuration?: number // Base duration before any speed adjustments
  voiceSettings: {
    voiceId: string
    voiceUrl: string
    voiceVol: number
    voiceName: string
    voiceSpeed: number
    alignment?: {
      characters: string[]
      character_start_times_seconds: number[]
      character_end_times_seconds: number[]
    }
  }
  voiceover?: {
    audioUrl: string
    audioDuration: number
    volume: number
    speed: number
  }
  captions?: Array<{
    start: number
    end: number
    text: string
    words: Array<{
      start: number
      end: number
      word: string
    }>
  }>
  texts: Text[]
  media?: Media | null
  startOffset?: number
}

export interface SubtitlePosition {
  x: number
  y: number
}

export interface SubtitleStyle {
  id: string
  name: string
  fontFamily: string
  fontSize: number
  fontWeight: 'normal' | 'bold'
  fontStyle: 'normal' | 'italic'
  textColor: string
  highlightColor: string
  backgroundColor: string
  backgroundOpacity: number
  animation:
    | 'none'
    | 'fade'
    | 'slide-up'
    | 'bounce'
    | 'typewriter'
    | 'color-up'
    | 'bounce-out'
  textAlign: 'left' | 'center' | 'right'
  textShadow: boolean
  borderRadius: number
  padding: number
  maxWidth: number
  isCustom: boolean
  createdAt?: string
}

export interface SubtitleSegment {
  id: string
  sceneId: string
  text: string
  startTime: number
  endTime: number
  words: Array<{
    word: string
    start: number
    end: number
  }>
}

export interface VideoStudioState {
  scenes: Scene[]
  currentScene: string
  subtitlePosition: SubtitlePosition
}

export interface RenderRequest {
  scenes: Scene[]
  subtitlePosition: SubtitlePosition
}

export interface TransitionEffect {
  type: TransitionType
  active: boolean
  frame: number
}
