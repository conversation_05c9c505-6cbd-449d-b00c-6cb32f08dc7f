/* eslint-disable @typescript-eslint/no-explicit-any */
import { authClient } from '@/lib/auth-client'

// Session cache to prevent excessive API calls
let sessionCache: any = null
let sessionCacheExpiry = 0
const CACHE_DURATION = 30000 // 30 seconds

/**
 * Get cached session or fetch new one
 */
async function getCachedSession() {
  const now = Date.now()

  // Return cached session if still valid
  if (sessionCache && now < sessionCacheExpiry) {
    return sessionCache
  }

  // Fetch new session and cache it
  try {
    const session = await authClient.getSession()
    sessionCache = session
    sessionCacheExpiry = now + CACHE_DURATION
    return session
  } catch (error) {
    console.error('Error fetching session:', error)
    return null
  }
}

/**
 * Clear session cache (call when session changes)
 */
export function clearSessionCache() {
  sessionCache = null
  sessionCacheExpiry = 0
}

/**
 * Check if the current user is an admin
 */
export async function isCurrentUserAdmin(): Promise<boolean> {
  try {
    const session = await getCachedSession()

    if (!session?.data?.user) return false

    return isUserAdmin(session?.data?.user.role)
  } catch (error) {
    console.error('Error checking admin status:', error)
    return false
  }
}

/**
 * Check if a user role is admin
 */
export function isUserAdmin(role?: string | null): boolean {
  if (!role) return false

  const adminRoles = ['admin', 'superadmin']
  const userRoles = role.split(',').map(r => r.trim())

  return userRoles.some(userRole => adminRoles.includes(userRole))
}

/**
 * Get user session with admin check
 */
export async function getAdminSession() {
  const session = await getCachedSession()
  if (!session?.data?.user) {
    throw new Error('Not authenticated')
  }

  const isAdmin = isUserAdmin(session.data.user.role)
  if (!isAdmin) {
    throw new Error('Insufficient permissions')
  }

  return session
}

/**
 * Admin action wrapper that checks permissions
 */
export async function withAdminPermission<T>(
  action: () => Promise<T>
): Promise<T> {
  await getAdminSession()
  return action()
}

/**
 * Check if current session is impersonating another user
 */
export async function isImpersonating(): Promise<boolean> {
  try {
    const session = await getCachedSession()
    // Access the session data structure properly
    const sessionData = session as any
    return !!sessionData?.data?.session?.impersonatedBy
  } catch (error) {
    console.error('Error checking impersonation status:', error)
    return false
  }
}

/**
 * Get the original admin user ID if impersonating
 */
export async function getImpersonatingAdminId(): Promise<string | null> {
  try {
    const session = await getCachedSession()
    // Access the session data structure properly
    const sessionData = session as any
    return sessionData?.data?.session?.impersonatedBy || null
  } catch (error) {
    console.error('Error getting impersonating admin ID:', error)
    return null
  }
}

/**
 * Format user role for display
 */
export function formatUserRole(role?: string | null): string {
  if (!role) return 'User'

  const roles = role.split(',').map(r => r.trim())
  return roles.map(r => r.charAt(0).toUpperCase() + r.slice(1)).join(', ')
}

/**
 * Check if user has specific admin permission
 */
export async function hasAdminPermission(
  permission: Record<string, string[]>,
  userId?: string
): Promise<boolean> {
  try {
    const result = await authClient.admin.hasPermission({
      userId,
      permissions: permission,
    })

    // Handle the result structure properly
    const resultData = result as any
    return resultData?.success || false
  } catch (error) {
    console.error('Error checking admin permission:', error)
    return false
  }
}
