/**
 * Fast client-side thumbnail generation using HTML5 video and canvas
 * This avoids server-side FFmpeg processing and timeout issues
 */

export interface ThumbnailOptions {
  width?: number
  height?: number
  quality?: number
  timeOffset?: number // seconds from start
}

export interface ThumbnailResult {
  blob: Blob
  dataUrl: string
  width: number
  height: number
}

/**
 * Generate thumbnail from video file using HTML5 video element
 * This is much faster than server-side FFmpeg processing
 */
export function generateVideoThumbnail(
  file: File,
  options: ThumbnailOptions = {}
): Promise<ThumbnailResult> {
  const { width = 400, height = 300, quality = 0.8, timeOffset = 1 } = options

  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    video.preload = 'metadata'
    video.muted = true
    video.crossOrigin = 'anonymous'

    // Set canvas dimensions
    canvas.width = width
    canvas.height = height

    video.onloadedmetadata = () => {
      // Seek to the specified time offset
      video.currentTime = Math.min(timeOffset, video.duration * 0.1)
    }

    video.onseeked = () => {
      try {
        // Use actual video dimensions for proper aspect ratio
        const videoWidth = video.videoWidth
        const videoHeight = video.videoHeight
        const videoAspect = videoWidth / videoHeight

        // Calculate thumbnail dimensions maintaining aspect ratio
        let thumbWidth = width
        let thumbHeight = height

        if (videoAspect > 1) {
          // Landscape video
          thumbHeight = Math.round(width / videoAspect)
        } else {
          // Portrait or square video
          thumbWidth = Math.round(height * videoAspect)
        }

        // Set canvas to actual thumbnail dimensions (not fixed 400x300)
        canvas.width = thumbWidth
        canvas.height = thumbHeight

        console.log(
          `Video thumbnail: ${videoWidth}x${videoHeight} (aspect: ${videoAspect.toFixed(2)}) -> ${thumbWidth}x${thumbHeight}`
        )

        // Fill background with black
        ctx.fillStyle = '#000000'
        ctx.fillRect(0, 0, thumbWidth, thumbHeight)

        // Draw video frame to fill entire canvas
        ctx.drawImage(video, 0, 0, thumbWidth, thumbHeight)

        // Convert canvas to blob
        canvas.toBlob(
          blob => {
            if (blob) {
              const dataUrl = canvas.toDataURL('image/jpeg', quality)
              resolve({
                blob,
                dataUrl,
                width: thumbWidth,
                height: thumbHeight,
              })
            } else {
              reject(new Error('Failed to create thumbnail blob'))
            }

            // Clean up
            URL.revokeObjectURL(video.src)
          },
          'image/jpeg',
          quality
        )
      } catch (error) {
        URL.revokeObjectURL(video.src)
        reject(error)
      }
    }

    video.onerror = () => {
      URL.revokeObjectURL(video.src)
      reject(new Error('Failed to load video for thumbnail'))
    }

    video.ontimeupdate = () => {
      // Sometimes onseeked doesn't fire, so we use timeupdate as backup
      if (Math.abs(video.currentTime - timeOffset) < 0.1) {
        video.ontimeupdate = null // Remove listener
        if (video.onseeked) {
          video.onseeked(new Event('seeked')) // Trigger the thumbnail generation
        }
      }
    }

    // Start loading the video
    video.src = URL.createObjectURL(file)
  })
}

/**
 * Generate thumbnail from image file using canvas
 * Resizes and optimizes the image
 */
export function generateImageThumbnail(
  file: File,
  options: ThumbnailOptions = {}
): Promise<ThumbnailResult> {
  const { width = 400, height = 300, quality = 0.8 } = options

  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      // Calculate dimensions to maintain aspect ratio
      const imgAspect = img.naturalWidth / img.naturalHeight
      const canvasAspect = width / height

      let drawWidth = width
      let drawHeight = height
      let offsetX = 0
      let offsetY = 0

      if (imgAspect > canvasAspect) {
        // Image is wider than canvas
        drawHeight = width / imgAspect
        offsetY = (height - drawHeight) / 2
      } else {
        // Image is taller than canvas
        drawWidth = height * imgAspect
        offsetX = (width - drawWidth) / 2
      }

      // Set canvas dimensions
      canvas.width = width
      canvas.height = height

      // Fill background with white for images
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, width, height)

      // Draw and resize image
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight)

      // Convert to blob
      canvas.toBlob(
        blob => {
          if (blob) {
            const dataUrl = canvas.toDataURL('image/jpeg', quality)
            resolve({
              blob,
              dataUrl,
              width,
              height,
            })
          } else {
            reject(new Error('Failed to create thumbnail blob'))
          }

          // Clean up
          URL.revokeObjectURL(img.src)
        },
        'image/jpeg',
        quality
      )
    }

    img.onerror = () => {
      URL.revokeObjectURL(img.src)
      reject(new Error('Failed to load image for thumbnail'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * Extract basic metadata from video file
 */
export function extractVideoMetadata(file: File): Promise<{
  width: number
  height: number
  duration: number
  aspectRatio: number
  orientation: 'landscape' | 'portrait' | 'square'
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.preload = 'metadata'
    video.muted = true

    video.onloadedmetadata = () => {
      const width = video.videoWidth
      const height = video.videoHeight
      const duration = video.duration
      const aspectRatio = width / height

      let orientation: 'landscape' | 'portrait' | 'square'
      if (aspectRatio > 1.1) {
        orientation = 'landscape'
      } else if (aspectRatio < 0.9) {
        orientation = 'portrait'
      } else {
        orientation = 'square'
      }

      resolve({
        width,
        height,
        duration,
        aspectRatio,
        orientation,
      })

      // Clean up
      URL.revokeObjectURL(video.src)
    }

    video.onerror = () => {
      URL.revokeObjectURL(video.src)
      reject(new Error('Failed to load video metadata'))
    }

    video.src = URL.createObjectURL(file)
  })
}

/**
 * Extract basic metadata from image file
 */
export function extractImageMetadata(file: File): Promise<{
  width: number
  height: number
  aspectRatio: number
  orientation: 'landscape' | 'portrait' | 'square'
}> {
  return new Promise((resolve, reject) => {
    const img = new Image()

    img.onload = () => {
      const width = img.naturalWidth
      const height = img.naturalHeight
      const aspectRatio = width / height

      let orientation: 'landscape' | 'portrait' | 'square'
      if (aspectRatio > 1.1) {
        orientation = 'landscape'
      } else if (aspectRatio < 0.9) {
        orientation = 'portrait'
      } else {
        orientation = 'square'
      }

      resolve({
        width,
        height,
        aspectRatio,
        orientation,
      })

      // Clean up
      URL.revokeObjectURL(img.src)
    }

    img.onerror = () => {
      URL.revokeObjectURL(img.src)
      reject(new Error('Failed to load image metadata'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * Determine video quality based on dimensions
 */
export function determineVideoQuality(width: number, height: number): string {
  const pixels = width * height

  if (pixels >= 3840 * 2160) return 'uhd' // 4K
  if (pixels >= 1920 * 1080) return 'hd' // 1080p
  if (pixels >= 1280 * 720) return 'hd' // 720p
  if (pixels >= 854 * 480) return 'sd' // 480p
  return 'sd' // Lower quality
}
