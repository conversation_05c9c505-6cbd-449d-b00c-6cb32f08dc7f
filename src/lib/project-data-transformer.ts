/**
 * Project Data Transformation Utilities
 * 
 * This module provides utilities for transforming project data from the database format
 * into the input props format expected by the video rendering system.
 * 
 * Key transformations:
 * - Database project structure → Rendering input props
 * - Music data → selectedMusic format
 * - Caption settings → captionStyle format
 * - Scene data → Enhanced scene format with voiceover, media, and captions
 */

// Type definitions for database project structure
export interface ProjectData {
  projectId: string
  userId: string
  organizationId?: string | null
  projectName: string
  method: string
  createdAt: string
  updatedAt: string
  coverColor: string | null
  coverPic: string | null
  orientation: string
  duration: number
  summary: string | null
  voiceRegenerations: number
  speech: {
    enabled: boolean
    src: string
    name: string
    volume: number
    transcript: {
      captions: Array<{
        start: number
        end: number
        sentence: string
        wordBoundries: Array<{ start: number; end: number; word: string }>
      }>
      status: string
    }
  } | null
  backgroundVideo: {
    src: string
    muted: boolean
  } | null
  music: {
    enabled: boolean
    src: string
    volume: number
    duration: number
    name: string
  } | null
  caption_settings: {
    enabled: boolean
    fontFamily: string
    fontSize: number
    fontWeight: string
    fontStyle: string
    textColor: string
    highlightColor: string
    backgroundColor: string
    backgroundOpacity: number
    textAlign: string
    textShadow: boolean
    borderRadius: number
    padding: number
    maxWidth: number
    animation: string
  } | null
  scenes: DatabaseScene[]
  blogImages: string[]
}

export interface DatabaseScene {
  id: string
  text: string
  title?: string
  duration: number
  startOffset?: number
  voiceSettings?: {
    voiceId: string
    voiceUrl: string
    voiceVol: number
    voiceName: string
    voiceSpeed: number
  }
  captions?: Array<{
    start: number
    end: number
    sentence?: string
    wordBoundries?: Array<{
      start: number
      end: number
      word: string
    }>
  }>
  media?: {
    type: string
    url: string
    position?: { x: number; y: number }
    size?: { width: number; height: number }
    thumbnail?: string
    fit?: string
    kenBurns?: string
    transition?: {
      type: string
      duration: number
    }
  }
}

export interface RenderParams {
  duration: number
  exportResolution: string
  subtitlePosition: { x: number; y: number }
  compositionHeight: number
  compositionWidth: number
  exportName: string
  durationInFrames: number
}

/**
 * Transforms raw project data from Supabase into the input props format
 * expected by the video rendering system
 * 
 * @param project - The project data from the database
 * @param params - Rendering parameters (composition size, export settings, etc.)
 * @returns Transformed input props ready for video rendering
 */
export function transformProjectDataToInputProps(
  project: ProjectData,
  params: RenderParams
) {
  const {
    exportResolution,
    subtitlePosition,
    compositionHeight,
    compositionWidth,
    durationInFrames,
    exportName,
    duration,
  } = params

  // Transform music data to selectedMusic format
  const selectedMusic = project.music
    ? {
        id: 'api-music',
        title: project.music.name || 'Project Music',
        genre: 'Background',
        mood: 'Neutral',
        artistName: 'Unknown',
        provider: 'API',
        licenseId: 'api',
        previewUrl: project.music.src,
        durationMillis: (project.music.duration || 0) * 1000,
      }
    : null

  // Transform caption settings to captionStyle format
  const captionStyle = project.caption_settings
    ? {
        id: 'custom-preview',
        name: 'Live Preview',
        isCustom: true,
        createdAt: new Date().toISOString(),
        fontFamily: project.caption_settings.fontFamily,
        fontSize: project.caption_settings.fontSize,
        fontWeight: project.caption_settings.fontWeight as 'normal' | 'bold',
        fontStyle: project.caption_settings.fontStyle as 'normal' | 'italic',
        textColor: project.caption_settings.textColor,
        highlightColor: project.caption_settings.highlightColor,
        backgroundColor: project.caption_settings.backgroundColor,
        backgroundOpacity: project.caption_settings.backgroundOpacity,
        animation: project.caption_settings.animation as
          | 'none'
          | 'fade'
          | 'slide-up'
          | 'bounce'
          | 'typewriter'
          | 'color-up'
          | 'bounce-out',
        textAlign: project.caption_settings.textAlign as
          | 'left'
          | 'center'
          | 'right',
        textShadow: project.caption_settings.textShadow,
        borderRadius: project.caption_settings.borderRadius,
        padding: project.caption_settings.padding,
        maxWidth: project.caption_settings.maxWidth,
      }
    : {
        id: 'default-caption-style',
        name: 'Default Caption Style',
        isCustom: false,
        createdAt: new Date().toISOString(),
        fontFamily: 'Inter',
        fontSize: 32,
        fontWeight: 'bold' as const,
        fontStyle: 'normal' as const,
        textColor: '#ffffff',
        highlightColor: '#3b82f6',
        backgroundColor: '#000000',
        backgroundOpacity: 60,
        animation: 'fade' as const,
        textAlign: 'center' as const,
        textShadow: true,
        borderRadius: 8,
        padding: 16,
        maxWidth: 90,
      }

  // Transform scenes from database format to expected rendering format
  const transformedScenes = (project.scenes || []).map(
    (scene: DatabaseScene) => {
      // Transform captions from database format to expected format
      const transformedCaptions = (scene.captions || []).map(caption => ({
        start: caption.start,
        end: caption.end,
        words: (caption.wordBoundries || []).map(word => ({
          start: word.start,
          end: word.end,
          word: word.word,
        })),
      }))

      // Transform media from database format to expected format
      const transformedMedia = scene.media
        ? {
            id: `media-${scene.id}`,
            type: scene.media.type?.toLowerCase() as 'image' | 'video',
            url: scene.media.url,
            position: scene.media.position || { x: 0, y: 0 },
            size: scene.media.size || { width: 1920, height: 1080 },
            startTime: 0,
            endTime: scene.duration,
            thumbnail: scene.media.thumbnail,
            duration: scene.duration,
            fit: ['blur', 'crop', 'contain'].includes(scene.media.fit || '')
              ? (scene.media.fit as 'blur' | 'crop' | 'contain')
              : 'blur',
            transition: (scene.media.transition?.type?.toLowerCase() ||
              'fade') as
              | 'none'
              | 'fade'
              | 'fadeblack'
              | 'fadewhite'
              | 'distance'
              | 'wipeleft'
              | 'wiperight'
              | 'wipeup'
              | 'wipedown'
              | 'slideleft'
              | 'slideright'
              | 'slideup'
              | 'slidedown',
            kenBurns: [
              'none',
              'zoom-in',
              'zoom-out',
              'pan-left',
              'pan-right',
              'pan-up',
              'pan-down',
            ].includes(scene.media.kenBurns || '')
              ? (scene.media.kenBurns as
                  | 'none'
                  | 'zoom-in'
                  | 'zoom-out'
                  | 'pan-left'
                  | 'pan-right'
                  | 'pan-up'
                  | 'pan-down')
              : 'zoom-in',
            effectDuration: scene.media.transition?.duration || 2,
          }
        : null

      // Create voiceover object from voiceSettings
      const voiceover = scene.voiceSettings?.voiceUrl
        ? {
            audioUrl: scene.voiceSettings.voiceUrl,
            audioDuration: scene.duration,
            volume: (scene.voiceSettings.voiceVol || 100) * 100, // Convert to expected scale
            speed: scene.voiceSettings.voiceSpeed || 1,
          }
        : undefined

      return {
        id: scene.id,
        name: scene.title || `Scene ${scene.id}`,
        duration: scene.duration,
        startOffset: scene.startOffset || 0,
        originalDuration: scene.duration, // Use duration as originalDuration
        text: scene.text,
        voiceSettings: {
          voiceId: scene.voiceSettings?.voiceId || '',
          voiceUrl: scene.voiceSettings?.voiceUrl || '',
          voiceVol: scene.voiceSettings?.voiceVol || 100,
          voiceName: scene.voiceSettings?.voiceName || '',
          voiceSpeed: scene.voiceSettings?.voiceSpeed || 1,
        },
        voiceover,
        captions: transformedCaptions,
        texts: [], // Empty array as expected
        media: transformedMedia,
      }
    }
  )

  // Construct the input props object matching the expected format
  return {
    scenes: transformedScenes,
    subtitlePosition,
    orientation: project.orientation,
    compositionWidth,
    compositionHeight,
    selectedMusic,
    musicVolume: project.music?.volume || 30,
    musicEnabled: project.music?.enabled || false,
    captionStyle,
    exportName,
    exportResolution,
    durationInFrames,
    speech: project.speech,
    duration,
  }
}
