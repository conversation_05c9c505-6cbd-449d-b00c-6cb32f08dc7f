import { nanoid } from 'nanoid'

const freeEmailProviders = [
  'gmail.com',
  'yahoo.com',
  'outlook.com',
  'hotmail.com',
  'aol.com',
  'icloud.com',
  'protonmail.com',
  'zoho.com',
  'yandex.com',
  'mail.com',
  'gmx.com',
  'live.com',
  'msn.com',
]

export function isFreeEmail(email: string): boolean {
  const domain = email.split('@')[1]?.toLowerCase()
  if (!domain) return false

  return freeEmailProviders.some(p => domain === p || domain.endsWith('.' + p))
}

export function getEmailDomain(email: string): string | null {
  const domain = email.split('@')[1]?.toLowerCase()
  return domain || null
}

export function getCompanyLogoUrl(domain: string): string {
  return `https://www.google.com/s2/favicons?sz=64&domain=${domain}`
}

export function getFallbackLogoUrl(domain: string): string {
  return `https://icons.duckduckgo.com/ip3/${domain}.ico`
}

export function getPersonalAvatarUrl(userName: string): string {
  const firstName = userName?.split(' ')[0] || 'User'
  const lastName = userName?.split(' ')[1] || ''
  const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()

  return `https://avatar.oxro.io/avatar.svg?name=${encodeURIComponent(initials)}&background=667eea&color=ffffff`
}

export function getDisplayName(userName: string, email: string): string {
  const firstName = userName?.split(' ')[0] || 'User'
  const isFree = isFreeEmail(email)

  if (isFree) {
    return `${firstName}'s Workspace`
  } else {
    const domain = getEmailDomain(email)
    const companyName = domain?.split('.')[0] || 'Company'
    // Capitalize first letter and add apostrophe 's
    const capitalizedCompanyName =
      companyName.charAt(0).toUpperCase() + companyName.slice(1)
    return `${capitalizedCompanyName}'s Workspace`
  }
}

export function getOrganizationLogo(userName: string, email: string): string {
  const isFree = isFreeEmail(email)

  if (isFree) {
    return getPersonalAvatarUrl(userName)
  } else {
    const domain = getEmailDomain(email)
    if (domain) {
      return getCompanyLogoUrl(domain)
    }
  }

  return getPersonalAvatarUrl(userName) // fallback
}

export function generateOrganizationSlug(
  userName: string,
  email: string
): string {
  const isFree = isFreeEmail(email)
  const shortId = nanoid(8) // Generate 8 character ID

  if (isFree) {
    const firstName = userName?.split(' ')[0] || 'user'
    return `${firstName.toLowerCase()}-${shortId}`
  } else {
    const domain = getEmailDomain(email)
    const companyName = domain?.split('.')[0] || 'company'
    return `${companyName.toLowerCase()}-${shortId}`
  }
}

export function getAvatarImage(userName: string, email: string): string | null {
  const isFree = isFreeEmail(email)

  if (isFree) {
    // For free emails, return null to use initials
    return null
  } else {
    const domain = getEmailDomain(email)
    if (domain) {
      return getCompanyLogoUrl(domain)
    }
  }

  return null
}
