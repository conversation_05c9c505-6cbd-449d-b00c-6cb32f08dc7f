import { google } from 'googleapis'
import { OAuth2Client } from 'google-auth-library'

// YouTube API scopes for video upload and channel management
export const YOUTUBE_SCOPES = [
  'openid',
  'https://www.googleapis.com/auth/userinfo.profile',
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/youtube',
  'https://www.googleapis.com/auth/youtube.upload',
  'https://www.googleapis.com/auth/youtube.readonly',
]

/**
 * Creates a Google OAuth2 client with the configured credentials
 */
export function createOAuth2Client(): OAuth2Client {
  const clientId = process.env.GOOGLE_OAUTH_CLIENT_ID
  const clientSecret = process.env.GOOGLE_OAUTH_CLIENT_SECRET
  const redirectUri = process.env.GOOGLE_OAUTH_REDIRECT_URI

  if (!clientId || !clientSecret || !redirectUri) {
    throw new Error('Missing Google OAuth credentials in environment variables')
  }

  return new google.auth.OAuth2(clientId, clientSecret, redirectUri)
}

/**
 * Generates the authorization URL for the OAuth flow
 * @param state - CSRF protection state parameter
 * @returns Authorization URL
 */
export function generateAuthUrl(state: string): string {
  const oauth2Client = createOAuth2Client()

  return oauth2Client.generateAuthUrl({
    access_type: 'offline', // Request refresh token
    scope: YOUTUBE_SCOPES,
    state,
    prompt: 'consent', // Force consent screen to ensure refresh token
    include_granted_scopes: true, // Incremental authorization
  })
}

/**
 * Exchanges authorization code for access and refresh tokens
 * @param code - Authorization code from OAuth callback
 * @returns Token information
 */
export async function exchangeCodeForTokens(code: string) {
  const oauth2Client = createOAuth2Client()

  try {
    const { tokens } = await oauth2Client.getToken(code)

    if (!tokens.access_token || !tokens.refresh_token) {
      throw new Error('Missing required tokens in OAuth response')
    }

    return {
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      expires_at: tokens.expiry_date ? new Date(tokens.expiry_date) : null,
      scope: tokens.scope,
    }
  } catch (error) {
    console.error('Error exchanging code for tokens:', error)
    throw new Error('Failed to exchange authorization code for tokens')
  }
}

/**
 * Custom error class for OAuth token refresh failures
 */
export class TokenRefreshError extends Error {
  public readonly isInvalidGrant: boolean
  public readonly shouldDeleteConnection: boolean

  constructor(message: string, originalError?: unknown) {
    super(message)
    this.name = 'TokenRefreshError'

    // Check if this is an invalid_grant error that requires connection cleanup
    const errorString = originalError?.toString?.() || ''
    const errorMessage =
      originalError instanceof Error ? originalError.message : ''

    this.isInvalidGrant =
      errorString.includes('invalid_grant') ||
      errorMessage.includes('invalid_grant') ||
      errorString.includes('Token has been expired or revoked') ||
      errorMessage.includes('Token has been expired or revoked')

    // Delete connection if it's an invalid_grant error or similar permanent failure
    this.shouldDeleteConnection = this.isInvalidGrant
  }
}

/**
 * Refreshes an expired access token using the refresh token
 * @param refreshToken - The refresh token
 * @returns New token information
 * @throws TokenRefreshError with proper error classification
 */
export async function refreshAccessToken(refreshToken: string) {
  const oauth2Client = createOAuth2Client()
  oauth2Client.setCredentials({ refresh_token: refreshToken })

  try {
    const { credentials } = await oauth2Client.refreshAccessToken()

    if (!credentials.access_token) {
      throw new TokenRefreshError('No access token received from refresh')
    }

    return {
      access_token: credentials.access_token,
      expires_at: credentials.expiry_date
        ? new Date(credentials.expiry_date)
        : null,
    }
  } catch (error) {
    console.error('Error refreshing access token:', error)

    // Throw our custom error with proper classification
    throw new TokenRefreshError('Failed to refresh access token', error)
  }
}

/**
 * Gets YouTube channel information for the authenticated user
 * @param accessToken - Valid access token
 * @returns Channel information
 */
export async function getChannelInfo(accessToken: string) {
  const oauth2Client = createOAuth2Client()
  oauth2Client.setCredentials({ access_token: accessToken })

  const youtube = google.youtube({ version: 'v3', auth: oauth2Client })

  try {
    const response = await youtube.channels.list({
      part: ['snippet', 'statistics', 'status'],
      mine: true,
    })

    const channel = response.data.items?.[0]
    if (!channel) {
      throw new Error('No channel found for the authenticated user')
    }

    return {
      id: channel.id!,
      title: channel.snippet?.title || '',
      description: channel.snippet?.description || '',
      thumbnailUrl: channel.snippet?.thumbnails?.default?.url || null,
      subscriberCount: channel.statistics?.subscriberCount || '0',
      videoCount: channel.statistics?.videoCount || '0',
      isVerified: channel.status?.isLinked || false,
      canUploadCustomThumbnails: channel.status?.isLinked || false,
      longUploadsStatus: channel.status?.longUploadsStatus || 'disallowed',
      customUrl: channel.snippet?.customUrl || null,
    }
  } catch (error) {
    console.error('Error fetching channel info:', error)
    throw new Error('Failed to fetch YouTube channel information')
  }
}

/**
 * Checks if a channel can upload custom thumbnails
 * @param accessToken - Valid access token
 * @returns Whether the channel can upload custom thumbnails
 */
export async function canUploadCustomThumbnails(
  accessToken: string
): Promise<boolean> {
  const oauth2Client = createOAuth2Client()
  oauth2Client.setCredentials({ access_token: accessToken })

  const youtube = google.youtube({ version: 'v3', auth: oauth2Client })

  try {
    const response = await youtube.channels.list({
      part: ['status', 'snippet'],
      mine: true,
    })

    const channel = response.data.items?.[0]
    if (!channel) {
      return false
    }

    // For custom thumbnail uploads, YouTube requires:
    // 1. Channel to be verified (isLinked = true)
    // 2. No recent Community Guidelines strikes
    // 3. The channel must have the feature enabled

    // Check if channel is verified - this is the primary requirement
    const isVerified = channel.status?.isLinked === true

    // If not verified, definitely can't upload custom thumbnails
    if (!isVerified) {
      console.log('Channel is not verified - custom thumbnails not allowed')
      return false
    }

    // If verified, we'll optimistically return true and let the actual upload
    // attempt reveal any specific restrictions (like Community Guidelines strikes).
    // This is because the YouTube API doesn't provide a direct way to check
    // for Community Guidelines strikes or the exact custom thumbnail feature status.
    console.log('Channel is verified - custom thumbnails should be allowed')
    return true
  } catch (error) {
    console.error('Error checking thumbnail upload permissions:', error)
    return false
  }
}

/**
 * Revokes the OAuth tokens for a user
 * @param accessToken - Access token to revoke
 */
export async function revokeTokens(accessToken: string): Promise<void> {
  const oauth2Client = createOAuth2Client()
  oauth2Client.setCredentials({ access_token: accessToken })

  try {
    await oauth2Client.revokeCredentials()
  } catch (error) {
    console.error('Error revoking tokens:', error)
    // Don't throw here - we'll still remove from database even if revocation fails
  }
}

/**
 * Validates if an access token is still valid
 * @param accessToken - Access token to validate
 * @returns Whether the token is valid
 */
export async function validateAccessToken(
  accessToken: string
): Promise<boolean> {
  const oauth2Client = createOAuth2Client()
  oauth2Client.setCredentials({ access_token: accessToken })

  try {
    const tokenInfo = await oauth2Client.getTokenInfo(accessToken)
    return tokenInfo.expiry_date ? tokenInfo.expiry_date > Date.now() : false
  } catch {
    return false
  }
}

/**
 * Deletes an invalid YouTube connection from the database
 * @param connectionId - ID of the connection to delete
 */
async function deleteInvalidConnection(connectionId: string): Promise<void> {
  try {
    const { db } = await import('@/lib/db')
    const { youtubeConnections } = await import('@/db/schema')
    const { eq } = await import('drizzle-orm')

    await db
      .delete(youtubeConnections)
      .where(eq(youtubeConnections.id, connectionId))

    console.log(`Deleted invalid YouTube connection: ${connectionId}`)
  } catch (error) {
    console.error(`Failed to delete invalid connection ${connectionId}:`, error)
  }
}

/**
 * Ensures a valid access token, refreshing if necessary
 * @param connection - YouTube connection object from database
 * @returns Valid access token
 * @throws Error with specific messages for different failure types
 */
export async function ensureValidAccessToken(connection: {
  id: string
  accessToken: string
  refreshToken: string
  expiresAt: Date
}): Promise<string> {
  const now = new Date()
  const { decrypt } = await import('@/lib/encryption')

  // Always validate the token with Google, even if it hasn't expired
  // This catches cases where users revoked access externally
  let accessToken: string

  if (connection.expiresAt > now) {
    // Token hasn't expired, but we still need to validate it
    accessToken = decrypt(connection.accessToken)

    // Validate the token with Google to ensure it's still valid
    const isValid = await validateAccessToken(accessToken)
    if (isValid) {
      return accessToken
    }

    // Token is invalid (likely revoked), attempt to refresh
    console.log(
      `Access token for connection ${connection.id} is invalid despite not being expired. Attempting refresh...`
    )
  }

  // Token is expired or invalid, attempt to refresh
  try {
    const { decryptTokens, encryptTokens } = await import('@/lib/encryption')
    const { db } = await import('@/lib/db')
    const { youtubeConnections } = await import('@/db/schema')
    const { eq } = await import('drizzle-orm')

    // Decrypt the refresh token
    const tokens = decryptTokens({
      encryptedAccessToken: connection.accessToken,
      encryptedRefreshToken: connection.refreshToken,
    })

    // Refresh the access token
    const refreshedTokens = await refreshAccessToken(tokens.refresh_token)

    // Encrypt the new access token
    const encryptedTokens = encryptTokens({
      access_token: refreshedTokens.access_token,
      refresh_token: tokens.refresh_token, // Keep the same refresh token
    })

    // Update the database with the new token
    await db
      .update(youtubeConnections)
      .set({
        accessToken: encryptedTokens.encryptedAccessToken,
        expiresAt:
          refreshedTokens.expires_at || new Date(Date.now() + 3600 * 1000),
        updatedAt: new Date(),
      })
      .where(eq(youtubeConnections.id, connection.id))

    console.log(
      `Successfully refreshed YouTube token for connection: ${connection.id}`
    )
    return refreshedTokens.access_token
  } catch (error) {
    console.error('Failed to refresh YouTube token:', error)

    // Handle invalid_grant errors by cleaning up the connection
    if (error instanceof TokenRefreshError && error.shouldDeleteConnection) {
      console.log(`Cleaning up invalid YouTube connection: ${connection.id}`)

      // Delete the invalid connection from database
      await deleteInvalidConnection(connection.id)

      // Throw a specific error that indicates the connection was revoked
      throw new Error('YOUTUBE_CONNECTION_REVOKED')
    }

    // For other errors, throw a generic refresh error
    throw new Error(
      'YouTube connection has expired and could not be refreshed. Please reconnect.'
    )
  }
}
