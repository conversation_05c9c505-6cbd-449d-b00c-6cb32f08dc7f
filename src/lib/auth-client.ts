import { createAuthClient } from 'better-auth/react'
import { organizationClient, adminClient } from 'better-auth/client/plugins'
import { stripeClient } from '@better-auth/stripe/client'

export const authClient = createAuthClient({
  plugins: [
    organizationClient(),
    adminClient(),
    stripeClient({
      subscription: true,
    }),
  ],
  baseURL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  redirectURL: '/',
})
