import { db } from '@/lib/db'
import { api<PERSON><PERSON><PERSON> } from '@/db/schema'
import { eq } from 'drizzle-orm'

export async function updateApiKeyLastUsed(key: string) {
  try {
    await db
      .update(apiKeys)
      .set({
        lastUsed: new Date(),
      })
      .where(eq(apiKeys.key, key))
  } catch (error) {
    console.error('[UPDATE_API_KEY_LAST_USED]', error)
  }
}

export async function validateApiKey(key: string) {
  try {
    const apiKey = await db.query.apiKeys.findFirst({
      where: eq(apiKeys.key, key),
    })

    if (!apiKey || !apiKey.isActive) {
      return null
    }

    // Update last used timestamp
    await updateApiKeyLastUsed(key)

    return apiKey
  } catch (error) {
    console.error('[VALIDATE_API_KEY]', error)
    return null
  }
}
