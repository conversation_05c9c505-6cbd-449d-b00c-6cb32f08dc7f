import { NextResponse } from 'next/server'

/**
 * Cache duration constants in seconds
 */
export const CACHE_DURATIONS = {
  // Static data that rarely changes
  FONTS: 86400, // 24 hours
  VOICES: 86400, // 24 hours

  // Search results that can be cached for moderate time
  PODCAST_SEARCH: 1800, // 30 minutes
  PODCAST_RSS: 3600, // 1 hour
  MUSIC_SEARCH: 86400, // 24 hours

  // Media search results that change more frequently
  PEXELS_SEARCH: 900, // 15 minutes
  PIXABAY_SEARCH: 900, // 15 minutes
  GIPHY_SEARCH: 900, // 15 minutes
  TENOR_SEARCH: 900, // 15 minutes
  GOOGLE_SEARCH: 1800, // 30 minutes (Google results are more stable)
  GETTY_SEARCH: 1800, // 30 minutes

  // Dynamic data that should be cached briefly
  PROJECTS: 300, // 5 minutes
  MEDIA_ASSETS: 600, // 10 minutes

  // Real-time data that shouldn't be cached
  VIDEO_STATUS: 0, // No cache
  SPEECH_GENERATION: 0, // No cache
} as const

/**
 * Add cache headers to a NextResponse
 */
export function addCacheHeaders(
  response: NextResponse,
  maxAge: number,
  staleWhileRevalidate?: number
): NextResponse {
  if (maxAge === 0) {
    // No cache for real-time data
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    return response
  }

  const swr = staleWhileRevalidate || maxAge * 2

  // Browser and CDN cache headers
  response.headers.set(
    'Cache-Control',
    `public, s-maxage=${maxAge}, stale-while-revalidate=${swr}`
  )

  // CDN-specific headers for better caching
  response.headers.set('CDN-Cache-Control', `public, s-maxage=${maxAge}`)
  response.headers.set('Vercel-CDN-Cache-Control', `public, s-maxage=${maxAge}`)

  // Add ETag for better cache validation
  const etag = `"${Date.now()}-${Math.random().toString(36).substring(2, 11)}"`
  response.headers.set('ETag', etag)

  return response
}

/**
 * Create a cached JSON response
 */
export function createCachedResponse(
  data: unknown,
  cacheMaxAge: number,
  staleWhileRevalidate?: number
): NextResponse {
  const response = NextResponse.json(data)
  return addCacheHeaders(response, cacheMaxAge, staleWhileRevalidate)
}

/**
 * Add compression headers to response
 */
export function addCompressionHeaders(response: NextResponse): NextResponse {
  // Enable compression for JSON responses
  response.headers.set('Content-Encoding', 'gzip')
  response.headers.set('Vary', 'Accept-Encoding')

  return response
}

/**
 * Cache configuration for different API endpoints
 */
export const API_CACHE_CONFIG = {
  '/api/fonts': CACHE_DURATIONS.FONTS,
  '/api/elevenlabs/voices': CACHE_DURATIONS.VOICES,
  '/api/podcast/search': CACHE_DURATIONS.PODCAST_SEARCH,
  '/api/podcast/rss': CACHE_DURATIONS.PODCAST_RSS,
  '/api/music': CACHE_DURATIONS.MUSIC_SEARCH,
  '/api/media/pexels/images': CACHE_DURATIONS.PEXELS_SEARCH,
  '/api/media/pexels/videos': CACHE_DURATIONS.PEXELS_SEARCH,
  '/api/media/pixabay/images': CACHE_DURATIONS.PIXABAY_SEARCH,
  '/api/media/pixabay/videos': CACHE_DURATIONS.PIXABAY_SEARCH,
  '/api/media/giphy/gifs': CACHE_DURATIONS.GIPHY_SEARCH,
  '/api/media/tenor/gifs': CACHE_DURATIONS.TENOR_SEARCH,
  '/api/media/google-images': CACHE_DURATIONS.GOOGLE_SEARCH,
  '/api/media/getty-images': CACHE_DURATIONS.GETTY_SEARCH,
  '/api/projects': CACHE_DURATIONS.PROJECTS,
  '/api/media/upload': CACHE_DURATIONS.MEDIA_ASSETS,
  '/api/video-data-status': CACHE_DURATIONS.VIDEO_STATUS,
  '/api/elevenlabs/generate-speech': CACHE_DURATIONS.SPEECH_GENERATION,
} as const

/**
 * Get cache duration for an API endpoint
 */
export function getCacheDuration(pathname: string): number {
  // Find matching cache config
  for (const [path, duration] of Object.entries(API_CACHE_CONFIG)) {
    if (pathname.startsWith(path)) {
      return duration
    }
  }

  // Default cache duration for unspecified endpoints
  return CACHE_DURATIONS.PROJECTS // 5 minutes default
}

/**
 * Utility to create error responses with appropriate cache headers
 */
export function createErrorResponse(
  error: string,
  status: number,
  cacheMaxAge: number = 0
): NextResponse {
  const response = NextResponse.json({ error }, { status })

  if (cacheMaxAge > 0) {
    return addCacheHeaders(response, cacheMaxAge)
  }

  // Don't cache error responses by default
  response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
  return response
}
