export function createSubscriptionWelcomeEmailTemplate({
  userName,
  planName,
  planFeatures = [],
  planLimits,
}: {
  userName?: string
  planName: string
  planFeatures?: string[]
  planLimits: {
    projects: number
    videoExports: number
    aiImages: number
    storage: number
    teamMembers: number
  }
}) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to ${planName} Plan - Adori AI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background:  #CC3333; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #000; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .feature { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #CC3333; }
        .feature h3 { margin: 0 0 10px 0; color: #CC3333; }
        .plan-highlight { background: linear-gradient(135deg, #CC3333 0%, #FF1493 100%); color: white; padding: 25px; margin: 20px 0; border-radius: 8px; text-align: center; }
        .plan-highlight h2 { margin: 0 0 15px 0; font-size: 24px; }
        .limits-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .limit-item { background: white; padding: 15px; border-radius: 8px; text-align: center; border: 2px solid #f0f0f0; }
        .limit-number { font-size: 24px; font-weight: bold; color: #8A2BE2; }
        .limit-label { font-size: 14px; color: #666; margin-top: 5px; }
        .features-list { list-style: none; padding: 0; }
        .features-list li { background: white; padding: 10px 15px; margin: 8px 0; border-radius: 6px; border-left: 3px solid #CC3333; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Welcome to ${planName}!</h1>
          <p>Your subscription is now active</p>
        </div>
        <div class="content">
          <h2>Hi${userName ? ` ${userName}` : ''},</h2>
          <p>Congratulations! Your ${planName} subscription is now active. You've just unlocked a whole new level of video creation capabilities with Adori AI.</p>

          <div class="plan-highlight">
            <h2>✨ ${planName.toUpperCase()} PLAN ACTIVATED</h2>
            <p>Your usage has been reset and you're ready to create amazing videos!</p>
          </div>

          <div class="feature">
            <h3>📊 Your New Limits</h3>
            <div class="limits-grid">
              <div class="limit-item">
                <div class="limit-number">${planLimits.projects}</div>
                <div class="limit-label">Projects</div>
              </div>
              <div class="limit-item">
                <div class="limit-number">${planLimits.videoExports}</div>
                <div class="limit-label">Video Exports</div>
              </div>
              <div class="limit-item">
                <div class="limit-number">${planLimits.aiImages}</div>
                <div class="limit-label">AI Images</div>
              </div>
              <div class="limit-item">
                <div class="limit-number">${planLimits.storage}MB</div>
                <div class="limit-label">Storage</div>
              </div>
            </div>
          </div>

          ${
            planFeatures.length > 0
              ? `
          <div class="feature">
            <h3>🚀 New Features Unlocked</h3>
            <ul class="features-list">
              ${planFeatures.map(feature => `<li>✅ ${feature}</li>`).join('')}
            </ul>
          </div>
          `
              : ''
          }

          <div class="feature">
            <h3>🎬 Ready to Create?</h3>
            <p>Your subscription is active and your usage has been reset. You can now:</p>
            <ul>
              <li>Create up to ${planLimits.projects} projects</li>
              <li>Export ${planLimits.videoExports} videos in high quality</li>
              <li>Generate ${planLimits.aiImages} AI images</li>
              <li>Use ${planLimits.storage}MB of storage</li>
            </ul>
          </div>

          <div style="text-align: center;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/create-video" class="button">Start Creating Videos</a>
          </div>

          <div class="footer">
            <p>Thank you for choosing Adori AI!</p>
            <p>If you have any questions, feel free to reach out to our support team.</p>
            <p>© 2025 Adori AI. All rights reserved.</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
Welcome to ${planName} Plan - Adori AI

Hi${userName ? ` ${userName}` : ''},

Congratulations! Your ${planName} subscription is now active. You've just unlocked a whole new level of video creation capabilities with Adori AI.

✨ ${planName.toUpperCase()} PLAN ACTIVATED
Your usage has been reset and you're ready to create amazing videos!

📊 Your New Limits:
• Projects: ${planLimits.projects}
• Video Exports: ${planLimits.videoExports}
• AI Images: ${planLimits.aiImages}
• Storage: ${planLimits.storage}MB
• Team Members: ${planLimits.teamMembers}

${
  planFeatures.length > 0
    ? `🚀 New Features Unlocked:
${planFeatures.map(feature => `• ${feature}`).join('\n')}`
    : ''
}

🎬 Ready to Create?
Your subscription is active and your usage has been reset. You can now:
• Create up to ${planLimits.projects} projects
• Export ${planLimits.videoExports} videos in high quality
• Generate ${planLimits.aiImages} AI images
• Use ${planLimits.storage}MB of storage

Start creating videos: ${process.env.NEXT_PUBLIC_APP_URL}/create-video

Thank you for choosing Adori AI!
If you have any questions, feel free to reach out to our support team.

© 2025 Adori AI. All rights reserved.
  `

  return { html, text }
}
