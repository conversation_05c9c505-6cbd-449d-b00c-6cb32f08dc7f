import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses'

// Initialize SES client
const sesClient = new SESClient({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.SES_ACCESS_ID!,
    secretAccessKey: process.env.SES_ACCESS_KEY!,
  },
})

interface SendEmailParams {
  to: string
  subject: string
  text: string
  html?: string
  from?: string
}

export async function sendEmail({
  to,
  subject,
  text,
  html,
  from = process.env.EMAIL_SENDER_ADDRESS || '<EMAIL>',
}: SendEmailParams) {
  // Format the from address with display name like "Adori AI <<EMAIL>>"
  const formattedFrom = from.includes('<') ? from : `Adori AI <${from}>`
  try {
    const command = new SendEmailCommand({
      Source: formattedFrom,
      Destination: {
        ToAddresses: [to],
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8',
        },
        Body: {
          Text: {
            Data: text,
            Charset: 'UTF-8',
          },
          ...(html && {
            Html: {
              Data: html,
              Charset: 'UTF-8',
            },
          }),
        },
      },
    })

    const result = await sesClient.send(command)
    console.log('Email sent successfully:', result.MessageId)
    return result
  } catch (error) {
    console.error('Error sending email:', error)
    throw error
  }
}

// Re-export all template functions
export * from './templates'
