export interface UploadResult {
  success: boolean
  url: string
  path: string
  filename: string
  size: number
  type: string
  bucket: string
}

export interface UploadError {
  error: string
}

export async function uploadFile(file: File): Promise<UploadResult> {
  // Step 1: Get signed upload URL
  const initiateResponse = await fetch('/api/upload', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    }),
  })

  if (!initiateResponse.ok) {
    const errorData: UploadError = await initiateResponse.json()
    throw new Error(errorData.error || 'Upload initiation failed')
  }

  const initData = await initiateResponse.json()

  // Step 2: Upload directly to Supabase using signed URL
  const uploadResponse = await fetch(initData.uploadUrl, {
    method: 'PUT',
    headers: {
      'x-upsert': 'false',
      Authorization: `Bearer ${initData.uploadToken}`,
      'Content-Type': file.type,
    },
    body: file,
  })

  if (!uploadResponse.ok) {
    throw new Error('Failed to upload file to storage')
  }

  // Step 3: Complete upload and get public URL
  const completeResponse = await fetch('/api/upload', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      filePath: initData.filePath,
      bucket: initData.bucket,
      fileSize: file.size,
    }),
  })

  if (!completeResponse.ok) {
    const errorData: UploadError = await completeResponse.json()
    throw new Error(errorData.error || 'Upload completion failed')
  }

  return await completeResponse.json()
}
