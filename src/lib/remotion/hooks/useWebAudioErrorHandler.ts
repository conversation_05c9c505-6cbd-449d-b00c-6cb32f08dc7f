/**
 * useWebAudioErrorHandler Hook
 *
 * Provides error handling and recovery mechanisms for WebAudio API issues
 * that can occur during Remotion Player operation, particularly the
 * "AudioContext encountered an error from the WebAudio renderer" error.
 *
 * This hook implements:
 * - AudioContext health monitoring
 * - Automatic recovery attempts
 * - Graceful degradation strategies
 * - Error reporting and logging
 */

import { useEffect, useCallback, useRef } from 'react'

interface WebAudioErrorHandlerOptions {
  onError?: (error: Error, context: string) => void
  enableRecovery?: boolean
  maxRetryAttempts?: number
}

interface WebAudioErrorHandler {
  handleAudioError: (error: Error, context: string) => void
  isAudioContextHealthy: () => boolean
  reinitializeAudioContext: () => Promise<boolean>
}

export const useWebAudioErrorHandler = (
  options: WebAudioErrorHandlerOptions = {}
): WebAudioErrorHandler => {
  const {
    onError,
    enableRecovery = true,
    maxRetryAttempts = 3,
  } = options

  const retryCountRef = useRef(0)
  const audioContextRef = useRef<AudioContext | null>(null)

  // Check if AudioContext is available and healthy
  const isAudioContextHealthy = useCallback((): boolean => {
    try {
      if (!window.AudioContext && !window.webkitAudioContext) {
        console.warn('WebAudio API not supported in this browser')
        return false
      }

      if (audioContextRef.current) {
        return audioContextRef.current.state !== 'closed'
      }

      return true
    } catch (error) {
      console.warn('AudioContext health check failed:', error)
      return false
    }
  }, [])

  // Attempt to reinitialize AudioContext
  const reinitializeAudioContext = useCallback(async (): Promise<boolean> => {
    try {
      // Close existing context if it exists
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        await audioContextRef.current.close()
      }

      // Create new AudioContext
      const AudioContextClass = window.AudioContext || window.webkitAudioContext
      if (!AudioContextClass) {
        throw new Error('AudioContext not supported')
      }

      const newContext = new AudioContextClass()
      audioContextRef.current = newContext

      // Resume context if it's suspended (common on mobile)
      if (newContext.state === 'suspended') {
        await newContext.resume()
      }

      console.log('✅ AudioContext reinitialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to reinitialize AudioContext:', error)
      return false
    }
  }, [])

  // Main error handler
  const handleAudioError = useCallback(
    async (error: Error, context: string) => {
      console.error(`🔊 WebAudio error in ${context}:`, error)

      // Report error to callback
      onError?.(error, context)

      // Check if this is a WebAudio renderer error
      const isWebAudioRendererError = 
        error.message.includes('AudioContext encountered an error') ||
        error.message.includes('WebAudio renderer') ||
        error.message.includes('audio context')

      if (isWebAudioRendererError && enableRecovery && retryCountRef.current < maxRetryAttempts) {
        retryCountRef.current += 1
        console.log(`🔄 Attempting WebAudio recovery (attempt ${retryCountRef.current}/${maxRetryAttempts})`)

        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Try to reinitialize AudioContext
        const recoverySuccess = await reinitializeAudioContext()
        
        if (recoverySuccess) {
          console.log('✅ WebAudio recovery successful')
          // Reset retry count on successful recovery
          retryCountRef.current = 0
        } else {
          console.warn('⚠️ WebAudio recovery failed')
        }
      } else if (retryCountRef.current >= maxRetryAttempts) {
        console.warn('⚠️ Max WebAudio recovery attempts reached, falling back to graceful degradation')
      }
    },
    [onError, enableRecovery, maxRetryAttempts, reinitializeAudioContext]
  )

  // Initialize AudioContext on mount
  useEffect(() => {
    if (isAudioContextHealthy()) {
      reinitializeAudioContext()
    }
  }, [isAudioContextHealthy, reinitializeAudioContext])

  // Global error handler for unhandled WebAudio errors
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      if (event.error && event.error.message) {
        const isWebAudioError = 
          event.error.message.includes('AudioContext') ||
          event.error.message.includes('WebAudio') ||
          event.error.message.includes('audio context')

        if (isWebAudioError) {
          handleAudioError(event.error, 'global')
        }
      }
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason && event.reason.message) {
        const isWebAudioError = 
          event.reason.message.includes('AudioContext') ||
          event.reason.message.includes('WebAudio') ||
          event.reason.message.includes('audio context')

        if (isWebAudioError) {
          handleAudioError(event.reason, 'promise rejection')
        }
      }
    }

    window.addEventListener('error', handleGlobalError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleGlobalError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [handleAudioError])

  return {
    handleAudioError,
    isAudioContextHealthy,
    reinitializeAudioContext,
  }
}

// Type augmentation for webkit AudioContext
declare global {
  interface Window {
    webkitAudioContext?: typeof AudioContext
  }
}
