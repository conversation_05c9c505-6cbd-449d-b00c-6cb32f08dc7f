/**
 * Subtitle System Hook
 *
 * Consolidated subtitle processing hook that handles both scene-based
 * and speech-based subtitle workflows while maintaining exact compatibility
 * with existing implementations.
 */

import { useMemo } from 'react'
import { useCurrentFrame } from 'remotion'
import type { Scene, CaptionStyle, SentenceAlignment } from '../types'
import {
  buildGlobalAlignment,
  buildGlobalAlignmentFromSpeech,
  buildWordAlignment,
  chunkWords,
} from '../utils'

interface SubtitleSystemProps {
  scenes: Scene[]
  speech?: {
    enabled: boolean
    src: string
    name: string
    volume: number
    transcript: {
      captions: Array<{
        start: number
        end: number
        sentence: string
        wordBoundries: Array<{ start: number; end: number; word: string }>
      }>
      status: string
    }
  } | null
  captionStyle?: CaptionStyle
}

interface SubtitleState {
  currentSentence: SentenceAlignment | undefined
  highlightIdx: number
  sentences: SentenceAlignment[]
}

/**
 * Hook for processing subtitle data and getting current subtitle state
 */
export const useSubtitleSystem = ({
  scenes,
  speech,
}: Omit<SubtitleSystemProps, 'captionStyle'>): SubtitleState => {
  const globalFrame = useCurrentFrame()
  const fps = 30
  const currentTime = globalFrame / fps

  // Memoize speech captions for performance
  const speechCaptions = useMemo(
    () => speech?.transcript?.captions,
    [speech?.transcript?.captions]
  )

  // Build subtitle data based on workflow type with optimized memoization
  const sentences = useMemo(() => {
    let globalAlignment, words

    if (speechCaptions && speechCaptions.length > 0) {
      // For speech-based workflows, use speech captions
      globalAlignment = buildGlobalAlignmentFromSpeech(speechCaptions)
      words = buildWordAlignment(globalAlignment)
      return chunkWords(words, 4)
    } else {
      // For text-based workflows, use scene captions
      globalAlignment = buildGlobalAlignment(scenes)
      words = buildWordAlignment(globalAlignment)
      return chunkWords(words, 4)
    }
  }, [speechCaptions, scenes])

  // Find current sentence and highlighted word
  const currentSentence = useMemo(() => {
    return sentences.find(s => currentTime >= s.start && currentTime <= s.end)
  }, [sentences, currentTime])

  const highlightIdx = useMemo(() => {
    if (!currentSentence) return -1
    return currentSentence.words.findIndex(
      w => currentTime >= w.start && currentTime <= w.end
    )
  }, [currentSentence, currentTime])

  return {
    currentSentence,
    highlightIdx,
    sentences,
  }
}

/**
 * Hook for calculating subtitle positioning and styling
 */
export const useSubtitleStyle = (
  subtitlePosition?: { x: number; y: number },
  captionStyle?: CaptionStyle,
  compositionWidth = 960,
  compositionHeight = 540
) => {
  return useMemo(() => {
    // Default subtitle position (bottom center)
    const forcedSubtitlePosition = subtitlePosition || { x: 240, y: 246 }

    // Calculate subtitle position using percentages for consistent positioning
    const previewWidth = 480
    const previewHeight = 270
    const subtitleX = (forcedSubtitlePosition.x / previewWidth) * 100
    const subtitleY = (forcedSubtitlePosition.y / previewHeight) * 100

    // Default caption style
    const styleProps = captionStyle || {
      fontFamily: 'Inter',
      fontSize: 48,
      fontWeight: 'bold' as const,
      fontStyle: 'normal' as const,
      textColor: '#ffffff',
      highlightColor: '#3b82f6',
      backgroundColor: '#000000',
      backgroundOpacity: 60,
      textAlign: 'center' as const,
      textShadow: true,
      borderRadius: 8,
      padding: 16,
      maxWidth: 90,
    }

    // Helper function to get scale factor based on composition dimensions
    const getScaleFactor = (
      compositionWidth: number,
      compositionHeight: number
    ): number => {
      // Determine resolution class based on the larger dimension
      const maxDimension = Math.max(compositionWidth, compositionHeight)

      if (maxDimension >= 1920) {
        return 0.85 // 1080p: 0.85x smaller than preview
      } else if (maxDimension >= 1280) {
        return 0.7 // 720p: 0.7x smaller than preview
      } else if (maxDimension >= 960) {
        return 1.0 // Preview baseline (960px)
      } else if (maxDimension >= 854) {
        return 0.45 // 480p: 0.45x smaller than preview
      }
      return 1.0 // Default to preview baseline
    }

    const scaleFactor = getScaleFactor(compositionWidth, compositionHeight)

    // Convert background color with opacity
    const getBackgroundColor = () => {
      if ((styleProps.backgroundOpacity || 0) === 0) {
        return 'transparent'
      }

      const bgColor = styleProps.backgroundColor || '#000000'
      const opacity = styleProps.backgroundOpacity || 60

      if (bgColor.startsWith('rgba')) {
        return bgColor
      }

      // Convert hex to rgba with opacity
      const hex = bgColor.replace('#', '')
      const r = parseInt(hex.substring(0, 2), 16)
      const g = parseInt(hex.substring(2, 4), 16)
      const b = parseInt(hex.substring(4, 6), 16)
      const alpha = opacity / 100
      return `rgba(${r}, ${g}, ${b}, ${alpha})`
    }

    return {
      subtitleX,
      subtitleY,
      styleProps,
      scaleFactor,
      getBackgroundColor,
    }
  }, [subtitlePosition, captionStyle, compositionWidth, compositionHeight])
}
