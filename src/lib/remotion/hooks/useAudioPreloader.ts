/**
 * useAudioPreloader Hook
 *
 * Preloads audio files from Supabase storage URLs in preview mode to prevent
 * silent gaps at the beginning of audio playback. This is the counterpart to
 * useImagePreloader but specifically for preview mode audio buffering.
 *
 * OPTIMIZATION: Only preloads the first scene's audio to prevent WebAudio renderer
 * overload. Subsequent scenes are loaded progressively via Remotion's premounting.
 *
 * Uses Remotion's useBufferState().delayPlayback() mechanism to pause the
 * player until the first scene's audio is preloaded and ready for smooth playback.
 */

import { useEffect } from 'react'
import { useBufferState } from 'remotion'
import { preloadAudio } from '@remotion/preload'
import type { Scene } from '../types'

export const useAudioPreloader = (
  scenes: Scene[],
  isRenderingContext: boolean
) => {
  const buffer = useBufferState()

  useEffect(() => {
    // Only run in PREVIEW mode (opposite of image preloader)
    // Audio preloading is not needed during video rendering
    if (isRenderingContext) return

    // Extract audio URL from ONLY the first scene to prevent WebAudio overload
    // Subsequent scenes will be loaded progressively via premounting
    const firstSceneAudioUrl =
      scenes.length > 0
        ? scenes[0].voiceover?.audioUrl || scenes[0].voiceSettings?.voiceUrl
        : null

    // Only preload network URLs, skip base64 and blob URLs
    const shouldPreloadFirstScene =
      firstSceneAudioUrl &&
      typeof firstSceneAudioUrl === 'string' &&
      !firstSceneAudioUrl.startsWith('data:') &&
      !firstSceneAudioUrl.startsWith('blob:') &&
      firstSceneAudioUrl.trim().length > 0

    const audioUrls = shouldPreloadFirstScene ? [firstSceneAudioUrl] : []

    // If no network audio URLs to preload, return early
    if (audioUrls.length === 0) return

    console.log(
      `🎵 Preloading first scene audio for preview (${audioUrls.length} file)...`
    )

    // Use Remotion's buffer state to pause playback until audio is ready
    const delayHandle = buffer.delayPlayback()

    // Preload all audio URLs (preloadAudio returns cleanup function, not Promise)
    const cleanupFunctions: (() => void)[] = []

    audioUrls.forEach(url => {
      try {
        console.log(
          `🎵 Preloading first scene audio:`,
          url.substring(0, 50) + '...'
        )
        const cleanup = preloadAudio(url)
        cleanupFunctions.push(cleanup)
      } catch (error) {
        console.warn(
          `⚠️ First scene audio failed to preload:`,
          url.substring(0, 50) + '...',
          error
        )
      }
    })

    console.log('✅ All audio preloading initiated')

    // Since preloadAudio doesn't return promises, we use a shorter delay
    // to allow the browser time to start preloading before unblocking
    setTimeout(() => {
      console.log('🎵 Audio preloading delay completed, resuming playback')
      delayHandle.unblock()
    }, 1000) // 1 second delay to allow preloading to start

    // Store cleanup functions for component cleanup
    const allCleanupFunctions = cleanupFunctions

    // Cleanup function to ensure handle is always unblocked and preloads are cleaned up
    return () => {
      try {
        delayHandle.unblock()
      } catch (error) {
        // Handle might already be resolved
        console.debug('Audio preloader cleanup:', error)
      }

      // Clean up all audio preloads
      allCleanupFunctions.forEach((cleanup, index) => {
        try {
          cleanup()
        } catch (error) {
          console.debug(`Audio preload cleanup ${index + 1} failed:`, error)
        }
      })
    }
  }, [scenes, isRenderingContext, buffer])
}
