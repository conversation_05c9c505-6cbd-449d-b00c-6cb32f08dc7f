/**
 * SceneVisual Component
 *
 * Consolidated scene visual rendering component that combines logic from both
 * the preview and rendering implementations to ensure consistency.
 *
 * This component handles:
 * - Media rendering (images and videos)
 * - Ken <PERSON> effects
 * - Transition effects
 * - Background blur effects
 * - Fallback content for scenes without media
 */

import React from 'react'
import {
  AbsoluteFill,
  useCurrentFrame,
  interpolate,
  OffthreadVideo,
  Loop,
} from 'remotion'
import type { SceneVisualProps, TransitionState } from '../../types'
import {
  applyTransition,
  createTransitionState,
  TRANSITION_FRAMES,
} from '../../utils'

const SceneVisualComponent: React.FC<SceneVisualProps> = ({
  scene,
  durationInFrames,
  transitionIn,
  transitionOut,
}) => {
  const defaultFrame = useCurrentFrame()
  const frame = transitionIn ? transitionIn.frame : defaultFrame
  const media = scene.media

  // Ken Burns effect configuration
  const kenBurns = scene.media?.kenBurns || 'none'
  const effectDuration = scene.media?.effectDuration || durationInFrames / 30

  // Initialize transition state
  const state: TransitionState = createTransitionState()

  // Apply Ken <PERSON> effects
  if (kenBurns === 'zoom-in') {
    state.scale = interpolate(frame, [0, effectDuration * 30], [1, 1.15], {
      extrapolateRight: 'clamp',
    })
  } else if (kenBurns === 'zoom-out') {
    state.scale = interpolate(frame, [0, effectDuration * 30], [1.15, 1], {
      extrapolateRight: 'clamp',
    })
  } else if (kenBurns === 'pan-left') {
    state.translateX = interpolate(frame, [0, effectDuration * 30], [0, -10], {
      extrapolateRight: 'clamp',
    })
  } else if (kenBurns === 'pan-right') {
    state.translateX = interpolate(frame, [0, effectDuration * 30], [0, 10], {
      extrapolateRight: 'clamp',
    })
  } else if (kenBurns === 'pan-up') {
    state.translateY = interpolate(frame, [0, effectDuration * 30], [0, -10], {
      extrapolateRight: 'clamp',
    })
  } else if (kenBurns === 'pan-down') {
    state.translateY = interpolate(frame, [0, effectDuration * 30], [0, 10], {
      extrapolateRight: 'clamp',
    })
  }

  // Apply transition effects
  if (transitionIn?.active) {
    const progress = interpolate(
      transitionIn.frame,
      [0, TRANSITION_FRAMES],
      [0, 1],
      {
        extrapolateRight: 'clamp',
      }
    )
    applyTransition(transitionIn.type, progress, true, state)
  }

  if (transitionOut?.active) {
    const progress = interpolate(
      transitionOut.frame,
      [0, TRANSITION_FRAMES],
      [0, 1],
      {
        extrapolateRight: 'clamp',
      }
    )
    applyTransition(transitionOut.type, progress, false, state)
  }

  // Common style properties
  const commonStyle = {
    maxWidth: '100%',
    maxHeight: '100%',
    borderRadius: 12,
    opacity: state.opacity,
    transition: 'none',
    clipPath: state.clipPath || undefined,
    filter: state.filter || undefined,
    backgroundColor: state.backgroundColor || undefined,
    imageRendering: state.filter?.includes('blur')
      ? ('pixelated' as const)
      : undefined,
  }

  // Separate styles for crop and blur modes (matching preview exactly)
  const cropModeStyle = {
    ...commonStyle,
    position: 'absolute' as const,
    inset: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover' as const,
    transform: `scale(${state.scale}) translateX(${state.translateX}%) translateY(${state.translateY}%) ${state.transform || ''}`,
    zIndex: 1,
  }

  const blurModeStyle = {
    ...commonStyle,
    position: 'absolute' as const,
    width: 'auto',
    height: '100%',
    objectFit: 'contain' as const,
    left: '50%',
    top: '50%',
    transform: `translate(-50%, -50%) scale(${state.scale}) translateX(${state.translateX}%) translateY(${state.translateY}%) ${state.transform || ''}`,
    zIndex: 1,
  }

  // Calculate media duration for video looping
  const mediaDurationInFrames = media?.duration
    ? Number(media.duration) * 30 - 15
    : 0

  return (
    <AbsoluteFill
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: state.backgroundColor,
      }}
    >
      {media?.url && media.type === 'image' && (
        <>
          {/* Background blur effect - only show if not crop mode */}
          {scene.media?.fit !== 'crop' && (
            <img
              src={media.url}
              alt='Scene media background'
              style={{
                position: 'absolute',
                inset: 0,
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                filter: 'blur(24px) brightness(0.7)',
                transform: 'scale(1.1)',
                zIndex: 0,
              }}
            />
          )}
          <img
            src={media.url}
            alt='Scene media'
            style={scene.media?.fit === 'crop' ? cropModeStyle : blurModeStyle}
          />
        </>
      )}
      {media?.url && media.type === 'video' && (
        <>
          {/* Background blur effect for video - only show if not crop mode */}
          {scene.media?.fit !== 'crop' && media.thumbnail && (
            <img
              src={media.thumbnail}
              alt='Video background'
              style={{
                position: 'absolute',
                inset: 0,
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                filter: 'blur(24px) brightness(0.7)',
                transform: 'scale(1.1)',
                zIndex: 0,
              }}
            />
          )}
          <Loop durationInFrames={mediaDurationInFrames}>
            <OffthreadVideo
              src={media.url}
              style={
                scene.media?.fit === 'crop' ? cropModeStyle : blurModeStyle
              }
              muted
            />
          </Loop>
        </>
      )}
      {/* Fallback content for scenes without media */}
      {!media?.url && (
        <div
          style={{
            width: '100%',
            height: '100%',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '24px',
            fontWeight: 'bold',
          }}
        >
          {scene.name || 'Scene'}
        </div>
      )}
    </AbsoluteFill>
  )
}

SceneVisualComponent.displayName = 'SceneVisual'

export const SceneVisual = React.memo(SceneVisualComponent)
