/**
 * ScenesVideo Component
 *
 * Main video composition component that consolidates logic from both
 * preview and rendering implementations to ensure consistency.
 *
 * This component handles:
 * - Scene sequencing and timing
 * - Audio playback (both scene-based and speech-based)
 * - Background music
 * - Subtitle display
 * - Transition effects between scenes
 * - Progressive asset loading via premounting to prevent WebAudio renderer overload
 */

import React from 'react'
import { Sequence, AbsoluteFill, useCurrentFrame, Audio } from 'remotion'
import type { BaseSceneProps } from '../../types'
import { SceneVisual } from './SceneVisual'
import { SubtitleDisplay } from '../subtitles/SubtitleDisplay'
import { TRANSITION_FRAMES } from '../../utils'

interface ScenesVideoProps extends BaseSceneProps {
  selectedMusic?: {
    id: string
    title: string
    genre: string
    mood: string
    artistName: string
    artistUrl?: string
    provider: string
    licenseId: string
    sourceUrl?: string
    previewUrl: string
    durationMillis: number
  } | null
  musicVolume?: number
  musicEnabled?: boolean
  compositionWidth?: number
  compositionHeight?: number
  speech?: {
    enabled: boolean
    src: string
    name: string
    volume: number
    transcript: {
      captions: Array<{
        start: number
        end: number
        sentence: string
        wordBoundries: Array<{ start: number; end: number; word: string }>
      }>
      status: string
    }
  } | null
}

export const ScenesVideo: React.FC<ScenesVideoProps> = ({
  scenes,
  subtitlePosition,
  captionStyle,
  selectedMusic,
  musicVolume = 50,
  musicEnabled = true,
  compositionWidth = 960,
  compositionHeight = 540,
  speech,
}) => {
  let frameOffset = 0
  const globalFrame = useCurrentFrame()
  const fps = 30

  // Calculate total video duration and music handling
  const totalVideoSeconds = speech
    ? // For speech-based workflows, use speech duration from captions
      speech.transcript?.captions?.length > 0
      ? Math.max(...speech.transcript.captions.map(caption => caption.end))
      : 0
    : // For text-based workflows, use scene voiceover durations
      scenes.reduce(
        (acc, scene) =>
          acc +
          (typeof scene.voiceover?.audioDuration === 'number'
            ? scene.voiceover.audioDuration
            : 0),
        0
      )

  const totalVideoFrames = Math.round(totalVideoSeconds * fps)
  const backgroundMusicDurationFrames = selectedMusic?.durationMillis
    ? Math.round((selectedMusic.durationMillis / 1000) * fps)
    : 0

  return (
    <AbsoluteFill style={{ background: '#eee' }}>
      {/* Speech Audio for Audio/Podcast Workflows */}
      {speech && speech.enabled && speech.src && (
        <Audio
          src={speech.src}
          volume={speech.volume / 100}
          pauseWhenBuffering
        />
      )}

      {/* Background Music */}
      {selectedMusic && musicEnabled && (
        <>
          {(() => {
            // Use previewUrl which contains the actual MP3 file
            const musicUrl = selectedMusic.previewUrl
            if (!musicUrl) return null

            return backgroundMusicDurationFrames > 0 &&
              totalVideoFrames > backgroundMusicDurationFrames ? (
              /* Loop the music if video is longer than music */
              <Audio
                src={musicUrl}
                volume={musicVolume / 100}
                pauseWhenBuffering
              />
            ) : (
              /* Single play if music is longer than or equal to video */
              <Audio
                src={musicUrl}
                volume={musicVolume / 100}
                pauseWhenBuffering
              />
            )
          })()}
        </>
      )}

      {/* Scene Sequences */}
      {scenes.map((scene, idx) => {
        // For speech-based workflows, use scene.duration; for text-based, use voiceover.audioDuration
        const duration = speech
          ? scene.duration || 0
          : scene.voiceover?.audioDuration || scene.duration || 0
        const durationInFrames = Math.round(duration * 30)
        const startFrame = frameOffset
        frameOffset += durationInFrames

        const transitionIn =
          scene.media?.transition && scene.media.transition !== 'none'
            ? {
                type: scene.media.transition,
                active:
                  globalFrame >= startFrame &&
                  globalFrame < startFrame + TRANSITION_FRAMES,
                frame: globalFrame - startFrame,
              }
            : undefined

        const nextScene = scenes[idx + 1]
        const transitionOut =
          nextScene &&
          nextScene.media?.transition &&
          nextScene.media.transition !== 'none'
            ? {
                type: nextScene.media.transition,
                active:
                  globalFrame >=
                    startFrame + durationInFrames - TRANSITION_FRAMES &&
                  globalFrame < startFrame + durationInFrames,
                frame:
                  globalFrame -
                  (startFrame + durationInFrames - TRANSITION_FRAMES),
              }
            : undefined

        return (
          <Sequence
            key={scene.id}
            from={startFrame}
            durationInFrames={durationInFrames}
            premountFor={90} // Premount 3 seconds (90 frames at 30fps) before scene starts
          >
            {/* Scene Audio - Only render if no speech audio (avoid double audio) */}
            {!speech &&
              (() => {
                // Use the same audio source priority as the preview components
                const audioUrl =
                  scene.voiceover?.audioUrl || scene.voiceSettings?.voiceUrl
                const volume =
                  scene.voiceSettings?.voiceVol ??
                  scene.voiceover?.volume ??
                  100
                const speed =
                  scene.voiceSettings?.voiceSpeed ?? scene.voiceover?.speed ?? 1

                if (audioUrl) {
                  return (
                    <Audio
                      src={audioUrl}
                      volume={volume / 100} // Convert to 0-1 range like preview
                      playbackRate={speed} // Use direct speed value like preview
                      pauseWhenBuffering
                    />
                  )
                }
                return null
              })()}

            {/* Scene Visual */}
            <SceneVisual
              scene={scene}
              durationInFrames={durationInFrames}
              transitionIn={transitionIn}
              transitionOut={transitionOut}
              compositionWidth={compositionWidth}
              compositionHeight={compositionHeight}
            />
          </Sequence>
        )
      })}

      {/* Subtitles */}
      <SubtitleDisplay
        scenes={scenes}
        subtitlePosition={subtitlePosition}
        captionStyle={captionStyle}
        compositionWidth={compositionWidth}
        compositionHeight={compositionHeight}
        speech={speech}
      />
    </AbsoluteFill>
  )
}
