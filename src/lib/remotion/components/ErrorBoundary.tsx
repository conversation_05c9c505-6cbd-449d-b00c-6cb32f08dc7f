/**
 * Error Boundary for Remotion Components
 *
 * Provides graceful error handling for Remotion components to prevent
 * the entire preview from crashing when individual components fail.
 */

'use client'
import React, { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
}

export class RemotionErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Remotion component error:', error, errorInfo)
    this.props.onError?.(error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
            backgroundColor: '#f5f5f5',
            border: '2px dashed #ccc',
            borderRadius: '8px',
            flexDirection: 'column',
            padding: '20px',
            textAlign: 'center',
          }}
        >
          <div
            style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#666',
              marginBottom: '8px',
            }}
          >
            Video Preview Error
          </div>
          <div
            style={{
              fontSize: '14px',
              color: '#888',
              marginBottom: '12px',
            }}
          >
            Something went wrong while rendering the video preview
          </div>
          <div
            style={{
              fontSize: '12px',
              color: '#aaa',
              fontFamily: 'monospace',
              backgroundColor: '#f9f9f9',
              padding: '8px',
              borderRadius: '4px',
              maxWidth: '300px',
              wordBreak: 'break-word',
            }}
          >
            {this.state.error?.message || 'Unknown error'}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Hook version for functional components
export const useRemotionErrorHandler = () => {
  const handleError = React.useCallback(
    (error: Error, errorInfo: ErrorInfo) => {
      console.error('Remotion error:', error, errorInfo)
      // You could send this to an error reporting service
    },
    []
  )

  return handleError
}
