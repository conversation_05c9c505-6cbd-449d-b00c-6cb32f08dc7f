/**
 * SubtitleDisplay Component
 *
 * Consolidated subtitle display component that handles both scene-based
 * and speech-based subtitle workflows while maintaining exact compatibility
 * with existing implementations.
 */

import React from 'react'
import type { BaseSceneProps } from '../../types'
import {
  useSubtitleSystem,
  useSubtitleStyle,
} from '../../hooks/useSubtitleSystem'

interface SubtitleDisplayProps extends BaseSceneProps {
  compositionWidth?: number
  compositionHeight?: number
  speech?: {
    enabled: boolean
    src: string
    name: string
    volume: number
    transcript: {
      captions: Array<{
        start: number
        end: number
        sentence: string
        wordBoundries: Array<{ start: number; end: number; word: string }>
      }>
      status: string
    }
  } | null
}

const SubtitleDisplayComponent: React.FC<SubtitleDisplayProps> = ({
  scenes,
  subtitlePosition,
  captionStyle,
  compositionWidth = 960,
  compositionHeight = 540,
  speech,
}) => {
  // Get current subtitle state
  const { currentSentence, highlightIdx } = useSubtitleSystem({
    scenes,
    speech,
  })

  // Get subtitle styling
  const { subtitleX, subtitleY, styleProps, scaleFactor, getBackgroundColor } =
    useSubtitleStyle(
      subtitlePosition,
      captionStyle,
      compositionWidth,
      compositionHeight
    )

  // Don't render if no current sentence
  if (!currentSentence) {
    return null
  }

  return (
    <div
      style={{
        position: 'absolute',
        left: `${subtitleX}%`,
        top: `${subtitleY}%`,
        transform: 'translate(-50%, -50%)',
        textAlign: styleProps.textAlign || 'center',
        color: styleProps.textColor || '#ffffff',
        fontSize: Math.round((styleProps.fontSize || 48) * scaleFactor),
        fontWeight: styleProps.fontWeight === 'bold' ? 700 : 400,
        fontStyle: styleProps.fontStyle || 'normal',
        fontFamily: `"${styleProps.fontFamily || 'Inter'}", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif`,
        textShadow:
          styleProps.textShadow !== false
            ? '0 2px 8px rgba(0,0,0,0.8)'
            : 'none',
        pointerEvents: 'none',
        zIndex: 10,
        width: 'auto',
        maxWidth: `${styleProps.maxWidth || 90}%`,
        padding: Math.round((styleProps.padding || 16) * scaleFactor),
        background: getBackgroundColor(),
        borderRadius: Math.round((styleProps.borderRadius || 8) * scaleFactor),
        lineHeight: 1.3,
      }}
    >
      {currentSentence.words.map((w, i) => (
        <span
          key={i}
          style={{
            color: styleProps.textColor || '#ffffff',
            background:
              i === highlightIdx
                ? styleProps.highlightColor || '#3b82f6'
                : undefined,
            borderRadius: Math.round(
              (styleProps.borderRadius || 8) * scaleFactor
            ),
            padding: `${Math.round(2 * scaleFactor)}px ${Math.round(4 * scaleFactor)}px`,
            marginRight: Math.round(4 * scaleFactor),
            transition: 'color 0.1s, background 0.1s',
          }}
        >
          {w.word}
        </span>
      ))}
    </div>
  )
}

SubtitleDisplayComponent.displayName = 'SubtitleDisplay'

export const SubtitleDisplay = React.memo(SubtitleDisplayComponent)
