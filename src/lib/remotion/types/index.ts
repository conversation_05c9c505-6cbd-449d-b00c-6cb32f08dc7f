/**
 * Shared TypeScript types for Remotion components
 *
 * These types are extracted from existing implementations to ensure
 * consistency between preview and rendering systems.
 */

// Import and re-export existing types to maintain compatibility
import type { Scene, TransitionType } from '@/types/video'
import type { CaptionStyle } from '@/store'

export type { Scene, TransitionType, CaptionStyle }

// Transition-related types
export interface TransitionState {
  scale: number
  translateY: number
  translateX: number
  opacity: number
  clipPath: string
  filter: string
  backgroundColor: string
  transform: string
}

export interface TransitionConfig {
  type: TransitionType
  active: boolean
  frame: number
}

// Audio-related types
export interface AudioAlignment {
  characters: string[]
  character_start_times_seconds: number[]
  character_end_times_seconds: number[]
}

export interface WordAlignment {
  word: string
  start: number
  end: number
}

export interface SentenceAlignment {
  words: WordAlignment[]
  start: number
  end: number
}

// Media-related types
export interface MediaDimensions {
  width: number
  height: number
}

export interface SubtitlePosition {
  x: number
  y: number
}

// Component prop types
export interface BaseSceneProps {
  scenes: Scene[]
  subtitlePosition?: SubtitlePosition
  captionStyle?: CaptionStyle
}

export interface SceneVisualProps {
  scene: Scene
  durationInFrames: number
  transitionIn?: TransitionConfig
  transitionOut?: TransitionConfig
  compositionWidth?: number
  compositionHeight?: number
}

// Constants (remove TRANSITION_FRAMES to avoid duplicate export)
export const DEFAULT_FPS = 30
