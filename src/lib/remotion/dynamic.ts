/**
 * Dynamic imports for Remotion components
 *
 * This file provides lazy-loaded versions of Remotion components
 * for better bundle splitting and performance optimization.
 */

import { lazy } from 'react'

// Lazy load UnifiedComposition for both video rendering and browser preview
export const DynamicUnifiedComposition = lazy(() =>
  import('./compositions/UnifiedComposition').then(module => ({
    default: module.UnifiedComposition,
  }))
)

// Legacy exports for backward compatibility
export const DynamicMainComposition = DynamicUnifiedComposition
export const DynamicPreviewComposition = DynamicUnifiedComposition

// Lazy load individual components for advanced use cases
export const DynamicSceneVisual = lazy(() =>
  import('./components/core/SceneVisual').then(module => ({
    default: module.SceneVisual,
  }))
)

export const DynamicScenesVideo = lazy(() =>
  import('./components/core/ScenesVideo').then(module => ({
    default: module.ScenesVideo,
  }))
)

export const DynamicSubtitleDisplay = lazy(() =>
  import('./components/subtitles/SubtitleDisplay').then(module => ({
    default: module.SubtitleDisplay,
  }))
)
