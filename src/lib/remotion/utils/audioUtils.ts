/**
 * Audio utilities for Remotion components
 *
 * Extracted from existing implementations to provide shared audio processing
 * logic while maintaining exact compatibility with current behavior.
 */

import type {
  Scene,
  AudioAlignment,
  WordAlignment,
  SentenceAlignment,
} from '../types'

/**
 * Normalize speed to ensure it's in the correct range (0.5-2)
 * Maintains exact same logic as existing implementation
 */
export const normalizeSpeed = (speed: number | undefined): number => {
  if (!speed) return 1

  // If speed is in old format (50-200), convert to new format (0.5-2)
  if (speed >= 50 && speed <= 200) {
    return speed / 100
  }

  // If speed is already in new format, clamp to valid range
  return Math.max(0.5, Math.min(2, speed))
}

/**
 * Build global alignment from scenes
 * Maintains exact same logic as existing implementation
 */
export const buildGlobalAlignment = (
  scenes: Scene[]
): { char: string; start: number; end: number }[] => {
  let offset = 0
  const globalAlignment: { char: string; start: number; end: number }[] = []

  for (const scene of scenes) {
    // Check for new API captions format first
    if (scene.captions && scene.captions.length > 0) {
      const speed = normalizeSpeed(
        scene.voiceSettings?.voiceSpeed ?? scene.voiceover?.speed ?? 1
      )

      for (const caption of scene.captions) {
        for (const wordData of caption.words) {
          // Use more precise calculation to avoid rounding errors
          const adjustedStart = parseFloat(
            (wordData.start / speed + offset).toFixed(3)
          )
          const adjustedEnd = parseFloat(
            (wordData.end / speed + offset).toFixed(3)
          )

          globalAlignment.push({
            char: wordData.word,
            start: adjustedStart,
            end: adjustedEnd,
          })
          const isLastWord =
            caption.words.indexOf(wordData) === caption.words.length - 1
          if (!isLastWord) {
            const spaceStart = parseFloat(
              (wordData.end / speed + offset).toFixed(3)
            )
            const spaceEnd = parseFloat(
              (wordData.end / speed + offset + 0.05).toFixed(3)
            )

            globalAlignment.push({
              char: ' ',
              start: spaceStart,
              end: spaceEnd,
            })
          }
        }
      }
      offset += scene.duration || 0
      continue
    }

    // Fallback to old alignment format
    const alignment = scene.voiceSettings?.alignment as
      | AudioAlignment
      | undefined

    if (!alignment || !Array.isArray(alignment.characters)) {
      offset += scene.duration || 0
      continue
    }

    const chars = alignment.characters
    const starts = alignment.character_start_times_seconds
    const ends = alignment.character_end_times_seconds
    const speed = normalizeSpeed(
      scene.voiceSettings?.voiceSpeed ?? scene.voiceover?.speed ?? 1
    )

    for (let i = 0; i < chars.length; i++) {
      // Use more precise calculation to avoid rounding errors
      const adjustedStart = parseFloat((starts[i] / speed + offset).toFixed(3))
      const adjustedEnd = parseFloat((ends[i] / speed + offset).toFixed(3))

      globalAlignment.push({
        char: chars[i],
        start: adjustedStart,
        end: adjustedEnd,
      })
    }
    offset += scene.duration || 0
  }

  return globalAlignment
}

/**
 * Build word alignment from global alignment
 * Maintains exact same logic as existing implementation
 */
export const buildWordAlignment = (
  globalAlignment: { char: string; start: number; end: number }[]
): WordAlignment[] => {
  return globalAlignment
    .filter(a => a.char.trim() !== '')
    .map(a => ({ word: a.char, start: a.start, end: a.end }))
}

/**
 * Chunk words into sentences
 * Maintains exact same logic as existing implementation
 */
export const chunkWords = (
  words: WordAlignment[],
  chunkSize = 4
): SentenceAlignment[] => {
  const sentences: SentenceAlignment[] = []
  for (let i = 0; i < words.length; i += chunkSize) {
    const chunk = words.slice(i, i + chunkSize)
    if (chunk.length) {
      sentences.push({
        words: chunk,
        start: chunk[0].start,
        end: chunk[chunk.length - 1].end,
      })
    }
  }
  return sentences
}

/**
 * Build global alignment from speech captions (for audio/podcast workflows)
 * Maintains exact same logic as existing implementation
 */
export const buildGlobalAlignmentFromSpeech = (
  captions: Array<{
    start: number
    end: number
    sentence: string
    wordBoundries: Array<{ start: number; end: number; word: string }>
  }>
): { char: string; start: number; end: number }[] => {
  const globalAlignment: { char: string; start: number; end: number }[] = []

  for (const caption of captions) {
    for (const wordData of caption.wordBoundries) {
      globalAlignment.push({
        char: wordData.word,
        start: wordData.start,
        end: wordData.end,
      })
      // Add space after each word except the last one
      const isLastWord =
        caption.wordBoundries.indexOf(wordData) ===
        caption.wordBoundries.length - 1
      if (!isLastWord) {
        globalAlignment.push({
          char: ' ',
          start: wordData.end,
          end: wordData.end + 0.05,
        })
      }
    }
  }

  return globalAlignment
}
