/**
 * Caption generation utilities for Remotion components
 *
 * These utilities handle caption generation from various sources including
 * ElevenLabs alignment data and basic text processing.
 */

import type { AudioAlignment } from '../types'

/**
 * Generate legacy captions from ElevenLabs alignment data
 * This function is used by Inngest functions for backward compatibility
 * Returns the old format with 'sentence' and 'wordBoundries'
 */
export function generateLegacyCaptionsFromElevenLabs(
  text: string,
  alignment: AudioAlignment,
  duration: number
) {
  if (!alignment || !Array.isArray(alignment.characters)) {
    return generateBasicLegacyCaptions(text, duration)
  }

  const chars = alignment.characters
  const starts = alignment.character_start_times_seconds
  const ends = alignment.character_end_times_seconds

  // Build word-level alignment
  const words: Array<{ word: string; start: number; end: number }> = []
  let currentWord = ''
  let wordStart = 0

  for (let i = 0; i < chars.length; i++) {
    const char = chars[i]
    const start = starts[i]
    const end = ends[i]

    if (char === ' ' || i === chars.length - 1) {
      if (currentWord.trim()) {
        words.push({
          word: currentWord.trim(),
          start: wordStart,
          end: end,
        })
      }
      currentWord = ''
      wordStart = i < chars.length - 1 ? starts[i + 1] : end
    } else {
      if (currentWord === '') {
        wordStart = start
      }
      currentWord += char
    }
  }

  // Group words into sentences (4 words per sentence)
  const captions = []
  const wordsPerSentence = 4

  for (let i = 0; i < words.length; i += wordsPerSentence) {
    const sentenceWords = words.slice(i, i + wordsPerSentence)
    if (sentenceWords.length > 0) {
      captions.push({
        start: sentenceWords[0].start,
        end: sentenceWords[sentenceWords.length - 1].end,
        sentence: sentenceWords.map(w => w.word).join(' '), // Legacy format for Inngest
        wordBoundries: sentenceWords.map(w => ({
          start: w.start,
          end: w.end,
          word: w.word,
        })), // Legacy format for Inngest
      })
    }
  }

  return captions
}

/**
 * Generate captions from ElevenLabs alignment data (modern version)
 * This is the updated version used by the scene editor components
 * Returns the new format with 'text' and 'words'
 */
export function generateCaptionsFromElevenLabs(
  text: string,
  alignment: AudioAlignment,
  duration: number
) {
  if (!alignment || !Array.isArray(alignment.characters)) {
    return generateBasicCaptions(text, duration)
  }

  const chars = alignment.characters
  const starts = alignment.character_start_times_seconds
  const ends = alignment.character_end_times_seconds

  // Build word-level alignment
  const words: Array<{ word: string; start: number; end: number }> = []
  let currentWord = ''
  let wordStart = 0

  for (let i = 0; i < chars.length; i++) {
    const char = chars[i]
    const start = starts[i]
    const end = ends[i]

    if (char === ' ' || i === chars.length - 1) {
      if (currentWord.trim()) {
        words.push({
          word: currentWord.trim(),
          start: wordStart,
          end: end,
        })
      }
      currentWord = ''
      wordStart = i < chars.length - 1 ? starts[i + 1] : end
    } else {
      if (currentWord === '') {
        wordStart = start
      }
      currentWord += char
    }
  }

  // Group words into sentences (4 words per sentence)
  const captions = []
  const wordsPerSentence = 4

  for (let i = 0; i < words.length; i += wordsPerSentence) {
    const sentenceWords = words.slice(i, i + wordsPerSentence)
    if (sentenceWords.length > 0) {
      captions.push({
        start: sentenceWords[0].start,
        end: sentenceWords[sentenceWords.length - 1].end,
        text: sentenceWords.map(w => w.word).join(' '), // Modern format
        words: sentenceWords.map(w => ({
          start: w.start,
          end: w.end,
          word: w.word,
        })), // Modern format
      })
    }
  }

  return captions
}

/**
 * Generate basic legacy captions when no alignment data is available
 * Returns the old format with 'sentence' and 'wordBoundries'
 */
export function generateBasicLegacyCaptions(text: string, duration: number) {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim())
  const timePerSentence = duration / sentences.length

  return sentences.map((sentence, index) => ({
    start: index * timePerSentence,
    end: (index + 1) * timePerSentence,
    sentence: sentence.trim(), // Legacy format
    wordBoundries: sentence
      .trim()
      .split(' ')
      .map((word, wordIndex, words) => {
        const wordDuration = timePerSentence / words.length
        return {
          start: index * timePerSentence + wordIndex * wordDuration,
          end: index * timePerSentence + (wordIndex + 1) * wordDuration,
          word: word,
        }
      }), // Legacy format
  }))
}

/**
 * Generate basic captions when no alignment data is available
 * Splits text into sentences and estimates timing
 * Returns the new format with 'text' and 'words'
 */
export function generateBasicCaptions(text: string, duration: number) {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim())
  const timePerSentence = duration / sentences.length

  return sentences.map((sentence, index) => ({
    start: index * timePerSentence,
    end: (index + 1) * timePerSentence,
    text: sentence.trim(), // Use 'text' instead of 'sentence'
    words: sentence
      .trim()
      .split(' ')
      .map((word, wordIndex, words) => {
        const wordDuration = timePerSentence / words.length
        return {
          start: index * timePerSentence + wordIndex * wordDuration,
          end: index * timePerSentence + (wordIndex + 1) * wordDuration,
          word: word,
        }
      }), // Use 'words' instead of 'wordBoundries'
  }))
}

/**
 * Estimate duration from text length
 * Used as fallback when audio duration is not available
 */
export function estimateDurationFromText(text: string): number {
  // Rough estimate: ~150 words per minute, ~5 characters per word
  const charactersPerSecond = (150 * 5) / 60
  return Math.max(1, text.length / charactersPerSecond)
}
