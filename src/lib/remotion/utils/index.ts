/**
 * Utility functions for Remotion components
 */

// Export specific functions to reduce memory usage during build
export {
  TRANSITION_FRAMES,
  getRandomTransition,
  applyTransition,
  createTransitionState,
} from './transitionUtils'
export {
  normalizeSpeed,
  buildGlobalAlignment,
  buildWordAlignment,
  chunkWords,
  buildGlobalAlignmentFromSpeech,
} from './audioUtils'
export {
  generateCaptionsFromElevenLabs,
  generateBasicCaptions,
} from './captionUtils'
