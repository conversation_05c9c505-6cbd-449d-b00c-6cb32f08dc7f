/**
 * Transition utilities for Remotion components
 * 
 * Extracted from existing implementations to provide shared transition logic
 * while maintaining exact compatibility with current behavior.
 */

import { interpolate, random } from 'remotion'
import type { TransitionType, TransitionState } from '../types'

export const TRANSITION_FRAMES = 20

/**
 * Get a random transition type
 * Maintains exact same logic as existing implementation
 */
export const getRandomTransition = (): TransitionType => {
  const transitions: TransitionType[] = [
    'fade',
    'fadeblack',
    'fadewhite',
    'wipeleft',
    'wiperight',
    'wipeup',
    'wipedown',
    'slideleft',
    'slideright',
    'slideup',
    'slidedown',
    'smoothleft',
    'smoothright',
    'smoothup',
    'smoothdown',
    'rectcrop',
    'circlecrop',
    'circleclose',
    'circleopen',
    'horzclose',
    'horzopen',
    'vertclose',
    'vertopen',
    'distance',
    'dissolve',
    'pixelize',
    'radial',
  ]
  return transitions[Math.floor(random(Date.now()) * transitions.length)]
}

/**
 * Apply transition effects to a transition state
 * Maintains exact same logic as existing implementations
 */
export const applyTransition = (
  type: TransitionType,
  progress: number,
  isIn: boolean,
  state: TransitionState
): void => {
  const p = isIn ? progress : 1 - progress

  switch (type) {
    case 'random':
      applyTransition(getRandomTransition(), progress, isIn, state)
      break

    case 'fade':
      state.opacity *= p
      break

    case 'fadeblack':
      state.opacity *= p
      state.backgroundColor = '#000'
      break

    case 'fadewhite':
      state.opacity *= p
      state.backgroundColor = '#fff'
      break

    case 'wipeleft':
      state.clipPath = `inset(0 ${100 - p * 100}% 0 0)`
      break

    case 'wiperight':
      state.clipPath = `inset(0 0 0 ${100 - p * 100}%)`
      break

    case 'wipeup':
      state.clipPath = `inset(${100 - p * 100}% 0 0 0)`
      break

    case 'wipedown':
      state.clipPath = `inset(0 0 ${100 - p * 100}% 0)`
      break

    case 'slideleft':
      state.translateX = isIn
        ? interpolate(p, [0, 1], [100, 0])
        : interpolate(p, [0, 1], [0, -100])
      break

    case 'slideright':
      state.translateX = isIn
        ? interpolate(p, [0, 1], [-100, 0])
        : interpolate(p, [0, 1], [0, 100])
      break

    case 'slideup':
      state.translateY = isIn
        ? interpolate(p, [0, 1], [100, 0])
        : interpolate(p, [0, 1], [0, -100])
      break

    case 'slidedown':
      state.translateY = isIn
        ? interpolate(p, [0, 1], [-100, 0])
        : interpolate(p, [0, 1], [0, 100])
      break

    case 'smoothleft':
    case 'smoothright':
    case 'smoothup':
    case 'smoothdown':
      const direction = type.replace('smooth', '')
      const ease = (t: number) => t * t * (3 - 2 * t)
      const smoothP = ease(p)
      switch (direction) {
        case 'left':
          state.translateX = isIn
            ? interpolate(smoothP, [0, 1], [100, 0])
            : interpolate(smoothP, [0, 1], [0, -100])
          break
        case 'right':
          state.translateX = isIn
            ? interpolate(smoothP, [0, 1], [-100, 0])
            : interpolate(smoothP, [0, 1], [0, 100])
          break
        case 'up':
          state.translateY = isIn
            ? interpolate(smoothP, [0, 1], [100, 0])
            : interpolate(smoothP, [0, 1], [0, -100])
          break
        case 'down':
          state.translateY = isIn
            ? interpolate(smoothP, [0, 1], [-100, 0])
            : interpolate(smoothP, [0, 1], [0, 100])
          break
      }
      break

    case 'rectcrop':
      const size = p * 100
      state.clipPath = `inset(${50 - size / 2}% ${50 - size / 2}% ${50 - size / 2}% ${50 - size / 2}%)`
      break

    case 'circlecrop':
      const radius = p * 150
      state.clipPath = `circle(${radius}% at center)`
      break

    case 'circleclose':
    case 'circleopen':
      const circleRadius = type === 'circleclose' ? (1 - p) * 150 : p * 150
      state.clipPath = `circle(${circleRadius}% at center)`
      break

    case 'horzclose':
    case 'horzopen':
      const horzSize = type === 'horzclose' ? (1 - p) * 50 : p * 50
      state.clipPath = `inset(${50 - horzSize}% 0)`
      break

    case 'vertclose':
    case 'vertopen':
      const vertSize = type === 'vertclose' ? (1 - p) * 50 : p * 50
      state.clipPath = `inset(0 ${50 - vertSize}%)`
      break

    case 'distance':
      state.scale = isIn
        ? interpolate(p, [0, 1], [2, 1])
        : interpolate(p, [0, 1], [1, 2])
      state.opacity *= p
      break

    case 'dissolve':
      state.opacity *= p
      state.filter = `grayscale(${1 - p}) contrast(${20 * p})`
      break

    case 'pixelize':
      const pixelSize = Math.max(2, Math.floor((1 - p) * 32))
      state.opacity *= p
      state.filter = `blur(${pixelSize / 2}px)`
      state.transform = `scale(${1 / pixelSize}) scale(${pixelSize})`
      break

    case 'radial':
      state.clipPath = `circle(${p * 150}% at center)`
      break
  }
}

/**
 * Create initial transition state
 */
export const createTransitionState = (): TransitionState => ({
  scale: 1,
  translateY: 0,
  translateX: 0,
  opacity: 1,
  clipPath: '',
  filter: '',
  backgroundColor: '',
  transform: '',
})
