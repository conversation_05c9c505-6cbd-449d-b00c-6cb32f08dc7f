import { plans } from '@/config/constants'
import type { Plan } from '@/types/pricing'

/**
 * Format storage value - show in GB if >= 1024MB
 */
function formatStorage(storageMB: number): string {
  if (storageMB >= 1024) {
    const storageGB = Math.round(storageMB / 1024)
    return `${storageGB}GB`
  }
  return `${storageMB}MB`
}

function getDisplayName(planName: string): string {
  // Extract base plan name (remove -monthly or -annual suffix)
  const basePlanName = planName.replace(/-monthly|-annual$/, '')

  // Map to correct display names
  switch (basePlanName.toLowerCase()) {
    case 'free':
      return 'Free'
    case 'basic':
      return 'Basic'
    case 'premium':
      return 'Premium'
    default:
      // Fallback: capitalize first letter
      return basePlanName.charAt(0).toUpperCase() + basePlanName.slice(1)
  }
}

/**
 * Convert plans from constants.ts to UI Plan format
 */
export function getUIPlans(
  billingPeriod: 'monthly' | 'annual' = 'annual'
): Plan[] {
  const filteredPlans = plans.filter(plan => {
    // Handle forever plans (free plan)
    if (plan.period === 'forever') return true

    // Handle new plan naming format (basic-monthly, basic-annual, etc.)
    if (plan.name.endsWith(`-${billingPeriod}`)) return true

    // Handle old plan naming format (fallback)
    if (plan.period === billingPeriod) return true

    return false
  })

  return filteredPlans.map(plan => {
    const isAnnual = plan.period === 'annual' || plan.name.endsWith('-annual')
    const isForever = plan.period === 'forever'
    const isMonthly =
      plan.period === 'monthly' || plan.name.endsWith('-monthly')

    // Calculate annual pricing for monthly plans
    const planPrice = plan.price || 0
    const annualPrice = isMonthly ? planPrice * 12 : planPrice
    const annualSavings =
      isMonthly && planPrice > 0
        ? `Save $${planPrice * 12 - annualPrice}`
        : undefined

    return {
      name: getDisplayName(plan.name), // Use display name instead of full name
      price: isForever ? '$0' : `$${planPrice}`,
      annualPrice: isAnnual ? `$${annualPrice}` : undefined,
      annualSavings: isAnnual ? annualSavings : undefined,
      billingPeriod: isForever ? '/forever' : `billed ${billingPeriod}ly`,
      description: getPlanDescription(plan.name),
      highlightTag: plan.name.startsWith('premium') ? 'Most Popular' : null,
      videoCreation: plan.limits.projects,
      videoDownloads: plan.limits.videoExports,
      maxVideoLength: getMaxVideoLength(plan.name),
      resolution: getResolution(plan.name),
      aiImageGenerator: `${plan.limits.aiImages} AI images`,
      storage: formatStorage(plan.limits.storage),
      publishToYoutube: plan.name.startsWith('premium'),
      proVoices: plan.name.startsWith('premium'),
      voiceCloning: false, // Not available in any plan currently
      curationSupport: false, // Not available in any plan currently
      buttonText: 'Get started',
      buttonVariant:
        plan.name === 'free' ? ('outline' as const) : ('default' as const),
    }
  })
}

function getPlanDescription(planName: string): string {
  // Extract base plan name (remove -monthly or -annual suffix)
  const basePlanName = planName.replace(/-monthly|-annual$/, '')

  switch (basePlanName) {
    case 'free':
      return 'For individuals'
    case 'basic':
      return 'For small to medium-sized businesses'
    case 'premium':
      return 'For growing businesses and teams'
    default:
      return 'For users'
  }
}

function getMaxVideoLength(planName: string): string {
  // Extract base plan name (remove -monthly or -annual suffix)
  const basePlanName = planName.replace(/-monthly|-annual$/, '')

  switch (basePlanName) {
    case 'free':
      return '1 minute'
    case 'basic':
      return '3 minutes'
    case 'premium':
      return '5 minutes'
    default:
      return '1 minute'
  }
}

function getResolution(planName: string): string {
  // Extract base plan name (remove -monthly or -annual suffix)
  const basePlanName = planName.replace(/-monthly|-annual$/, '')

  switch (basePlanName) {
    case 'free':
    case 'basic':
      return 'HD'
    case 'premium':
      return 'Full HD'
    default:
      return 'HD'
  }
}

/**
 * Get plan by name from constants
 */
export function getPlanByName(planName: string) {
  // Handle both old format (basic, premium) and new format (basic-monthly, basic-annual, etc.)
  const basePlanName = planName.replace(/-monthly|-annual$/, '')
  return plans.find(p => {
    const planBaseName = p.name.replace(/-monthly|-annual$/, '')
    return planBaseName.toLowerCase() === basePlanName.toLowerCase()
  })
}

/**
 * Get plan by name and period from constants
 */
export function getPlanByNameAndPeriod(
  planName: string,
  period?: 'monthly' | 'annual'
) {
  if (!period) {
    return getPlanByName(planName)
  }

  // Handle both old format (basic, premium) and new format (basic-monthly, basic-annual, etc.)
  const basePlanName = planName.replace(/-monthly|-annual$/, '')
  const targetPlanName = `${basePlanName}-${period}`

  return plans.find(p => p.name.toLowerCase() === targetPlanName.toLowerCase())
}

/**
 * Get plan limits by name and period
 */
export function getPlanLimits(planName: string, period?: 'monthly' | 'annual') {
  const plan = getPlanByNameAndPeriod(planName, period)
  return plan?.limits || plans[0].limits // Default to free plan limits
}
