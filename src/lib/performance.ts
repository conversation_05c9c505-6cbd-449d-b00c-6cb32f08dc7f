/**
 * Performance monitoring utilities for API endpoints
 */

interface PerformanceMetric {
  endpoint: string
  duration: number
  timestamp: Date
  success: boolean
  error?: string
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private maxMetrics = 100 // Keep last 100 metrics

  /**
   * Start timing an operation
   */
  startTimer(endpoint: string) {
    const startTime = performance.now()

    return {
      end: (success: boolean = true, error?: string) => {
        const duration = performance.now() - startTime

        this.addMetric({
          endpoint,
          duration,
          timestamp: new Date(),
          success,
          error,
        })

        // Log slow operations in development
        if (process.env.NODE_ENV === 'development' && duration > 1000) {
          console.warn(
            `🐌 Slow API call: ${endpoint} took ${duration.toFixed(2)}ms`
          )
        }

        return duration
      },
    }
  }

  /**
   * Add a performance metric
   */
  private addMetric(metric: PerformanceMetric) {
    this.metrics.push(metric)

    // Keep only the last N metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }
  }

  /**
   * Get performance statistics for an endpoint
   */
  getStats(endpoint: string) {
    const endpointMetrics = this.metrics.filter(m => m.endpoint === endpoint)

    if (endpointMetrics.length === 0) {
      return null
    }

    const durations = endpointMetrics.map(m => m.duration)
    const successCount = endpointMetrics.filter(m => m.success).length

    return {
      count: endpointMetrics.length,
      successRate: (successCount / endpointMetrics.length) * 100,
      avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      p95Duration: this.percentile(durations, 95),
      recentMetrics: endpointMetrics.slice(-10), // Last 10 calls
    }
  }

  /**
   * Get all performance statistics
   */
  getAllStats() {
    const endpoints = [...new Set(this.metrics.map(m => m.endpoint))]

    return endpoints.reduce(
      (stats, endpoint) => {
        stats[endpoint] = this.getStats(endpoint)
        return stats
      },
      {} as Record<string, ReturnType<typeof this.getStats>>
    )
  }

  /**
   * Calculate percentile
   */
  private percentile(arr: number[], p: number): number {
    const sorted = [...arr].sort((a, b) => a - b)
    const index = Math.ceil((p / 100) * sorted.length) - 1
    return sorted[index] || 0
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics = []
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

/**
 * Higher-order function to wrap API routes with performance monitoring
 */
export function withPerformanceMonitoring<
  T extends (...args: unknown[]) => Promise<Response>,
>(handler: T, endpoint: string): T {
  return (async (...args: Parameters<T>) => {
    const timer = performanceMonitor.startTimer(endpoint)

    try {
      const response = await handler(...args)
      const duration = timer.end(response.ok)

      // Add performance header in development
      if (process.env.NODE_ENV === 'development') {
        response.headers.set(
          'X-Performance-Duration',
          `${duration.toFixed(2)}ms`
        )
      }

      return response
    } catch (error) {
      timer.end(false, error instanceof Error ? error.message : 'Unknown error')
      throw error
    }
  }) as T
}

/**
 * Middleware to log performance metrics
 */
export function logPerformanceStats() {
  if (process.env.NODE_ENV === 'development') {
    const stats = performanceMonitor.getAllStats()
    console.table(
      Object.entries(stats).map(([endpoint, stat]) => ({
        endpoint,
        calls: stat?.count || 0,
        'success%': stat?.successRate.toFixed(1) || '0',
        'avg(ms)': stat?.avgDuration.toFixed(2) || '0',
        'p95(ms)': stat?.p95Duration.toFixed(2) || '0',
        'max(ms)': stat?.maxDuration.toFixed(2) || '0',
      }))
    )
  }
}
