# Toast System Documentation

## Overview

This toast system provides a comprehensive notification solution for the Adori AI platform, built on top of Sonner with custom theming and specialized functions.

## Basic Usage

```typescript
import { toast } from '@/lib/toast'

// Basic notifications
toast.success('Operation completed!')
toast.error('Something went wrong')
toast.warning('Please check your input')
toast.info('New feature available')

// Loading toast
const loadingId = toast.loading('Processing...')
// Later dismiss it
toast.dismiss(loadingId)
```

## Promise-Based Toasts

```typescript
// For regular promises
toast.promise(apiCall(), {
  loading: 'Processing...',
  success: 'Complete!',
  error: 'Failed!',
})

// With custom messages
toast.promise(uploadFile(), {
  loading: 'Uploading file...',
  success: data => `File uploaded: ${data.filename}`,
  error: err => `Upload failed: ${err.message}`,
})
```

## TanStack Query Integration

### 1. Using useMutation with Toast Integration

```typescript
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/lib/toast'

// Method 1: Handle in onSuccess/onError callbacks
const createVideoMutation = useMutation({
  mutationFn: async (videoData: VideoData) => {
    const response = await fetch('/api/videos', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(videoData),
    })
    if (!response.ok) throw new Error('Failed to create video')
    return response.json()
  },
  onMutate: () => {
    // Show loading toast when mutation starts
    return toast.loading('Creating video...')
  },
  onSuccess: (data, variables, context) => {
    // Dismiss loading toast and show success
    if (context) toast.dismiss(context)
    toast.success('Video created successfully!')

    // Invalidate queries
    queryClient.invalidateQueries({ queryKey: ['videos'] })
  },
  onError: (error, variables, context) => {
    // Dismiss loading toast and show error
    if (context) toast.dismiss(context)
    toast.error(`Failed to create video: ${error.message}`)
  },
})

// Usage in component
const handleCreateVideo = () => {
  createVideoMutation.mutate(videoData)
}
```

### 2. Custom Hook with Toast Integration

```typescript
// hooks/useCreateVideoMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/lib/toast'

export const useCreateVideoMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (videoData: VideoData) => {
      const response = await fetch('/api/videos', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(videoData),
      })
      if (!response.ok) throw new Error('Failed to create video')
      return response.json()
    },
    onMutate: async videoData => {
      // Show loading toast
      const toastId = toast.loading('Creating video...')

      // Optimistic update (optional)
      await queryClient.cancelQueries({ queryKey: ['videos'] })
      const previousVideos = queryClient.getQueryData(['videos'])

      return { toastId, previousVideos }
    },
    onSuccess: (data, variables, context) => {
      // Update loading toast to success
      if (context?.toastId) {
        toast.dismiss(context.toastId)
        toast.success('Video created successfully!')
      }

      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['videos'] })
    },
    onError: (error, variables, context) => {
      // Update loading toast to error
      if (context?.toastId) {
        toast.dismiss(context.toastId)
        toast.error(`Failed to create video: ${error.message}`)
      }

      // Rollback optimistic update
      if (context?.previousVideos) {
        queryClient.setQueryData(['videos'], context.previousVideos)
      }
    },
  })
}

// Usage in component
const createVideoMutation = useCreateVideoMutation()

const handleCreateVideo = () => {
  createVideoMutation.mutate(videoData)
}
```

### 3. Promise Toast with TanStack Query

```typescript
// Method 3: Using toast.promise with mutateAsync
const createVideoMutation = useMutation({
  mutationFn: async (videoData: VideoData) => {
    const response = await fetch('/api/videos', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(videoData),
    })
    if (!response.ok) throw new Error('Failed to create video')
    return response.json()
  },
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['videos'] })
  },
})

const handleCreateVideo = async () => {
  await toast.promise(createVideoMutation.mutateAsync(videoData), {
    loading: 'Creating video...',
    success: 'Video created successfully!',
    error: err => `Failed to create video: ${err.message}`,
  })
}
```

### 4. Query Error Handling with Toast

```typescript
// For queries that might fail
const {
  data: videos,
  error,
  isError,
} = useQuery({
  queryKey: ['videos'],
  queryFn: async () => {
    const response = await fetch('/api/videos')
    if (!response.ok) throw new Error('Failed to fetch videos')
    return response.json()
  },
  onError: error => {
    toast.error(`Failed to load videos: ${error.message}`)
  },
})

// Or handle in component effect
useEffect(() => {
  if (isError && error) {
    toast.error(`Failed to load videos: ${error.message}`)
  }
}, [isError, error])
```

### 5. Batch Operations with Progress

```typescript
const useBatchDeleteMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (videoIds: string[]) => {
      const results = []
      const toastId = toast.loading(`Deleting 0/${videoIds.length} videos...`)

      for (let i = 0; i < videoIds.length; i++) {
        try {
          const response = await fetch(`/api/videos/${videoIds[i]}`, {
            method: 'DELETE',
          })
          if (!response.ok)
            throw new Error(`Failed to delete video ${videoIds[i]}`)

          results.push({ id: videoIds[i], success: true })

          // Update progress
          toast.loading(`Deleting ${i + 1}/${videoIds.length} videos...`, {
            id: toastId,
          })
        } catch (error) {
          results.push({
            id: videoIds[i],
            success: false,
            error: error.message,
          })
        }
      }

      toast.dismiss(toastId)
      return results
    },
    onSuccess: results => {
      const successful = results.filter(r => r.success).length
      const failed = results.filter(r => !r.success).length

      if (failed === 0) {
        toast.success(`Successfully deleted ${successful} videos`)
      } else {
        toast.warning(`Deleted ${successful} videos, ${failed} failed`)
      }

      queryClient.invalidateQueries({ queryKey: ['videos'] })
    },
  })
}
```

### 6. Real-world Example: Voice Generation

```typescript
// hooks/useGenerateVoiceMutation.ts
export const useGenerateVoiceMutation = () => {
  return useMutation({
    mutationFn: async (data: { text: string; voiceId: string }) => {
      const response = await fetch('/api/elevenlabs/generate-speech', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })
      if (!response.ok) throw new Error('Failed to generate voice')
      return response.json()
    },
  })
}

// In component
const generateVoiceMutation = useGenerateVoiceMutation()

const handleGenerateVoice = async () => {
  try {
    await toast.promise(
      generateVoiceMutation.mutateAsync({
        text: selectedText,
        voiceId: selectedVoice.voice_id,
      }),
      {
        loading: 'Generating voice...',
        success: 'Voice generated successfully!',
        error: err => `Voice generation failed: ${err.message}`,
      }
    )
  } catch (error) {
    // Error already handled by toast.promise
    console.error('Voice generation failed:', error)
  }
}
```

### 7. Global Error Handling

```typescript
// providers/query-provider.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { toast } from '@/lib/toast'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      onError: error => {
        // Global error handling for queries
        toast.error(`Query failed: ${error.message}`)
      },
    },
    mutations: {
      onError: error => {
        // Global error handling for mutations
        toast.error(`Operation failed: ${error.message}`)
      },
    },
  },
})
```

## Best Practices for TanStack Query + Toast

1. **Use `mutateAsync` with `toast.promise`** for simple cases
2. **Use `onMutate`, `onSuccess`, `onError`** for complex scenarios with optimistic updates
3. **Store toast IDs in context** to properly dismiss loading toasts
4. **Handle errors gracefully** - don't double-toast the same error
5. **Use custom hooks** to encapsulate mutation + toast logic
6. **Consider global error handlers** for consistent error reporting

## Specialized Toast Functions

### Video Rendering Progress

```typescript
const id = toast.renderProgress('Starting render...')
toast.renderProgress('Processing...', id)
toast.renderProgress('Almost done...', id)
toast.dismiss(id)
toast.success('Video rendered successfully!')
```

### Caption Template Success

```typescript
toast.captionTemplateSuccess('Bold Style')
// Shows: "Caption template 'Bold Style' created successfully!"
```

### API Error Handling

```typescript
try {
  const result = await apiCall()
} catch (error) {
  toast.apiError(error, 'Failed to process request')
}
```

## Configuration

The toast system is configured in `src/components/ui/toaster.tsx` with:

- Theme integration (auto light/dark mode)
- Rich colors enabled
- Position: bottom-right
- Custom styling with CSS variables

All toasts automatically adapt to your current theme and use your brand colors.
