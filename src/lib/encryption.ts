import crypto from 'crypto'

// Use a consistent encryption key from environment
const ENCRYPTION_KEY =
  process.env.ENCRYPTION_KEY ||
  'default-key-for-development-only-change-in-production'
const ALGORITHM = 'aes-256-cbc'

// Cache the derived key to avoid expensive scryptSync operations
let cachedKey: Buffer | null = null
function getDerivedKey(): Buffer {
  if (!cachedKey) {
    cachedKey = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32)
  }
  return cachedKey
}

/**
 * Encrypts a string value using AES-256-CBC
 * @param text - The text to encrypt
 * @returns Encrypted string in format: iv:encryptedData
 */
export function encrypt(text: string): string {
  try {
    // Use cached derived key for better performance
    const key = getDerivedKey()

    // Generate a random initialization vector
    const iv = crypto.randomBytes(16)

    // Create cipher with IV
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv)

    // Encrypt the text
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')

    // Return iv:encryptedData format
    return `${iv.toString('hex')}:${encrypted}`
  } catch (error) {
    console.error('Encryption error:', error)
    throw new Error('Failed to encrypt data')
  }
}

/**
 * Decrypts a string value encrypted with the encrypt function
 * @param encryptedData - The encrypted string in format: iv:encryptedData
 * @returns Decrypted string
 */
export function decrypt(encryptedData: string): string {
  try {
    // Split the encrypted data
    const parts = encryptedData.split(':')
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted data format')
    }

    const [ivHex, encrypted] = parts

    // Use cached derived key for better performance
    const key = getDerivedKey()

    // Convert hex string back to buffer
    const iv = Buffer.from(ivHex, 'hex')

    // Create decipher with IV
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv)

    // Decrypt the data
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  } catch (error) {
    console.error('Decryption error:', error)
    throw new Error('Failed to decrypt data')
  }
}

/**
 * Safely encrypts OAuth tokens for storage
 * @param tokens - Object containing access_token and refresh_token
 * @returns Object with encrypted tokens
 */
export function encryptTokens(tokens: {
  access_token: string
  refresh_token: string
}): {
  encryptedAccessToken: string
  encryptedRefreshToken: string
} {
  return {
    encryptedAccessToken: encrypt(tokens.access_token),
    encryptedRefreshToken: encrypt(tokens.refresh_token),
  }
}

/**
 * Safely decrypts OAuth tokens from storage
 * @param encryptedTokens - Object containing encrypted tokens
 * @returns Object with decrypted tokens
 */
export function decryptTokens(encryptedTokens: {
  encryptedAccessToken: string
  encryptedRefreshToken: string
}): {
  access_token: string
  refresh_token: string
} {
  return {
    access_token: decrypt(encryptedTokens.encryptedAccessToken),
    refresh_token: decrypt(encryptedTokens.encryptedRefreshToken),
  }
}
