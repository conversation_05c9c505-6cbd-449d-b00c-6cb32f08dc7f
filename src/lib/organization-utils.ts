import { db } from '@/lib/db'
import { schema } from '@/db/schema'
import { eq, and, desc } from 'drizzle-orm'

export async function setActiveOrganizationForUser(userId: string) {
  try {
    // Find the user's organization
    const member = await db
      .select({
        organizationId: schema.member.organizationId,
        organizationName: schema.organization.name,
      })
      .from(schema.member)
      .innerJoin(
        schema.organization,
        eq(schema.member.organizationId, schema.organization.id)
      )
      .where(eq(schema.member.userId, userId))
      .limit(1)

    if (member.length > 0) {
      // Update the user's session to set the active organization
      await db
        .update(schema.session)
        .set({ activeOrganizationId: member[0].organizationId })
        .where(eq(schema.session.userId, userId))

      console.log(
        `Set active organization "${member[0].organizationName}" for user ${userId}`
      )
      return member[0].organizationId
    }

    return null
  } catch (error) {
    console.error('Failed to set active organization:', error)
    return null
  }
}

export async function getActiveOrganizationForUser(userId: string) {
  try {
    console.log('getActiveOrganizationForUser', userId)

    // First, try to get the active organization ID from the user's session
    const userSession = await db
      .select({
        activeOrganizationId: schema.session.activeOrganizationId,
      })
      .from(schema.session)
      .where(eq(schema.session.userId, userId))
      .orderBy(desc(schema.session.createdAt)) // Get the most recent session
      .limit(1)

    const activeOrganizationId =
      userSession.length > 0 ? userSession[0]?.activeOrganizationId : null

    console.log('activeOrganizationId from session:', activeOrganizationId)

    // If we have an active organization ID, get that specific organization
    if (activeOrganizationId) {
      const member = await db
        .select({
          organizationId: schema.member.organizationId,
          organizationName: schema.organization.name,
          organizationSlug: schema.organization.slug,
          organizationLogo: schema.organization.logo,
          role: schema.member.role,
        })
        .from(schema.member)
        .innerJoin(
          schema.organization,
          eq(schema.member.organizationId, schema.organization.id)
        )
        .where(
          and(
            eq(schema.member.userId, userId),
            eq(schema.member.organizationId, activeOrganizationId)
          )
        )
        .limit(1)

      if (member.length > 0) {
        console.log('Found active organization:', member[0])
        return member[0]
      }
    }

    // Fallback: If no active organization ID or the active organization is not found,
    // get the first organization the user is a member of
    const member = await db
      .select({
        organizationId: schema.member.organizationId,
        organizationName: schema.organization.name,
        organizationSlug: schema.organization.slug,
        organizationLogo: schema.organization.logo,
        role: schema.member.role,
      })
      .from(schema.member)
      .innerJoin(
        schema.organization,
        eq(schema.member.organizationId, schema.organization.id)
      )
      .where(eq(schema.member.userId, userId))
      .limit(1)

    console.log('Fallback organization:', member.length > 0 ? member[0] : null)
    return member.length > 0 ? member[0] : null
  } catch (error) {
    console.error('Failed to get active organization:', error)
    return null
  }
}
