/**
 * Application-wide constants
 *
 * This file contains constants that are used throughout the application.
 * Centralizing these values makes it easier to maintain and update them.
 */

// Breakpoints
export const MOBILE_BREAKPOINT = 768 // in pixels

// Other constants can be added here as needed
export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
export const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp']

/**
 * Stripe Plans Configuration
 *
 * This section contains all plan definitions, pricing, and feature configurations.
 * Update this when adding new plans or changing existing ones.
 */

export const plans = [
  {
    id: 1,
    name: 'free',
    period: 'forever',
    priceId: process.env.STRIPE_FREE_PLAN_PRICE_ID!,
    limits: {
      projects: 3,
      videoExports: 0,
      aiImages: 20,
      storage: 100, // 100MB
      teamMembers: 1, // Only the user themselves, no invitations allowed
      voiceRegenerations: 3, // per project
      videoDuration: 60, // 1 minute in seconds
      podcastDuration: 10 * 60, // 10 minutes in seconds
    },
    price: 0,
    features: [],
    gatedFeatures: {
      videoExport: true, // Video exports not available on free plan
      videoPublishing: true,
      teamInvitations: true, // Team invitations not available on free plan
    },
  },
  {
    id: 2,
    name: 'basic-monthly',
    period: 'monthly',
    priceId: process.env.STRIPE_BASIC_PLAN_PRICE_ID!,
    annualDiscountPriceId:
      process.env.STRIPE_BASIC_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    limits: {
      projects: 10,
      videoExports: 15,
      aiImages: 100,
      storage: 500, // 500MB
      teamMembers: 1, // Only the user themselves, no invitations allowed
      voiceRegenerations: 10, // per project
      videoDuration: 180, // 3 minutes in seconds
      podcastDuration: 30 * 60, // 30 minutes in seconds
    },
    price: 15,
    features: ['full HD exports'],
    gatedFeatures: {
      videoExport: false,
      videoPublishing: true,
      teamInvitations: true, // Team invitations not available on basic plan
    },
  },
  {
    id: 3,
    name: 'basic-annual',
    period: 'annual',
    priceId: process.env.STRIPE_BASIC_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    annualDiscountPriceId:
      process.env.STRIPE_BASIC_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    limits: {
      projects: 120,
      videoExports: 150,
      aiImages: 1200,
      storage: 6144,
      teamMembers: 1, // Only the user themselves, no invitations allowed
      voiceRegenerations: 10, // per project (annual equivalent)
      videoDuration: 180, // 3 minutes in seconds
      podcastDuration: 30 * 60, // 30 minutes in seconds
    },
    price: 144,
    features: [
      '720p video exports',
      '3 min video script limit',
      'up to 30 minutes Podcast',
    ],
    gatedFeatures: {
      videoExport: false,
      videoPublishing: true,
      teamInvitations: true, // Team invitations not available on basic annual plan
    },
  },
  {
    id: 4,
    name: 'premium-monthly',
    period: 'monthly',
    priceId: process.env.STRIPE_PREMIUM_PLAN_PRICE_ID!,
    annualDiscountPriceId:
      process.env.STRIPE_PREMIUM_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    limits: {
      projects: 20,
      videoExports: 30,
      aiImages: 200,
      storage: 1024, // 1GB
      teamMembers: 5, // Allow up to 5 team members including owner
      voiceRegenerations: 20, // per project
      videoDuration: 300, // 5 minutes in seconds
      podcastDuration: 80 * 60, // 80 minutes in seconds
    },
    price: 45,
    features: [
      '1080p video exports',
      '5 min video script limit',
      'upto 80 minutes Podcast',
      'Publish to Youtube',
      'Invite team members',
      'Bulk voice regeneration',
    ],
    gatedFeatures: {
      videoExport: false,
      videoPublishing: false,
      teamInvitations: false, // Team invitations allowed on premium plan
    },
  },
  {
    id: 5,
    name: 'premium-annual',
    period: 'annual',
    priceId: process.env.STRIPE_PREMIUM_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    annualDiscountPriceId:
      process.env.STRIPE_PREMIUM_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    limits: {
      projects: 240,
      videoExports: 300,
      aiImages: 2400,
      storage: 12288, // 12GB
      teamMembers: 5, // Allow up to 5 team members including owner
      voiceRegenerations: 20, // per project (annual equivalent)
      videoDuration: 300, // 5 minutes in seconds
      podcastDuration: 80 * 60, // 80 minutes in seconds
    },
    price: 432, // $45 * 12 months = $540, with 20% annual discount = $432
    features: [
      '1080p video exports',
      '5 min video script limit',
      'upto 80 minutes Podcast',
      'Publish to Youtube',
      'Invite team members',
      'Bulk voice regeneration',
    ],
    gatedFeatures: {
      videoExport: false,
      videoPublishing: false,
      teamInvitations: false, // Team invitations allowed on premium annual plan
    },
  },
]
