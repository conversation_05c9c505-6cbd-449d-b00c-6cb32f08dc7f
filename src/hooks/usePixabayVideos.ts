import { useQuery } from '@tanstack/react-query'

export type PixabayVideo = {
  id: number
  user?: string
  duration: number
  videos: {
    large?: { url: string; width: number; height: number }
    medium?: { url: string; width: number; height: number }
    small?: { url: string; width: number; height: number }
    tiny?: { url: string; width: number; height: number }
  }
}

export type PixabayImage = {
  id: number
  tags?: string
  user?: string
  webformatURL: string
  largeImageURL: string
  views: number
  downloads: number
}

export function usePixabayVideos(
  query: string,
  enabled: boolean,
  orientation?: 'landscape' | 'portrait' | 'square',
  page: number = 1
) {
  return useQuery({
    queryKey: ['pixabay-videos', query, orientation, page],
    enabled: enabled && !!query,
    queryFn: async () => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        page: page.toString(),
      })

      if (orientation) {
        params.append('orientation', orientation)
      }

      const res = await fetch(`/api/media/pixabay/videos?${params}`)
      if (!res.ok) throw new Error('Failed to fetch Pixabay videos')
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}

export function usePixabayImages(
  query: string,
  enabled: boolean,
  orientation: string = 'all',
  page: number = 1
) {
  return useQuery({
    queryKey: ['pixabay-images', query, orientation, page],
    enabled: enabled && !!query,
    queryFn: async () => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        orientation,
        page: page.toString(),
      })

      const res = await fetch(`/api/media/pixabay/images?${params}`)
      if (!res.ok) throw new Error('Failed to fetch Pixabay images')
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}
