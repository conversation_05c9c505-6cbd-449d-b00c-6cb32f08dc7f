'use client'

import { useQuery } from '@tanstack/react-query'

export interface GoogleImage {
  kind: string
  title: string
  htmlTitle: string
  link: string
  displayLink: string
  snippet: string
  htmlSnippet: string
  mime: string
  fileFormat: string
  image: {
    contextLink: string
    height: number
    width: number
    byteSize: number
    thumbnailLink: string
    thumbnailHeight: number
    thumbnailWidth: number
  }
}

export interface GoogleImagesResponse {
  items: GoogleImage[]
  searchInformation: {
    searchTime: number
    formattedSearchTime: string
    totalResults: string
    formattedTotalResults: string
  }
  queries: {
    request?: Array<{
      title: string
      totalResults: string
      searchTerms: string
      count: number
      startIndex: number
      inputEncoding: string
      outputEncoding: string
      safe: string
      cx: string
    }>
    nextPage?: Array<{
      title: string
      totalResults: string
      searchTerms: string
      count: number
      startIndex: number
      inputEncoding: string
      outputEncoding: string
      safe: string
      cx: string
    }>
  }
}

export function useGoogleImages(
  query: string,
  enabled: boolean,
  page: number = 1,
  rights: string = 'cc_publicdomain,cc_attribute'
) {
  return useQuery({
    queryKey: ['google-images', query, page, rights],
    enabled: enabled && !!query,
    queryFn: async (): Promise<GoogleImagesResponse> => {
      const params = new URLSearchParams({
        query,
        per_page: '10', // Google API max is 10 per request
        page: page.toString(),
        rights,
      })

      const res = await fetch(`/api/media/google-images?${params}`)
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch Google images')
      }
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (
        error.message.includes('Rate limit') ||
        error.message.includes('Page limit')
      ) {
        return false
      }
      return failureCount < 3
    },
  })
}
