import { useQuery } from '@tanstack/react-query'

export type TenorGif = {
  id: string
  title: string
  media_formats: {
    gif?: {
      url: string
      dims: [number, number] // [width, height]
      size: number
    }
    tinygif?: {
      url: string
      dims: [number, number]
      size: number
    }
    mp4?: {
      url: string
      dims: [number, number]
      size: number
    }
    tinymp4?: {
      url: string
      dims: [number, number]
      size: number
    }
    preview?: {
      url: string
      dims: [number, number]
      size: number
    }
  }
  created: number
  hasaudio: boolean
  tags: string[]
  itemurl: string
  url: string
}

export function useTenorGifs(
  query: string,
  enabled: boolean,
  orientation?: 'landscape' | 'portrait' | 'square',
  page: number = 1
) {
  return useQuery({
    queryKey: ['tenor-gifs', query, orientation, page],
    enabled: enabled && !!query,
    queryFn: async () => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        page: page.toString(),
      })

      if (orientation) {
        params.append('orientation', orientation)
      }

      const res = await fetch(`/api/media/tenor/gifs?${params}`)
      if (!res.ok) throw new Error('Failed to fetch Tenor GIFs')
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}
