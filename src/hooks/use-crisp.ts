'use client'

import { Crisp, EventsColors } from 'crisp-sdk-web'
import { authClient } from '@/lib/auth-client'
import { usePlanLimits } from '@/hooks/use-feature-gating'

// Check if we're in a production environment (same logic as CrispProvider)
const isProduction = () => {
  if (typeof window === 'undefined') return false

  const host = window.location.host
  const isLocalhost =
    host.includes('localhost') ||
    host.includes('127.0.0.1') ||
    host.includes('0.0.0.0') ||
    host.includes('::1') ||
    host.startsWith('192.168.') ||
    host.startsWith('10.') ||
    host.startsWith('172.')

  const isDevDomain =
    host.includes('.staging') ||
    host.includes('.dev') ||
    (host.includes('vercel.app') && !host.includes('adoriai.com'))

  return !isLocalhost && !isDevDomain
}

/**
 * Hook for interacting with Crisp customer support chat
 * Provides utilities for sending custom events and managing chat state
 */
export function useCrisp() {
  const { data: session } = authClient.useSession()
  const { data: activeOrganization } = authClient.useActiveOrganization()
  const { planName } = usePlanLimits()

  // Check if Crisp is available (production + authenticated user)
  const isCrispAvailable = isProduction() && !!session?.user

  /**
   * Push a custom event to Crisp with user context
   */
  const pushEvent = (
    eventName: string,
    eventData?: Record<string, unknown>,
    color: EventsColors = EventsColors.Blue
  ) => {
    if (!isCrispAvailable) {
      console.log('Crisp: Event not pushed (not available)', eventName)
      return
    }

    try {
      const enrichedData = {
        ...eventData,
        user_id: session?.user?.id,
        user_email: session?.user?.email,
        organization_id: activeOrganization?.id,
        organization_name: activeOrganization?.name,
        plan: planName,
        timestamp: new Date().toISOString(),
      }

      Crisp.session.pushEvent(eventName, enrichedData, color)
      console.log('Crisp: Event pushed', eventName, enrichedData)
    } catch (error) {
      console.error('Crisp: Failed to push event', error)
      // Don't throw error to avoid breaking the app
    }
  }

  /**
   * Update user plan metadata in Crisp
   */
  const updatePlanMetadata = () => {
    if (!isCrispAvailable) {
      console.log('Crisp: Plan metadata not updated (not available)')
      return
    }

    try {
      const planData = {
        current_plan: planName,
        plan_updated_at: new Date().toISOString(),
      }

      Crisp.session.setData(planData)
      console.log('Crisp: Plan metadata updated', planData)
    } catch (error) {
      console.error('Crisp: Failed to update plan metadata', error)
    }
  }

  /**
   * Show the chat widget
   */
  const showChat = () => {
    if (!isCrispAvailable) return
    try {
      Crisp.chat.show()
    } catch (error) {
      console.error('Crisp: Failed to show chat', error)
    }
  }

  /**
   * Hide the chat widget
   */
  const hideChat = () => {
    if (!isCrispAvailable) return
    try {
      Crisp.chat.hide()
    } catch (error) {
      console.error('Crisp: Failed to hide chat', error)
    }
  }

  /**
   * Open the chat widget
   */
  const openChat = () => {
    if (!isCrispAvailable) return
    try {
      Crisp.chat.open()
    } catch (error) {
      console.error('Crisp: Failed to open chat', error)
    }
  }

  /**
   * Close the chat widget
   */
  const closeChat = () => {
    if (!isCrispAvailable) return
    try {
      Crisp.chat.close()
    } catch (error) {
      console.error('Crisp: Failed to close chat', error)
    }
  }

  /**
   * Get unread message count
   */
  const getUnreadCount = (): number => {
    if (!isCrispAvailable) return 0
    try {
      return Crisp.chat.unreadCount()
    } catch (error) {
      console.error('Crisp: Failed to get unread count', error)
      return 0
    }
  }

  /**
   * Check if chat is opened
   */
  const isChatOpened = (): boolean => {
    if (!isCrispAvailable) return false
    try {
      return Crisp.chat.isChatOpened()
    } catch (error) {
      console.error('Crisp: Failed to check if chat is opened', error)
      return false
    }
  }

  /**
   * Check if chat is visible
   */
  const isChatVisible = (): boolean => {
    if (!isCrispAvailable) return false
    try {
      return Crisp.chat.isVisible()
    } catch (error) {
      console.error('Crisp: Failed to check if chat is visible', error)
      return false
    }
  }

  return {
    pushEvent,
    updatePlanMetadata,
    showChat,
    hideChat,
    openChat,
    closeChat,
    getUnreadCount,
    isChatOpened,
    isChatVisible,
    isCrispAvailable, // Expose availability status
  }
}
