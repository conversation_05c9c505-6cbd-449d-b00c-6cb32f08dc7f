'use client'

import { useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

export interface YouTubeConnection {
  id: string
  channelInfo: {
    id: string
    title: string
    thumbnailUrl: string | null
    description: string | null
    isVerified?: boolean
    canUploadCustomThumbnails?: boolean
    subscriberCount?: string
    videoCount?: string
    longUploadsStatus?: string
    customUrl?: string | null
  }
  connectedAt: string
  scopes: string[]
  isExpired?: boolean
  expiresAt?: string
}

export interface YouTubeConnectionStatus {
  connected: boolean
  connections: YouTubeConnection[]
}

/**
 * Utility function to automatically disconnect YouTube connections when they're revoked
 */
export async function handleYouTubeConnectionRevoked(
  queryClient: ReturnType<typeof useQueryClient>,
  connectionId?: string,
  showToast: boolean = true
) {
  try {
    // Call the disconnect API
    const response = await fetch('/api/youtube/auth/disconnect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(connectionId ? { connectionId } : {}),
    })

    if (response.ok) {
      // Invalidate all YouTube-related queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: ['youtube-connections'] })
      queryClient.invalidateQueries({ queryKey: ['youtube-playlists'] })

      if (showToast) {
        toast.success(
          'Invalid YouTube connection has been disconnected automatically.'
        )
      }
    } else {
      console.error(
        'Failed to disconnect YouTube connection:',
        response.statusText
      )
    }
  } catch (error) {
    console.error('Error disconnecting YouTube connection:', error)
  }
}

/**
 * Hook for managing YouTube connection status with automatic error handling
 */
export function useYouTubeStatus() {
  const queryClient = useQueryClient()

  const query = useQuery<YouTubeConnectionStatus>({
    queryKey: ['youtube-connections'],
    queryFn: async () => {
      const response = await fetch('/api/youtube/auth/status')

      if (!response.ok) {
        throw new Error('Failed to fetch YouTube connection status')
      }

      const data = await response.json()
      return {
        connected: data.connected || false,
        connections: data.connections || [],
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    refetchOnWindowFocus: false,
    retry: failureCount => {
      // Retry errors up to 2 times
      return failureCount < 2
    },
  })

  return {
    ...query,
    connections: query.data?.connections || [],
    isConnected: query.data?.connected || false,
    handleConnectionRevoked: (connectionId?: string, showToast?: boolean) =>
      handleYouTubeConnectionRevoked(queryClient, connectionId, showToast),
  }
}

/**
 * Hook for YouTube playlists with automatic error handling
 */
export function useYouTubePlaylists(
  connectionId: string,
  enabled: boolean = true
) {
  const queryClient = useQueryClient()

  return useQuery<{
    playlists: Array<{
      id: string
      title: string
      description: string
      thumbnailUrl: string | null
      itemCount: number
    }>
    channelId: string
    channelTitle: string
  }>({
    queryKey: ['youtube-playlists', connectionId],
    queryFn: async () => {
      const response = await fetch(
        `/api/youtube/playlists?connectionId=${connectionId}`
      )

      if (!response.ok) {
        // Handle specific CONNECTION_REVOKED error
        if (response.status === 410) {
          const errorData = await response.json().catch(() => ({}))
          if (errorData.code === 'CONNECTION_REVOKED') {
            // Automatically disconnect the invalid connection
            await handleYouTubeConnectionRevoked(
              queryClient,
              connectionId,
              true
            )
            throw new Error('CONNECTION_REVOKED')
          }
        }

        throw new Error('Failed to fetch playlists')
      }

      return response.json()
    },
    enabled: enabled && !!connectionId,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: (failureCount, error) => {
      // Don't retry CONNECTION_REVOKED errors
      if (error instanceof Error && error.message === 'CONNECTION_REVOKED') {
        return false
      }
      // Retry other errors up to 3 times
      return failureCount < 3
    },
  })
}
