'use client'

import { useState, useEffect, useCallback } from 'react'

// Constants for localStorage keys
const SIDEBAR_STATE_KEY = 'adori-sidebar-state'
const SIDEBAR_SUB_NAV_KEY = 'adori-sidebar-subnav-state'

// Types for sidebar state
export interface SidebarState {
  isCollapsed: boolean
}

export interface SubNavState {
  [key: string]: boolean // key is the nav item title, value is expanded state
}

// Default states
const DEFAULT_SIDEBAR_STATE: SidebarState = {
  isCollapsed: false,
}

const DEFAULT_SUB_NAV_STATE: SubNavState = {
  'Create Video': true, // Default to expanded for better UX
}

/**
 * Custom hook for managing sidebar state with localStorage persistence
 */
export function useSidebarState() {
  const [sidebarState, setSidebarState] = useState<SidebarState>(DEFAULT_SIDEBAR_STATE)
  const [subNavState, setSubNavState] = useState<SubNavState>(DEFAULT_SUB_NAV_STATE)
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize state from localStorage on mount
  useEffect(() => {
    try {
      // Load sidebar state
      const savedSidebarState = localStorage.getItem(SIDEBAR_STATE_KEY)
      if (savedSidebarState) {
        const parsed = JSON.parse(savedSidebarState) as SidebarState
        setSidebarState(parsed)
      }

      // Load sub-navigation state
      const savedSubNavState = localStorage.getItem(SIDEBAR_SUB_NAV_KEY)
      if (savedSubNavState) {
        const parsed = JSON.parse(savedSubNavState) as SubNavState
        setSubNavState(parsed)
      }
    } catch (error) {
      console.warn('Failed to load sidebar state from localStorage:', error)
      // Reset to defaults if parsing fails
      setSidebarState(DEFAULT_SIDEBAR_STATE)
      setSubNavState(DEFAULT_SUB_NAV_STATE)
    } finally {
      setIsInitialized(true)
    }
  }, [])

  // Save sidebar state to localStorage
  const updateSidebarState = useCallback((newState: Partial<SidebarState>) => {
    setSidebarState(prevState => {
      const updatedState = { ...prevState, ...newState }
      try {
        localStorage.setItem(SIDEBAR_STATE_KEY, JSON.stringify(updatedState))
      } catch (error) {
        console.warn('Failed to save sidebar state to localStorage:', error)
      }
      return updatedState
    })
  }, [])

  // Save sub-navigation state to localStorage
  const updateSubNavState = useCallback((navTitle: string, isExpanded: boolean) => {
    setSubNavState(prevState => {
      const updatedState = { ...prevState, [navTitle]: isExpanded }
      try {
        localStorage.setItem(SIDEBAR_SUB_NAV_KEY, JSON.stringify(updatedState))
      } catch (error) {
        console.warn('Failed to save sub-navigation state to localStorage:', error)
      }
      return updatedState
    })
  }, [])

  // Toggle sidebar collapsed state
  const toggleSidebar = useCallback(() => {
    updateSidebarState({ isCollapsed: !sidebarState.isCollapsed })
  }, [sidebarState.isCollapsed, updateSidebarState])

  // Toggle sub-navigation expanded state
  const toggleSubNav = useCallback((navTitle: string) => {
    const currentState = subNavState[navTitle] ?? false
    updateSubNavState(navTitle, !currentState)
  }, [subNavState, updateSubNavState])

  // Get sub-navigation state for a specific nav item
  const getSubNavState = useCallback((navTitle: string): boolean => {
    return subNavState[navTitle] ?? false
  }, [subNavState])

  return {
    // State
    sidebarState,
    subNavState,
    isInitialized,
    
    // Actions
    updateSidebarState,
    updateSubNavState,
    toggleSidebar,
    toggleSubNav,
    getSubNavState,
    
    // Computed values
    isCollapsed: sidebarState.isCollapsed,
  }
}
