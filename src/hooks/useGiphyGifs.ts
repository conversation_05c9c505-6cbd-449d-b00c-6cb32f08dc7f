import { useQuery } from '@tanstack/react-query'

export type GiphyGif = {
  id: string
  title: string
  images: {
    original: {
      url: string
      width: string
      height: string
      size: string
    }
    fixed_height: {
      url: string
      width: string
      height: string
      size: string
    }
    fixed_width: {
      url: string
      width: string
      height: string
      size: string
    }
    preview_gif: {
      url: string
      width: string
      height: string
      size: string
    }
  }
  user?: {
    display_name?: string
    username?: string
  }
  url: string
}

export function useGiphyGifs(
  query: string,
  enabled: boolean,
  orientation?: 'landscape' | 'portrait' | 'square',
  page: number = 1
) {
  return useQuery({
    queryKey: ['giphy-gifs', query, orientation, page],
    enabled: enabled && !!query,
    queryFn: async () => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        page: page.toString(),
      })

      if (orientation) {
        params.append('orientation', orientation)
      }

      const res = await fetch(`/api/media/giphy/gifs?${params}`)
      if (!res.ok) throw new Error('Failed to fetch Giphy GIFs')
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}
