'use client'

import { useEffect } from 'react'

interface UseNavigationGuardOptions {
  hasUnsavedChanges: boolean
  isSaving: boolean
  message?: string
}

export function useNavigationGuard({
  hasUnsavedChanges,
  isSaving,
  message = 'You have unsaved changes. Are you sure you want to leave without saving?',
}: UseNavigationGuardOptions) {
  useEffect(() => {
    // Browser navigation guard (back button, refresh, close tab)
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && !isSaving) {
        event.preventDefault()
        event.returnValue = message
        return message
      }
    }

    // Handle browser back/forward navigation
    const handlePopState = () => {
      if (hasUnsavedChanges && !isSaving) {
        const shouldLeave = window.confirm(message)
        if (!shouldLeave) {
          // Prevent navigation by pushing current state back
          window.history.pushState(null, '', window.location.href)
        }
      }
    }

    if (hasUnsavedChanges && !isSaving) {
      // Add a state to history to catch back button
      window.history.pushState(null, '', window.location.href)

      window.addEventListener('beforeunload', handleBeforeUnload)
      window.addEventListener('popstate', handlePopState)
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('popstate', handlePopState)
    }
  }, [hasUnsavedChanges, isSaving, message])
}
