import { useMutation } from '@tanstack/react-query'

interface TransformTextRequest {
  text: string
  action:
    | 'improve'
    | 'emojify'
    | 'longer'
    | 'shorter'
    | 'fix'
    | 'simplify'
    | 'custom'
  customInstruction?: string
}

interface TransformTextResponse {
  originalText: string
  transformedText: string
  action: string
  customInstruction?: string
}

interface TransformTextError {
  error: string
  details?: string
}

const transformText = async (
  params: TransformTextRequest
): Promise<TransformTextResponse> => {
  const response = await fetch('/api/openai/text-transform', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  })

  if (!response.ok) {
    const errorData: TransformTextError = await response.json()
    throw new Error(
      errorData.details || errorData.error || 'Failed to transform text'
    )
  }

  return response.json()
}

export const useOpenAITextTransform = () => {
  return useMutation({
    mutationFn: transformText,
    mutationKey: ['openai-text-transform'],
  })
}

export type { TransformTextRequest, TransformTextResponse, TransformTextError }
