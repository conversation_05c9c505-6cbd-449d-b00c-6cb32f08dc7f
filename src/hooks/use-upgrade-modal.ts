/**
 * Upgrade Modal Hook
 * 
 * Utility hook for triggering upgrade modals with feature-specific messaging
 */

import { useModalStore } from '@/store/use-modal-store'
import { GatedFeature, getFeatureDisplayName, getUpgradeMessage } from '@/lib/feature-gating'
import { useCurrentPlan } from '@/hooks/use-feature-gating'

export function useUpgradeModal() {
  const { onOpen } = useModalStore()
  const { plan } = useCurrentPlan()

  const openUpgradeModal = (
    feature: GatedFeature,
    customMessage?: string
  ) => {
    const featureName = getFeatureDisplayName(feature)
    const planName = plan?.name || 'free'
    const upgradeMessage = customMessage || getUpgradeMessage(feature, planName)

    onOpen('upgrade', {
      feature,
      featureName,
      upgradeMessage,
      currentPlan: planName,
    })
  }

  return { openUpgradeModal }
}
