/**
 * Gated Autopick Options Hook
 *
 * Returns autopick options with AI image generation gated based on usage limits
 */

import { useMemo } from 'react'
import { AUTOPICK_OPTIONS } from '@/app/(dashboard)/_components/video-form/types'
import { useAIImageLimits, usePlanLimits } from '@/hooks/use-feature-gating'

export function useGatedAutopickOptions() {
  const aiImageLimits = useAIImageLimits()

  const gatedOptions = useMemo(() => {
    if (aiImageLimits.allowed) {
      return AUTOPICK_OPTIONS
    }

    // Filter out only AI image option when limits are reached, keep mixed media
    return AUTOPICK_OPTIONS.filter(option => option.value !== 'ai-images')
  }, [aiImageLimits.allowed])

  return {
    options: gatedOptions,
    aiImagesBlocked: !aiImageLimits.allowed,
    upgradeMessage: aiImageLimits.upgradeMessage,
  }
}

/**
 * Hook to get the default autopick value based on plan and AI image limits
 */
export function useDefaultAutopickValue() {
  const { planName } = usePlanLimits()
  const aiImageLimits = useAIImageLimits()

  const defaultValue = useMemo(() => {
    // Free plan users always get "Mixed Media"
    if (planName === 'free') {
      return 'mix'
    }

    // Basic/Premium users get "AI Generated Images" if within quota, otherwise "Mixed Media"
    if (aiImageLimits.allowed) {
      return 'ai-images'
    }

    return 'mix'
  }, [planName, aiImageLimits.allowed])

  return {
    defaultValue,
    shouldUpdate: planName !== 'free' && !aiImageLimits.allowed, // Should update when AI quota exceeded
  }
}
