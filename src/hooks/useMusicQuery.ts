import { useQuery } from '@tanstack/react-query'
import { MusicApiResponse } from '@/app/api/music/route'

export interface MusicQueryParams {
  page?: number
  limit?: number
}

export interface MusicQueryResult {
  data: MusicApiResponse | undefined
  isLoading: boolean
  isError: boolean
  error: Error | null
  refetch: () => void
}

const fetchMusic = async (
  params: MusicQueryParams
): Promise<MusicApiResponse> => {
  const searchParams = new URLSearchParams()

  if (params.page) searchParams.set('page', params.page.toString())
  if (params.limit) searchParams.set('limit', params.limit.toString())

  const response = await fetch(`/api/music?${searchParams.toString()}`)

  if (!response.ok) {
    throw new Error('Failed to fetch music tracks')
  }

  return response.json()
}

export const useMusicQuery = (
  params: MusicQueryParams = {}
): MusicQueryResult => {
  const { page = 1, limit = 20 } = params

  const queryResult = useQuery({
    queryKey: ['music', { page, limit }],
    queryFn: () => fetchMusic({ page, limit }),
    // staleTime, gcTime, retry, and retryDelay now handled by global config
  })

  return {
    data: queryResult.data,
    isLoading: queryResult.isPending,
    isError: queryResult.isError,
    error: queryResult.error,
    refetch: queryResult.refetch,
  }
}
