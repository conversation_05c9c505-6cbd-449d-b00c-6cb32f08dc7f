'use client'

import { useQuery, useQueryClient } from '@tanstack/react-query'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'

export interface MediaAsset {
  id: string
  userId: string
  fileName: string
  originalName: string
  mimeType: string
  fileSize: number
  originalUrl: string
  thumbnailUrl?: string | null
  lowResUrl?: string | null
  width?: number | null
  height?: number | null
  duration?: string | null // stored as numeric string
  quality?: string | null
  metadata?: {
    aspectRatio?: number
    orientation?: 'landscape' | 'portrait' | 'square'
    dominantColors?: string[]
    tags?: string[]
    description?: string
    alt?: string
  } | null
  createdAt: Date | null
  updatedAt: Date | null
}

export interface MediaAssetsFilters {
  type?: 'image' | 'video' | 'all'
  search?: string
  orientation?: 'landscape' | 'portrait' | 'square'
  quality?: string
}

export interface MediaAssetsOptions {
  page?: number
  limit?: number
  filters?: MediaAssetsFilters
}

async function fetchMediaAssets(
  options: MediaAssetsOptions = {}
): Promise<{ assets: MediaAsset[]; total: number }> {
  const { page = 1, limit = 20, filters = {} } = options

  // Build query parameters
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  })

  if (filters.type && filters.type !== 'all') {
    params.append('type', filters.type)
  }

  if (filters.search) {
    params.append('search', filters.search)
  }

  if (filters.quality) {
    params.append('quality', filters.quality)
  }

  const response = await fetch(`/api/media/upload?${params.toString()}`)

  if (!response.ok) {
    throw new Error('Failed to fetch media assets')
  }

  const data = await response.json()

  // Transform the API response to match our MediaAsset interface
  const assets: MediaAsset[] = data.assets.map(
    (asset: {
      id: string
      user?: { id: string }
      metadata?: {
        fileName: string
        mimeType: string
        aspectRatio?: number
        orientation?: 'landscape' | 'portrait' | 'square'
        dominantColors?: string[]
        uploadedAt?: string
      }
      file_size: number
      url: string
      thumbnail: string
      low_res_url: string
      width: number
      height: number
      duration?: number
      quality?: string
    }) => ({
      id: asset.id as string,
      userId: asset.user?.id as string,
      fileName: asset.metadata?.fileName as string,
      originalName: asset.metadata?.fileName as string,
      mimeType: asset.metadata?.mimeType as string,
      fileSize: asset.file_size as number,
      originalUrl: asset.url as string,
      thumbnailUrl: asset.thumbnail as string,
      lowResUrl: asset.low_res_url as string,
      width: asset.width as number,
      height: asset.height as number,
      duration: asset.duration?.toString(),
      quality: asset.quality,
      metadata: {
        aspectRatio: asset.metadata?.aspectRatio,
        orientation: asset.metadata?.orientation,
        dominantColors: asset.metadata?.dominantColors,
        tags: [],
        description: undefined,
        alt: undefined,
      },
      createdAt: asset.metadata?.uploadedAt
        ? new Date(asset.metadata.uploadedAt)
        : null,
      updatedAt: null,
    })
  )

  return {
    assets,
    total: data.total_results || assets.length,
  }
}

export function useMediaAssets(options: MediaAssetsOptions = {}) {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()

  return useQuery({
    queryKey: ['media-assets', session?.user?.id, options],
    queryFn: () => {
      if (!session?.user?.id) {
        throw new Error('User not authenticated')
      }
      return fetchMediaAssets(options)
    },
    enabled: !!session?.user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useMediaAsset(assetId: string) {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()

  return useQuery({
    queryKey: ['media-asset', assetId],
    queryFn: async () => {
      if (!session?.user?.id) {
        throw new Error('User not authenticated')
      }

      const response = await fetch(`/api/media/upload?id=${assetId}`)

      if (!response.ok) {
        throw new Error('Failed to fetch media asset')
      }

      const data = await response.json()

      if (!data.assets || data.assets.length === 0) {
        return undefined
      }

      const asset = data.assets[0]

      // Transform the API response to match our MediaAsset interface
      return {
        id: asset.id,
        userId: asset.user.id,
        fileName: asset.metadata.fileName,
        originalName: asset.metadata.fileName,
        mimeType: asset.metadata.mimeType,
        fileSize: asset.file_size,
        originalUrl: asset.url,
        thumbnailUrl: asset.thumbnail,
        lowResUrl: asset.low_res_url,
        width: asset.width,
        height: asset.height,
        duration: asset.duration?.toString(),
        quality: asset.quality,
        metadata: {
          aspectRatio: asset.metadata.aspectRatio,
          orientation: asset.metadata.orientation,
          dominantColors: asset.metadata.dominantColors,
          tags: asset.tags,
          description: undefined,
          alt: undefined,
        },
        createdAt: asset.metadata.uploadedAt
          ? new Date(asset.metadata.uploadedAt)
          : null,
        updatedAt: null,
      } as MediaAsset
    },
    enabled: !!session?.user?.id && !!assetId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useInvalidateMediaAssets() {
  const queryClient = useQueryClient()
  // const { user } = useUser()
  const { data: session } = authClient.useSession()

  return () => {
    queryClient.invalidateQueries({
      queryKey: ['media-assets', session?.user?.id],
    })
  }
}

// Helper function to format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Helper function to format duration
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }
}

// Helper function to get media type from mime type
export function getMediaType(mimeType: string): 'image' | 'video' | 'unknown' {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  return 'unknown'
}

// Helper function to get quality badge color
export function getQualityBadgeColor(quality?: string | null): string {
  switch (quality) {
    case 'uhd':
      return 'bg-purple-100 text-purple-800'
    case 'hd':
      return 'bg-green-100 text-green-800'
    case 'sd':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Helper function to get orientation icon
export function getOrientationIcon(orientation?: string): string {
  switch (orientation) {
    case 'landscape':
      return '📱'
    case 'portrait':
      return '📱'
    case 'square':
      return '⬜'
    default:
      return '📄'
  }
}
