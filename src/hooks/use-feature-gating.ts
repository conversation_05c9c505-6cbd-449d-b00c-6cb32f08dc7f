/**
 * Feature Gating Hooks
 *
 * React hooks for checking feature access and managing gated features
 */

import { useMemo } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useCachedActiveSubscription } from '@/hooks/useSubscription'
import { useUsage } from '@/hooks/use-usage'
import {
  checkFeatureAccess,
  GatedFeature,
  FeatureCheckResult,
  getCurrentPlan,
  getUpgradeMessage,
} from '@/lib/feature-gating'

/**
 * Hook to check if a specific feature is allowed
 */
export function useFeatureAccess(
  feature: GatedFeature
): FeatureCheckResult & { isLoading: boolean } {
  const { subscription, isLoading: subscriptionLoading } =
    useCachedActiveSubscription()
  const { data: usage, isLoading: usageLoading } = useUsage()

  const result = useMemo(() => {
    if (subscriptionLoading || usageLoading) {
      return { allowed: true, isLoading: true }
    }

    const check = checkFeatureAccess(feature, subscription, usage)
    return { ...check, isLoading: false }
  }, [feature, subscription, usage, subscriptionLoading, usageLoading])

  return result
}

/**
 * Hook to check multiple features at once
 */
export function useMultipleFeatureAccess(
  features: GatedFeature[]
): Record<GatedFeature, FeatureCheckResult> & { isLoading: boolean } {
  const { subscription, isLoading: subscriptionLoading } =
    useCachedActiveSubscription()
  const { data: usage, isLoading: usageLoading } = useUsage()

  const results = useMemo(() => {
    if (subscriptionLoading || usageLoading) {
      const loadingResults = features.reduce(
        (acc, feature) => {
          acc[feature] = { allowed: true }
          return acc
        },
        {} as Record<GatedFeature, FeatureCheckResult>
      )

      return { ...loadingResults, isLoading: true }
    }

    const checks = features.reduce(
      (acc, feature) => {
        acc[feature] = checkFeatureAccess(feature, subscription, usage)
        return acc
      },
      {} as Record<GatedFeature, FeatureCheckResult>
    )

    return { ...checks, isLoading: false }
  }, [features, subscription, usage, subscriptionLoading, usageLoading])

  return results
}

/**
 * Hook to get current plan information
 */
export function useCurrentPlan() {
  const { subscription, isLoading } = useCachedActiveSubscription()

  const plan = useMemo(() => {
    if (isLoading) return null
    return getCurrentPlan(subscription)
  }, [subscription, isLoading])

  return { plan, isLoading }
}

/**
 * Hook to get plan limits for display
 */
export function usePlanLimits() {
  const { plan, isLoading } = useCurrentPlan()

  return {
    limits: plan?.limits || null,
    gatedFeatures: plan?.gatedFeatures || null,
    planName: plan?.name.split('-')[0].toLowerCase() || 'free',
    isLoading,
  }
}

/**
 * Hook for voice regeneration tracking using project API
 */
export function useVoiceRegeneration(projectId: string) {
  const { planName, limits } = usePlanLimits()

  // Get current regenerations from video store (reactive to store changes)
  const { data: attempts, refetch } = useQuery({
    queryKey: ['voice-regeneration-project', projectId],
    queryFn: async () => {
      if (!projectId) return 0

      try {
        const response = await fetch(`/api/projects/${projectId}`)
        if (!response.ok) return 0

        const project = await response.json()
        return project.voiceRegenerations || 0
      } catch {
        return 0
      }
    },
    staleTime: 30000, // Cache for 30 seconds
    refetchOnWindowFocus: false,
    enabled: !!projectId,
  })

  const limit = limits?.voiceRegenerations || 0

  return {
    allowed: (attempts || 0) < limit,
    attempts: attempts || 0,
    limit,
    remaining: Math.max(0, limit - (attempts || 0)),
    reason:
      (attempts || 0) >= limit
        ? `You've reached your limit of ${limit} voice regenerations per project`
        : '',
    upgradeRequired: (attempts || 0) >= limit,
    planName,
    refetch,
  }
}

/**
 * Hook to get upgrade message for a feature
 */
export function useUpgradeMessage(feature: GatedFeature) {
  const { planName } = usePlanLimits()

  return useMemo(() => {
    return getUpgradeMessage(feature, planName)
  }, [feature, planName])
}

/**
 * Hook to check if user can create new projects
 */
export function useCanCreateProject() {
  const projectCheck = useFeatureAccess('projects')

  return {
    canCreate: projectCheck.allowed,
    reason: projectCheck.reason,
    current: projectCheck.current || 0,
    limit: projectCheck.limit || 0,
    upgradeRequired: projectCheck.upgradeRequired || false,
    upgradeMessage: projectCheck.upgradeRequired
      ? getUpgradeMessage('projects', projectCheck.planName || 'free')
      : undefined,
  }
}

/**
 * Hook to check video duration limits
 */
export function useVideoDurationLimits() {
  const durationCheck = useFeatureAccess('videoDuration')

  return {
    maxDuration: durationCheck.limit || 60, // Default to 1 minute
    planName: durationCheck.planName || 'free',
    upgradeMessage: ``,
  }
}

/**
 * Hook to check podcast episode duration limits
 */
export function usePodcastDurationLimits() {
  const { planName, limits } = usePlanLimits()

  const maxDurationSeconds = limits?.podcastDuration || 30 * 60 // Default to 10 minutes
  const maxDurationMinutes = Math.floor(maxDurationSeconds / 60)

  return {
    maxDurationSeconds,
    maxDurationMinutes,
    planName,
    canProcessEpisode: (episodeDurationSeconds: number) =>
      episodeDurationSeconds <= maxDurationSeconds,
    upgradeMessage: `Your ${planName} plan allows podcast episodes up to ${maxDurationMinutes} minute(s). Upgrade to process longer episodes.`,
  }
}

/**
 * Hook to check AI image generation limits
 */
export function useAIImageLimits() {
  const aiImageCheck = useFeatureAccess('aiImages')

  return {
    allowed: aiImageCheck.allowed,
    current: aiImageCheck.current || 0,
    limit: aiImageCheck.limit || 0,
    remaining: Math.max(
      0,
      (aiImageCheck.limit || 0) - (aiImageCheck.current || 0)
    ),
    upgradeRequired: aiImageCheck.upgradeRequired || false,
    upgradeMessage: aiImageCheck.upgradeRequired
      ? getUpgradeMessage('aiImages', aiImageCheck.planName || 'free')
      : undefined,
  }
}

/**
 * Hook to refresh subscription and usage data
 * Useful after plan upgrades or when data needs to be refreshed
 */
export function useRefreshFeatureData() {
  const queryClient = useQueryClient()
  const { refetch: refetchSubscription } = useCachedActiveSubscription()

  const refreshAll = async () => {
    // Invalidate and refetch subscription data
    await refetchSubscription()

    // Invalidate usage data to force refresh
    queryClient.invalidateQueries({ queryKey: ['usage'] })

    // Invalidate all subscription-related queries
    queryClient.invalidateQueries({ queryKey: ['subscriptions'] })
  }

  return { refreshAll }
}

/**
 * Hook to check video export access
 */
export function useVideoExportAccess() {
  const exportCheck = useFeatureAccess('videoExports')

  return {
    allowed: exportCheck.allowed,
    reason: exportCheck.reason,
    current: exportCheck.current || 0,
    limit: exportCheck.limit || 0,
    upgradeRequired: exportCheck.upgradeRequired || false,
    upgradeMessage: exportCheck.upgradeRequired
      ? getUpgradeMessage('videoExports', exportCheck.planName || 'free')
      : undefined,
  }
}

/**
 * Hook to check video publishing access
 */
export function useVideoPublishingAccess() {
  const publishCheck = useFeatureAccess('videoPublishing')

  return {
    allowed: publishCheck.allowed,
    reason: publishCheck.reason,
    upgradeRequired: publishCheck.upgradeRequired || false,
    upgradeMessage: publishCheck.upgradeRequired
      ? getUpgradeMessage('videoPublishing', publishCheck.planName || 'free')
      : undefined,
  }
}

/**
 * Hook to check team member invitation access
 */
export function useTeamMemberAccess() {
  const teamInvitationCheck = useFeatureAccess('teamInvitations')

  return {
    allowed: teamInvitationCheck.allowed,
    current: teamInvitationCheck.current || 0,
    limit: teamInvitationCheck.limit || 1,
    remaining: Math.max(
      0,
      (teamInvitationCheck.limit || 1) - (teamInvitationCheck.current || 0)
    ),
    upgradeRequired: teamInvitationCheck.upgradeRequired || false,
    upgradeMessage: teamInvitationCheck.upgradeRequired
      ? getUpgradeMessage(
          'teamInvitations',
          teamInvitationCheck.planName || 'free'
        )
      : undefined,
  }
}
