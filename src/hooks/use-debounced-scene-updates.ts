'use client'

import { useCallback, useRef, useEffect } from 'react'
import { useVideoStore } from '@/store/video-store'
import type { Scene } from '@/types/video'

/**
 * Hook for debounced scene updates to prevent excessive store updates
 * @param sceneId - The ID of the scene to update
 * @param delay - Debounce delay in milliseconds (default: 300ms)
 * @returns Debounced update function
 */
export function useDebounceSceneUpdate(sceneId: string, delay = 300) {
  const { updateScene } = useVideoStore()
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)

  const debouncedUpdate = useCallback(
    (updates: Partial<Scene>) => {
      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // Set new timeout for debounced update
      timeoutRef.current = setTimeout(() => {
        updateScene(sceneId, updates)
      }, delay)
    },
    [sceneId, updateScene, delay]
  )

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return debouncedUpdate
}

/**
 * Hook for debounced voiceover speed updates
 * @param sceneId - The ID of the scene to update
 * @param delay - Debounce delay in milliseconds (default: 100ms for responsiveness)
 * @returns Debounced speed update function
 */
export function useDebounceVoiceoverSpeed(sceneId: string, delay = 100) {
  const { updateVoiceoverSpeed } = useVideoStore()
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)

  const debouncedSpeedUpdate = useCallback(
    (speed: number) => {
      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // Set new timeout for debounced update
      timeoutRef.current = setTimeout(() => {
        updateVoiceoverSpeed(sceneId, speed)
      }, delay)
    },
    [sceneId, updateVoiceoverSpeed, delay]
  )

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return debouncedSpeedUpdate
}

/**
 * Hook for immediate local state with debounced store sync
 * Provides responsive UI with efficient store updates
 * @param onUpdate - Function to call with debounced updates
 * @param delay - Debounce delay in milliseconds
 * @returns [localValue, setLocalValue, isUpdating]
 */
export function useDebouncedValue<T>(
  onUpdate: (value: T) => void,
  delay = 300
) {
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const isUpdatingRef = useRef(false)

  const debouncedUpdate = useCallback(
    (value: T) => {
      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      isUpdatingRef.current = true

      // Set new timeout for debounced update
      timeoutRef.current = setTimeout(() => {
        onUpdate(value)
        isUpdatingRef.current = false
      }, delay)
    },
    [onUpdate, delay]
  )

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return {
    debouncedUpdate,
    isUpdating: () => isUpdatingRef.current,
  }
}

/**
 * Utility function to create a debounced function
 * @param func - Function to debounce
 * @param delay - Debounce delay in milliseconds
 * @returns Debounced function with cancel method
 */
export function createDebouncedFunction<
  T extends (...args: unknown[]) => unknown,
>(func: T, delay: number): T & { cancel: () => void } {
  let timeoutId: NodeJS.Timeout | null = null

  const debouncedFunc = ((...args: Parameters<T>) => {
    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    // Set new timeout
    timeoutId = setTimeout(() => {
      func(...args)
      timeoutId = null
    }, delay)
  }) as T & { cancel: () => void }

  // Add cancel method
  debouncedFunc.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  return debouncedFunc
}
