import { useQuery } from '@tanstack/react-query'
import { Podcast } from './usePodcastSearch'

export type PodcastRSSResponse = {
  status: string
  podcast: Podcast
}

export function usePodcastRSS(rssUrl: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['podcast-rss', rssUrl],
    enabled: enabled && !!rssUrl && rssUrl.trim().length > 0,
    queryFn: async (): Promise<PodcastRSSResponse> => {
      const params = new URLSearchParams({
        url: rssUrl.trim(),
      })

      const res = await fetch(`/api/podcast/rss?${params}`)
      if (!res.ok) {
        const error = await res.text()
        throw new Error(error || 'Failed to lookup RSS feed')
      }
      return res.json()
    },
    // staleTime and retry now handled by global config
  })
}
