'use client'

import { useState } from 'react'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import { toast } from '@/lib/toast'
import {
  generateVideoThumbnail,
  generateImageThumbnail,
  extractVideoMetadata,
  extractImageMetadata,
  determineVideoQuality,
} from '@/lib/client-thumbnail-generator'

export interface MediaUploadResult {
  id: string
  originalUrl: string
  thumbnailUrl?: string
  lowResUrl?: string
  metadata: {
    width: number
    height: number
    fileSize: number
    mimeType: string
    duration?: number
    quality?: string
    aspectRatio: number
    orientation: 'landscape' | 'portrait' | 'square'
    dominantColors?: string[]
  }
}

export interface UploadProgress {
  stage:
    | 'analyzing'
    | 'generating-thumbnail'
    | 'uploading-original'
    | 'uploading-thumbnail'
    | 'saving-metadata'
    | 'complete'
  progress: number
  message: string
}

export interface BatchUploadProgress {
  totalFiles: number
  completedFiles: number
  currentFile: string
  overallProgress: number
  fileProgresses: Record<string, UploadProgress>
}

export function useMediaUpload() {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(
    null
  )
  const [batchProgress, setBatchProgress] =
    useState<BatchUploadProgress | null>(null)

  const uploadMedia = async (
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<MediaUploadResult> => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated')
    }

    setIsUploading(true)
    const updateProgress = (progress: UploadProgress) => {
      setUploadProgress(progress)
      onProgress?.(progress)
    }

    updateProgress({
      stage: 'analyzing',
      progress: 10,
      message: 'Processing media file...',
    })

    try {
      const isImage = file.type.startsWith('image/')
      const isVideo = file.type.startsWith('video/')

      if (!isImage && !isVideo) {
        throw new Error('File must be an image or video')
      }

      // Extract metadata client-side
      let metadata: {
        width: number
        height: number
        fileSize: number
        mimeType: string
        duration?: number
        quality?: string
        aspectRatio: number
        orientation: 'landscape' | 'portrait' | 'square'
        dominantColors?: string[]
      }
      if (isImage) {
        const imageMetadata = await extractImageMetadata(file)
        metadata = {
          ...imageMetadata,
          fileSize: file.size,
          mimeType: file.type,
        }
      } else {
        const videoMetadata = await extractVideoMetadata(file)
        metadata = {
          ...videoMetadata,
          fileSize: file.size,
          mimeType: file.type,
          quality: determineVideoQuality(
            videoMetadata.width,
            videoMetadata.height
          ),
        }
      }

      updateProgress({
        stage: 'generating-thumbnail',
        progress: 30,
        message: 'Generating thumbnail...',
      })

      // Generate thumbnail client-side
      let thumbnailBlob: Blob | null = null
      try {
        if (isImage) {
          const thumbnailResult = await generateImageThumbnail(file, {
            width: 400,
            height: 300,
            quality: 0.8,
          })
          thumbnailBlob = thumbnailResult.blob
        } else {
          // For videos, use dimensions that maintain aspect ratio
          const maxWidth = 400
          const maxHeight = 600 // Allow taller thumbnails for portrait videos

          let thumbWidth = maxWidth
          let thumbHeight = maxHeight

          if (metadata.aspectRatio > 1) {
            // Landscape video
            thumbHeight = Math.round(maxWidth / metadata.aspectRatio)
          } else {
            // Portrait video
            thumbWidth = Math.round(maxHeight * metadata.aspectRatio)
          }

          const thumbnailResult = await generateVideoThumbnail(file, {
            width: thumbWidth,
            height: thumbHeight,
            quality: 0.8,
            timeOffset: 1,
          })
          thumbnailBlob = thumbnailResult.blob
        }
      } catch (error) {
        console.warn('Failed to generate thumbnail:', error)
        // Continue without thumbnail
      }

      updateProgress({
        stage: 'uploading-original',
        progress: 50,
        message: 'Initiating upload...',
      })

      // Initiate upload to get signed URLs (no large payload)
      const initiateResponse = await fetch('/api/media/initiate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          metadata,
          includeThumbnail: Boolean(thumbnailBlob),
        }),
      })

      if (!initiateResponse.ok) {
        let errorMessage = 'Upload initiation failed'
        try {
          const errorData = await initiateResponse.json()
          errorMessage = errorData.error || errorMessage
        } catch {
          // If JSON parsing fails, use status text
          errorMessage = initiateResponse.statusText || errorMessage
        }
        throw new Error(errorMessage)
      }

      let initData: {
        id: string
        bucket: string
        filePath: string
        thumbnailPath?: string | null
        uploadUrl: string
        uploadToken: string
        thumbnailUploadUrl?: string
        thumbnailUploadToken?: string
      }

      try {
        initData = await initiateResponse.json()
      } catch {
        throw new Error('Failed to parse upload initiation response')
      }

      const uploadId = initData.id

      updateProgress({
        stage: 'uploading-original',
        progress: 60,
        message: 'Uploading to storage...',
      })

      // Perform direct upload to Supabase using signed URLs
      const putHeaders = {
        'x-upsert': 'false',
        Authorization: `Bearer ${initData.uploadToken}`,
        'Content-Type': file.type,
      }
      const uploadResp = await fetch(initData.uploadUrl, {
        method: 'PUT',
        headers: putHeaders as unknown as HeadersInit,
        body: file,
      })
      if (!uploadResp.ok) {
        throw new Error('Failed to upload file to storage')
      }

      updateProgress({
        stage: 'uploading-thumbnail',
        progress: 70,
        message: 'Uploading thumbnail...',
      })

      // Upload thumbnail if available
      let thumbnailSize = 0
      if (
        thumbnailBlob &&
        initData.thumbnailUploadUrl &&
        initData.thumbnailUploadToken
      ) {
        const thumbHeaders = {
          'x-upsert': 'false',
          Authorization: `Bearer ${initData.thumbnailUploadToken}`,
          'Content-Type': 'image/jpeg',
        }
        thumbnailSize = thumbnailBlob.size
        const thumbResp = await fetch(initData.thumbnailUploadUrl, {
          method: 'PUT',
          headers: thumbHeaders as unknown as HeadersInit,
          body: thumbnailBlob,
        })
        if (!thumbResp.ok) {
          console.warn('Thumbnail upload failed')
        }
      }

      updateProgress({
        stage: 'saving-metadata',
        progress: 90,
        message: 'Finalizing upload...',
      })

      // Tell server to finalize and return asset info
      const completeResp = await fetch('/api/media/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: uploadId,
          bucket: initData.bucket,
          filePath: initData.filePath,
          thumbnailPath: initData.thumbnailPath,
          fileSize: file.size,
          thumbnailSize,
          clientMetadata: {
            aspectRatio: metadata.aspectRatio,
            orientation: metadata.orientation,
            dominantColors: metadata.dominantColors,
          },
        }),
      })

      if (!completeResp.ok) {
        let errorMessage = 'Failed to finalize upload'
        try {
          const errorData = await completeResp.json()
          errorMessage = errorData.error || errorMessage
        } catch {
          errorMessage = completeResp.statusText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const result = await completeResp.json()

      updateProgress({
        stage: 'complete',
        progress: 100,
        message: 'Upload complete!',
      })

      // Transform the response to match our expected format
      const uploadResult: MediaUploadResult = {
        id: result.id,
        originalUrl: result.url,
        thumbnailUrl: result.thumbnail,
        lowResUrl: result.low_res_url,
        metadata: {
          width: result.width,
          height: result.height,
          fileSize: result.file_size,
          mimeType: result.metadata.mimeType,
          duration: result.duration,
          quality: result.quality,
          aspectRatio: result.metadata.aspectRatio,
          orientation: result.metadata.orientation,
          dominantColors: result.metadata.dominantColors,
        },
      }

      toast.success(`${isImage ? 'Image' : 'Video'} uploaded successfully!`)
      return uploadResult
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Upload failed'
      toast.error(errorMessage)
      throw error
    } finally {
      setIsUploading(false)
      setUploadProgress(null)
    }
  }

  const uploadMultipleMedia = async (
    files: File[]
  ): Promise<MediaUploadResult[]> => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated')
    }

    const results: MediaUploadResult[] = []
    const fileProgresses: Record<string, UploadProgress> = {}

    // Initialize batch progress
    setBatchProgress({
      totalFiles: files.length,
      completedFiles: 0,
      currentFile: files[0]?.name || '',
      overallProgress: 0,
      fileProgresses,
    })

    try {
      // Upload all files concurrently
      const uploadPromises = files.map(async (file, index) => {
        const fileId = `file-${index}-${Date.now()}`

        const onFileProgress = (progress: UploadProgress) => {
          fileProgresses[fileId] = progress

          // Calculate overall progress
          const totalProgress = Object.values(fileProgresses).reduce(
            (sum, p) => sum + p.progress,
            0
          )
          const overallProgress = Math.round(totalProgress / files.length)

          setBatchProgress(prev =>
            prev
              ? {
                  ...prev,
                  currentFile: file.name,
                  overallProgress,
                  fileProgresses: { ...fileProgresses },
                }
              : null
          )
        }

        try {
          const result = await uploadMedia(file, onFileProgress)

          // Mark file as complete
          setBatchProgress(prev =>
            prev
              ? {
                  ...prev,
                  completedFiles: prev.completedFiles + 1,
                }
              : null
          )

          return result
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error)
          throw error
        }
      })

      // Wait for all uploads to complete
      const uploadResults = await Promise.all(uploadPromises)
      results.push(...uploadResults)

      return results
    } finally {
      setBatchProgress(null)
    }
  }

  return {
    uploadMedia,
    uploadMultipleMedia,
    isUploading,
    uploadProgress,
    batchProgress,
  }
}
