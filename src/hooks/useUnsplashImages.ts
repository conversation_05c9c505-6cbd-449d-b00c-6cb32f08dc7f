import { useQuery } from '@tanstack/react-query'

export type UnsplashImage = {
  id: string
  urls: {
    raw: string
    full: string
    regular: string
    small: string
    thumb: string
  }
  user: {
    name: string
  }
  description?: string
  alt_description?: string
}

export function useUnsplashImages(
  query: string,
  enabled: boolean,
  orientation: string = 'landscape',
  page: number = 1
) {
  return useQuery({
    queryKey: ['unsplash-images', query, orientation, page],
    enabled: enabled && !!query,
    queryFn: async () => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        orientation,
        page: page.toString(),
      })

      const res = await fetch(`/api/media/unsplash/images?${params}`)
      if (!res.ok) throw new Error('Failed to fetch Unsplash images')
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}
