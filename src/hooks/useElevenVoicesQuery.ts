import { useQuery } from '@tanstack/react-query'

export interface ElevenVoice {
  voice_id: string
  name: string
  category?: string
  labels?: {
    accent?: string
    gender?: string
    age?: string
    description?: string
    descriptive?: string
    use_case?: string
    language?: string
  }
  description?: string
  preview_url?: string
}

export interface ElevenVoicesResponse {
  voices: ElevenVoice[]
  has_more?: boolean
  total_count?: number
  next_page_token?: string
}

export function useElevenVoicesQuery() {
  return useQuery<ElevenVoice[], Error>({
    queryKey: ['eleven-voices'],
    queryFn: async () => {
      const response = await fetch('/api/elevenlabs/voices')
      if (!response.ok) {
        const error = await response.text()
        throw new Error(`Failed to fetch voices: ${error}`)
      }
      const data = await response.json()
      return data.voices
    },
  })
}
