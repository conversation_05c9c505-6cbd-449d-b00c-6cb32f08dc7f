'use client'

import { useEffect, useState, useRef } from 'react'

/**
 * A more stable media query hook that handles browser inspector issues
 * This hook prevents layout thrashing when the browser inspector is opened/closed
 */
export function useStableMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastMatchesRef = useRef<boolean>(false)

  useEffect(() => {
    // Prevent SSR issues
    if (typeof window === 'undefined') return

    const media = window.matchMedia(query)

    // Initial check
    if (!isInitialized) {
      setMatches(media.matches)
      lastMatchesRef.current = media.matches
      setIsInitialized(true)
    }

    // Debounced listener to prevent rapid state changes
    const listener = () => {
      // Clear any pending timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // Only update if the value has actually changed and after a delay
      timeoutRef.current = setTimeout(() => {
        if (media.matches !== lastMatchesRef.current) {
          setMatches(media.matches)
          lastMatchesRef.current = media.matches
        }
      }, 200) // 200ms debounce to handle inspector opening/closing
    }

    media.addEventListener('change', listener)

    return () => {
      media.removeEventListener('change', listener)
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [query, isInitialized])

  return matches
}

/**
 * Hook to detect if browser inspector is likely open
 * This is a heuristic based on viewport changes
 */
export function useInspectorDetection() {
  const [isInspectorOpen, setIsInspectorOpen] = useState(false)
  const lastViewportRef = useRef({ width: 0, height: 0 })

  useEffect(() => {
    if (typeof window === 'undefined') return

    const checkInspector = () => {
      const currentWidth = window.innerWidth
      const currentHeight = window.innerHeight
      const lastViewport = lastViewportRef.current

      // If viewport changed significantly and quickly, inspector might be opening/closing
      if (lastViewport.width > 0 && lastViewport.height > 0) {
        const widthChange = Math.abs(currentWidth - lastViewport.width)
        const heightChange = Math.abs(currentHeight - lastViewport.height)

        // Heuristic: if width or height changed by more than 200px, inspector might be involved
        if (widthChange > 200 || heightChange > 200) {
          setIsInspectorOpen(true)
          // Reset after a delay
          setTimeout(() => setIsInspectorOpen(false), 1000)
        }
      }

      lastViewportRef.current = { width: currentWidth, height: currentHeight }
    }

    // Initial setup
    lastViewportRef.current = {
      width: window.innerWidth,
      height: window.innerHeight,
    }

    // Debounced resize listener
    let timeoutId: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(checkInspector, 100)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      clearTimeout(timeoutId)
    }
  }, [])

  return isInspectorOpen
}
