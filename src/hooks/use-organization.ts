import { useState, useEffect } from 'react'
import { authClient } from '@/lib/auth-client'
import { toast } from 'sonner'

export interface OrganizationMember {
  id: string
  userId: string
  organizationId: string
  role: string
  createdAt: Date
  user?: {
    id: string
    email: string
    name?: string
  }
}

export interface OrganizationInvitation {
  id: string
  organizationId: string
  email: string
  role: string
  status: string
  expiresAt: Date
  inviterId: string
  createdAt: Date
  inviter?: {
    id: string
    email: string
    name?: string
  }
}

export interface Organization {
  id: string
  name: string
  slug: string
  logo?: string
  metadata?: string
  createdAt: Date
}

export function useOrganization() {
  const [activeOrganization, setActiveOrganization] =
    useState<Organization | null>(null)
  const [members, setMembers] = useState<OrganizationMember[]>([])
  const [invitations, setInvitations] = useState<OrganizationInvitation[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  // Get active organization
  const { data: activeOrgData } = authClient.useActiveOrganization()

  useEffect(() => {
    if (activeOrgData) {
      // Convert Better Auth organization data to our interface
      const org: Organization = {
        id: activeOrgData.id,
        name: activeOrgData.name,
        slug: activeOrgData.slug,
        logo: activeOrgData.logo || undefined,
        metadata: activeOrgData.metadata,
        createdAt: activeOrgData.createdAt,
      }
      setActiveOrganization(org)
      loadOrganizationData()
    } else {
      setActiveOrganization(null)
      setMembers([])
      setInvitations([])
      setLoading(false)
    }
  }, [activeOrgData])

  // If no active organization but user might have one, try to set it
  useEffect(() => {
    if (!activeOrgData && !loading) {
      // Small delay to avoid too many requests
      const timer = setTimeout(() => {
        setActiveOrganizationIfNeeded()
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [activeOrgData, loading])

  const setActiveOrganizationIfNeeded = async () => {
    try {
      const response = await fetch('/api/organization/active', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.organization) {
          // The hook will automatically refresh when the active organization changes
          console.log('Active organization set successfully')
        }
      }
    } catch (error) {
      console.error('Failed to set active organization:', error)
    }
  }

  const loadOrganizationData = async () => {
    try {
      setLoading(true)

      // Load members
      const membersData = await authClient.organization.listMembers()

      if (membersData && 'members' in membersData) {
        const members: OrganizationMember[] = (
          membersData.members as unknown[]
        ).map((member: unknown) => {
          const memberData = member as Record<string, unknown>
          const userData = memberData.user as
            | Record<string, unknown>
            | undefined
          return {
            id: memberData.id as string,
            userId: memberData.userId as string,
            organizationId: memberData.organizationId as string,
            role: memberData.role as string,
            createdAt: memberData.createdAt as Date,
            user: userData
              ? {
                  id: userData.id as string,
                  email: userData.email as string,
                  name: (userData.name as string) || undefined,
                }
              : undefined,
          }
        })
        setMembers(members)
      } else {
        setMembers([])
      }

      // Load invitations
      const invitationsData = await authClient.organization.listInvitations()

      if (invitationsData && Array.isArray(invitationsData)) {
        const invitations: OrganizationInvitation[] = invitationsData.map(
          invitation => ({
            id: invitation.id,
            organizationId: invitation.organizationId,
            email: invitation.email,
            role: invitation.role,
            status: invitation.status,
            expiresAt: invitation.expiresAt,
            inviterId: invitation.inviterId,
            createdAt: invitation.createdAt,
          })
        )
        setInvitations(invitations)
      } else {
        setInvitations([])
      }
    } catch (error) {
      console.error('Failed to load organization data:', error)
      toast.error('Failed to load organization data')
    } finally {
      setLoading(false)
    }
  }

  const updateOrganization = async (data: { name?: string; slug?: string }) => {
    if (!activeOrganization) return

    try {
      setUpdating(true)
      await authClient.organization.update({
        organizationId: activeOrganization.id,
        data,
      })

      // Refresh organization data
      if (activeOrganization) {
        loadOrganizationData()
      }

      toast.success('Organization updated successfully')
    } catch (error) {
      console.error('Failed to update organization:', error)
      toast.error('Failed to update organization')
    } finally {
      setUpdating(false)
    }
  }

  const inviteMember = async (email: string, role: string = 'member') => {
    if (!activeOrganization) return

    try {
      setUpdating(true)
      await authClient.organization.inviteMember({
        email,
        role: role as 'member' | 'admin' | 'owner',
        organizationId: activeOrganization.id,
      })

      // Refresh invitations
      const invitationsData = await authClient.organization.listInvitations({
        query: {
          organizationId: activeOrganization.id,
        },
      })
      setInvitations(
        (invitationsData as unknown as OrganizationInvitation[]) || []
      )

      toast.success(`Invitation sent to ${email}`)
    } catch (error) {
      console.error('Failed to invite member:', error)
      toast.error('Failed to invite member')
    } finally {
      setUpdating(false)
    }
  }

  const removeMember = async (memberIdOrEmail: string) => {
    if (!activeOrganization) return

    try {
      setUpdating(true)
      await authClient.organization.removeMember({
        memberIdOrEmail,
        organizationId: activeOrganization.id,
      })

      // Refresh members
      const membersData = await authClient.organization.listMembers({
        query: {
          organizationId: activeOrganization.id,
        },
      })
      setMembers((membersData as unknown as OrganizationMember[]) || [])

      toast.success('Member removed successfully')
    } catch (error) {
      console.error('Failed to remove member:', error)
      toast.error('Failed to remove member')
    } finally {
      setUpdating(false)
    }
  }

  const updateMemberRole = async (memberId: string, role: string) => {
    if (!activeOrganization) return

    try {
      setUpdating(true)
      await authClient.organization.updateMemberRole({
        memberId,
        role: role as 'member' | 'admin' | 'owner',
        organizationId: activeOrganization.id,
      })

      // Refresh members
      const membersData = await authClient.organization.listMembers({
        query: {
          organizationId: activeOrganization.id,
        },
      })
      setMembers((membersData as unknown as OrganizationMember[]) || [])

      toast.success('Member role updated successfully')
    } catch (error) {
      console.error('Failed to update member role:', error)
      toast.error('Failed to update member role')
    } finally {
      setUpdating(false)
    }
  }

  const cancelInvitation = async (invitationId: string) => {
    if (!activeOrganization) return

    try {
      setUpdating(true)
      await authClient.organization.cancelInvitation({
        invitationId,
      })

      // Refresh invitations
      const invitationsData = await authClient.organization.listInvitations({
        query: {
          organizationId: activeOrganization.id,
        },
      })
      setInvitations(
        (invitationsData as unknown as OrganizationInvitation[]) || []
      )

      toast.success('Invitation cancelled successfully')
    } catch (error) {
      console.error('Failed to cancel invitation:', error)
      toast.error('Failed to cancel invitation')
    } finally {
      setUpdating(false)
    }
  }

  const resendInvitation = async (invitationId: string) => {
    if (!activeOrganization) return

    try {
      setUpdating(true)
      // For resending, we need to cancel the current invitation and create a new one
      const invitation = invitations.find(inv => inv.id === invitationId)
      if (!invitation) {
        toast.error('Invitation not found')
        return
      }

      await authClient.organization.cancelInvitation({
        invitationId,
      })

      await authClient.organization.inviteMember({
        email: invitation.email,
        role: invitation.role as 'member' | 'admin' | 'owner',
        organizationId: activeOrganization.id,
        resend: true,
      })

      // Refresh invitations
      const invitationsData = await authClient.organization.listInvitations({
        query: {
          organizationId: activeOrganization.id,
        },
      })
      setInvitations(
        (invitationsData as unknown as OrganizationInvitation[]) || []
      )

      toast.success('Invitation resent successfully')
    } catch (error) {
      console.error('Failed to resend invitation:', error)
      toast.error('Failed to resend invitation')
    } finally {
      setUpdating(false)
    }
  }

  return {
    activeOrganization,
    members,
    invitations,
    loading,
    updating,
    updateOrganization,
    inviteMember,
    removeMember,
    updateMemberRole,
    cancelInvitation,
    resendInvitation,
    refresh: () => activeOrganization && loadOrganizationData(),
  }
}
