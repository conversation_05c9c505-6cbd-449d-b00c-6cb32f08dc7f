'use client'

import { toast } from 'sonner'
import { authClient } from '@/lib/auth-client'

/**
 * Hook for handling workspace switching with full application refresh
 * This approach ensures complete data synchronization and eliminates timing issues
 */
export function useWorkspaceSwitch() {
  const switchWorkspace = async (organizationId: string) => {
    try {
      console.log('🔄 Switching to workspace:', organizationId)

      // Validate organizationId
      if (!organizationId || typeof organizationId !== 'string') {
        throw new Error('Invalid organization ID')
      }

      // Show loading toast to inform user
      toast.loading('Switching workspace...', { id: 'workspace-switch' })

      // Step 1: Switch the active organization using better-auth
      await authClient.organization.setActive({
        organizationId,
      })

      console.log('✅ Workspace switched, refreshing application...')

      // Step 2: Wait a brief moment for the session to be updated on the server
      // This ensures the session is fully updated before the page refresh
      await new Promise(resolve => setTimeout(resolve, 300))

      // Step 3: Perform full application refresh
      // This is the most reliable way to ensure all workspace-dependent data is refreshed
      // and eliminates any timing issues with cache invalidation
      console.log('🔄 Performing full application refresh...')

      // Dismiss the loading toast before refresh
      toast.dismiss('workspace-switch')

      // Use window.location.reload() for complete application refresh
      window.location.reload()

      return { success: true }
    } catch (error) {
      console.error('❌ Failed to switch workspace:', error)

      // Dismiss loading toast and show error
      toast.dismiss('workspace-switch')
      toast.error('Failed to switch workspace. Please try again.')

      return { success: false, error }
    }
  }

  /**
   * Force refresh workspace data without switching
   * Useful for refreshing data after workspace-related changes
   */
  const refreshWorkspaceData = () => {
    console.log('🔄 Refreshing workspace data with full page reload...')
    toast.loading('Refreshing workspace data...', { id: 'workspace-refresh' })

    // Small delay to show the toast, then refresh
    setTimeout(() => {
      window.location.reload()
    }, 500)
  }

  return {
    switchWorkspace,
    refreshWorkspaceData,
  }
}
