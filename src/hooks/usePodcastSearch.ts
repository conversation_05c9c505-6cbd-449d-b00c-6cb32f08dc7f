import { useQuery, useInfiniteQuery } from '@tanstack/react-query'

export type Podcast = {
  id: number
  title: string
  author: string
  description: string
  image: string
  url: string
  link: string
  episodeCount: number
  language: string
  explicit: boolean
  lastUpdateTime: number
  categories: Record<string, string>
}

export type PodcastSearchResponse = {
  status: string
  podcasts: Podcast[]
  count: number
  query: string
}

export function usePodcastSearch(
  query: string,
  enabled: boolean = true,
  max: number = 20
) {
  return useQuery({
    queryKey: ['podcast-search', query, max],
    enabled: enabled && !!query && query.trim().length > 0,
    queryFn: async (): Promise<PodcastSearchResponse> => {
      const params = new URLSearchParams({
        q: query.trim(),
        max: max.toString(),
        clean: 'true',
      })

      const res = await fetch(`/api/podcast/search?${params}`)
      if (!res.ok) {
        const error = await res.text()
        throw new Error(error || 'Failed to search podcasts')
      }
      return res.json()
    },
    // staleTime and retry now handled by global config
  })
}

// Infinite loading version for podcast search
export function useInfinitePodcastSearch(
  query: string,
  enabled: boolean = true,
  pageSize: number = 20
) {
  return useInfiniteQuery({
    queryKey: ['podcast-search-infinite', query, pageSize],
    enabled: enabled && !!query && query.trim().length > 0,
    queryFn: async ({ pageParam = 0 }): Promise<PodcastSearchResponse> => {
      const params = new URLSearchParams({
        q: query.trim(),
        max: pageSize.toString(),
        start: (pageParam * pageSize).toString(),
        clean: 'true',
      })

      const res = await fetch(`/api/podcast/search?${params}`)
      if (!res.ok) {
        const error = await res.text()
        throw new Error(error || 'Failed to search podcasts')
      }
      return res.json()
    },
    getNextPageParam: (lastPage, allPages) => {
      // If the last page has fewer results than pageSize, we've reached the end
      if (lastPage.podcasts.length < pageSize) {
        return undefined
      }
      // Return the next page number
      return allPages.length
    },
    initialPageParam: 0,
  })
}
