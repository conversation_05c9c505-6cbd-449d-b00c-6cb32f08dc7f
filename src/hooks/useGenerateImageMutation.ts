import { useMutation } from '@tanstack/react-query'

export interface GenerateImageParams {
  prompt: string
  model?: string
  orientation?: string
  style?: string
}

export interface GenerateImageResult {
  imageUrl: string
}

export function useGenerateImageMutation() {
  return useMutation<GenerateImageResult, Error, GenerateImageParams>({
    mutationFn: async params => {
      const response = await fetch('/api/media/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(`Image generation failed: ${error}`)
      }

      return response.json()
    },
  })
}
