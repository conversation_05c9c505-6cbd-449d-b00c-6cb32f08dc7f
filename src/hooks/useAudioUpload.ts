import { useState } from 'react'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import { toast } from '@/lib/toast'

export interface AudioUploadResult {
  id: string
  originalUrl: string
  metadata: {
    fileSize: number
    mimeType: string
    duration?: number
  }
}

export interface AudioUploadProgress {
  stage: 'analyzing' | 'uploading' | 'processing' | 'complete'
  progress: number
  message: string
}

export interface AudioBatchUploadProgress {
  totalFiles: number
  completedFiles: number
  currentFile: string
  overallProgress: number
  fileProgresses: Record<string, AudioUploadProgress>
}

export function useAudioUpload() {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] =
    useState<AudioUploadProgress | null>(null)
  const [batchProgress, setBatchProgress] =
    useState<AudioBatchUploadProgress | null>(null)

  const uploadAudio = async (
    file: File,
    onProgress?: (progress: AudioUploadProgress) => void
  ): Promise<AudioUploadResult> => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated')
    }

    setIsUploading(true)
    const updateProgress = (progress: AudioUploadProgress) => {
      setUploadProgress(progress)
      onProgress?.(progress)
    }

    updateProgress({
      stage: 'analyzing',
      progress: 10,
      message: 'Processing audio file...',
    })

    try {
      const isAudio = file.type.startsWith('audio/')

      if (!isAudio) {
        throw new Error('File must be an audio file')
      }

      // Extract basic metadata
      const metadata = {
        fileSize: file.size,
        mimeType: file.type,
        duration: undefined as number | undefined,
      }

      // Try to get duration from audio file
      try {
        const audio = new Audio()
        const durationPromise = new Promise<number>((resolve, reject) => {
          audio.addEventListener('loadedmetadata', () => {
            resolve(audio.duration)
          })
          audio.addEventListener('error', () => {
            reject(new Error('Could not load audio metadata'))
          })
          audio.src = URL.createObjectURL(file)
        })

        metadata.duration = await durationPromise
      } catch (error) {
        console.warn('Could not extract audio duration:', error)
        // Continue without duration
      }

      updateProgress({
        stage: 'uploading',
        progress: 30,
        message: 'Initiating upload...',
      })

      // Initiate upload (signed URL)
      const initiateResponse = await fetch('/api/media/initiate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          metadata,
          includeThumbnail: false,
        }),
      })

      if (!initiateResponse.ok) {
        let errorMessage = 'Upload initiation failed'
        try {
          const errorData = await initiateResponse.json()
          errorMessage = errorData.error || errorMessage
        } catch {
          // If JSON parsing fails, use status text
          errorMessage = initiateResponse.statusText || errorMessage
        }
        throw new Error(errorMessage)
      }

      let initData: {
        id: string
        bucket: string
        filePath: string
        uploadUrl: string
        uploadToken: string
      }

      try {
        initData = await initiateResponse.json()
      } catch {
        throw new Error('Failed to parse upload initiation response')
      }

      const uploadId = initData.id

      updateProgress({
        stage: 'uploading',
        progress: 60,
        message: 'Uploading to storage...',
      })

      // Upload directly
      const putHeaders = {
        'x-upsert': 'false',
        Authorization: `Bearer ${initData.uploadToken}`,
        'Content-Type': file.type,
      }
      const uploadResp = await fetch(initData.uploadUrl, {
        method: 'PUT',
        headers: putHeaders as unknown as HeadersInit,
        body: file,
      })
      if (!uploadResp.ok) {
        throw new Error('Failed to upload audio to storage')
      }

      updateProgress({
        stage: 'processing',
        progress: 90,
        message: 'Finalizing upload...',
      })

      const completeResp = await fetch('/api/media/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: uploadId,
          bucket: initData.bucket,
          filePath: initData.filePath,
          thumbnailPath: null,
          fileSize: file.size,
          thumbnailSize: null,
          clientMetadata: {},
        }),
      })

      if (!completeResp.ok) {
        let errorMessage = 'Failed to finalize upload'
        try {
          const errorData = await completeResp.json()
          errorMessage = errorData.error || errorMessage
        } catch {
          errorMessage = completeResp.statusText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const result = await completeResp.json()

      updateProgress({
        stage: 'complete',
        progress: 100,
        message: 'Upload complete!',
      })

      const uploadResult: AudioUploadResult = {
        id: result.id,
        originalUrl: result.url,
        metadata: {
          fileSize: result.file_size,
          mimeType: result.metadata.mimeType,
          duration: result.duration,
        },
      }

      toast.success('Audio uploaded successfully!')
      return uploadResult
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Upload failed'
      toast.error(errorMessage)
      throw error
    } finally {
      setIsUploading(false)
      setUploadProgress(null)
    }
  }

  const uploadMultipleAudio = async (
    files: File[]
  ): Promise<AudioUploadResult[]> => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated')
    }

    setIsUploading(true)
    const results: AudioUploadResult[] = []
    const totalFiles = files.length

    setBatchProgress({
      totalFiles,
      completedFiles: 0,
      currentFile: '',
      overallProgress: 0,
      fileProgresses: {},
    })

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const fileId = `file-${i}`

        setBatchProgress(prev =>
          prev
            ? {
                ...prev,
                currentFile: file.name,
              }
            : null
        )

        const onFileProgress = (progress: AudioUploadProgress) => {
          setBatchProgress(prev =>
            prev
              ? {
                  ...prev,
                  fileProgresses: {
                    ...prev.fileProgresses,
                    [fileId]: progress,
                  },
                  overallProgress: Math.round(
                    ((i + progress.progress / 100) / totalFiles) * 100
                  ),
                }
              : null
          )
        }

        try {
          const result = await uploadAudio(file, onFileProgress)
          results.push(result)

          setBatchProgress(prev =>
            prev
              ? {
                  ...prev,
                  completedFiles: prev.completedFiles + 1,
                  overallProgress: Math.round(((i + 1) / totalFiles) * 100),
                }
              : null
          )
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error)
          // Continue with other files
        }
      }

      return results
    } finally {
      setIsUploading(false)
      setBatchProgress(null)
    }
  }

  return {
    uploadAudio,
    uploadMultipleAudio,
    isUploading,
    uploadProgress,
    batchProgress,
  }
}
