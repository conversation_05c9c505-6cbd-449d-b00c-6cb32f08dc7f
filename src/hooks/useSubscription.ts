import { useState, useCallback } from 'react'
import { authClient } from '@/lib/auth-client'
import { toast } from 'sonner'
import { useQuery } from '@tanstack/react-query'

// Simple hook to get user's role in active organization
export function useUserRole() {
  const { data: session } = authClient.useSession()
  const organizationId = session?.session?.activeOrganizationId

  return useQuery({
    queryKey: ['user-role', organizationId],
    queryFn: async () => {
      if (!organizationId) return null

      const response = await fetch('/api/organization/active')
      if (response.ok) {
        const data = await response.json()
        return data.organization?.role || 'member'
      }
      return 'member'
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export interface SubscriptionData {
  id: string
  status: string
  plan: string
  referenceId: string
  limits?: Record<string, number>
  periodStart?: string | Date
  periodEnd?: string | Date
  cancelAtPeriodEnd?: boolean
}

export function useSubscription() {
  const [isLoading, setIsLoading] = useState(false)
  const { data: session } = authClient.useSession()
  const { data: userRole } = useUserRole()
  // const { data: activeOrg } = authClient.useActiveOrganization()

  // Simple function to check if user can upgrade subscription
  const canUpgradeSubscription = async (): Promise<{
    canUpgrade: boolean
    error?: string
  }> => {
    // If no organization ID, this is a user-level subscription - allow upgrade
    if (!session?.session?.activeOrganizationId) {
      return { canUpgrade: true }
    }

    // Only owners can upgrade subscriptions
    if (userRole !== 'owner') {
      return {
        canUpgrade: false,
        error:
          "As you are a member of this organization, you don't have permission to change the plan. Only owners can change the plan.",
      }
    }

    return { canUpgrade: true }
  }

  const upgradeSubscription = async ({
    plan,
    annual = false,
    successUrl = '/billing/subscription',
    cancelUrl = '/billing/plans',
  }: {
    plan: 'basic' | 'premium'
    annual?: boolean
    successUrl?: string
    cancelUrl?: string
  }) => {
    // Check permissions before attempting upgrade
    const permissionCheck = await canUpgradeSubscription()
    if (!permissionCheck.canUpgrade) {
      toast.error(
        permissionCheck.error ||
          "You don't have permission to upgrade the subscription"
      )
      return
    }

    // Convert plan name to include billing period
    const planName = annual ? `${plan}-annual` : `${plan}-monthly`
    setIsLoading(true)

    try {
      // Get organization ID from session, with fallback to API call
      let organizationId = session?.session?.activeOrganizationId

      // If no organization ID in session, try to fetch it from the API
      if (!organizationId && session?.user?.id) {
        try {
          const response = await fetch('/api/organization/active')
          if (response.ok) {
            const data = await response.json()
            organizationId = data.organization?.id || null
            console.log('Fetched organization ID from API:', organizationId)
          }
        } catch (error) {
          console.warn('Failed to fetch active organization from API:', error)
        }
      }

      // Use organization ID if available, otherwise use user ID
      const referenceId = organizationId || session?.user?.id

      const metadata = {
        email: session?.user?.email,
        userId: session?.user?.id,
        organizationId: organizationId,
      }

      if (!referenceId) {
        toast.error('No valid reference ID found for subscription upgrade')
        return { error: new Error('No valid reference ID') }
      }

      console.log('Subscription upgrade metadata:', metadata)

      const { data, error } = await authClient.subscription.upgrade({
        plan: planName,
        annual,
        referenceId,
        metadata,
        successUrl,
        cancelUrl,
        disableRedirect: false, // Allow redirect to Stripe checkout
      })

      if (error) {
        toast.error(error.message || 'Failed to upgrade subscription')
        return { error }
      }

      toast.success('Redirecting to checkout...')
      return { data }
    } catch (error) {
      console.error('Subscription upgrade error:', error)
      toast.error('An unexpected error occurred')
      return { error: error as Error }
    } finally {
      setIsLoading(false)
    }
  }

  const cancelSubscription = async ({
    subscriptionId,
    returnUrl = '/billing',
  }: {
    subscriptionId: string
    returnUrl?: string
  }) => {
    setIsLoading(true)

    try {
      // Use organization ID if available, otherwise use user ID
      const referenceId =
        session?.session?.activeOrganizationId || session?.user?.id

      if (!referenceId) {
        toast.error('No valid reference ID found for subscription cancellation')
        return { error: new Error('No valid reference ID') }
      }

      const { data, error } = await authClient.subscription.cancel({
        referenceId,
        subscriptionId,
        returnUrl,
      })

      if (error) {
        toast.error(error.message || 'Failed to cancel subscription')
        return { error }
      }

      toast.success('Redirecting to billing portal...')
      return { data }
    } catch (error) {
      console.error('Subscription cancellation error:', error)
      toast.error('An unexpected error occurred')
      return { error: error as Error }
    } finally {
      setIsLoading(false)
    }
  }

  const restoreSubscription = async ({
    subscriptionId,
  }: {
    subscriptionId: string
  }) => {
    setIsLoading(true)

    try {
      // Use organization ID if available, otherwise use user ID
      const referenceId =
        session?.session?.activeOrganizationId || session?.user?.id

      if (!referenceId) {
        toast.error('No valid reference ID found for subscription restoration')
        return { error: new Error('No valid reference ID') }
      }

      const { data, error } = await authClient.subscription.restore({
        referenceId,
        subscriptionId,
      })

      if (error) {
        toast.error(error.message || 'Failed to restore subscription')
        return { error }
      }

      toast.success('Subscription restored successfully')
      return { data }
    } catch (error) {
      console.error('Subscription restoration error:', error)
      toast.error('An unexpected error occurred')
      return { error: error as Error }
    } finally {
      setIsLoading(false)
    }
  }

  const getSubscriptions = useCallback(async () => {
    try {
      // Use organization ID if available, otherwise use user ID
      const referenceId =
        session?.session?.activeOrganizationId || session?.user?.id

      // Only proceed if we have a valid referenceId
      if (!referenceId) {
        console.error('No valid referenceId found for subscription query')
        return { subscriptions: [], error: new Error('No valid reference ID') }
      }

      const { data: subscriptions, error } = await authClient.subscription.list(
        {
          query: {
            referenceId,
          },
        }
      )

      if (error) {
        console.error('Failed to fetch subscriptions:', error)
        return { subscriptions: [], error }
      }

      return { subscriptions: subscriptions || [], error: null }
    } catch (error) {
      console.error('Subscription fetch error:', error)
      return { subscriptions: [], error: error as Error }
    }
  }, [session?.session?.activeOrganizationId, session?.user?.id])

  const getActiveSubscription =
    useCallback(async (): Promise<SubscriptionData | null> => {
      const { subscriptions, error } = await getSubscriptions()

      if (error || !subscriptions.length) {
        return null
      }

      // Find active or trialing subscription
      const activeSubscription = subscriptions.find(
        sub => sub.status === 'active' || sub.status === 'trialing'
      )

      return (activeSubscription as SubscriptionData) || null
    }, [getSubscriptions])

  return {
    isLoading,
    upgradeSubscription,
    cancelSubscription,
    restoreSubscription,
    canUpgradeSubscription,
    getSubscriptions,
    getActiveSubscription,
    // activeOrg,
  }
}

// Cached hook for getting subscriptions
export function useCachedSubscriptions() {
  const { data: session } = authClient.useSession()
  const referenceId =
    session?.session?.activeOrganizationId || session?.user?.id

  return useQuery({
    queryKey: ['subscriptions', referenceId],
    queryFn: async () => {
      const { data: subscriptions, error } = await authClient.subscription.list(
        {
          query: {
            referenceId,
          },
        }
      )

      if (error) {
        throw new Error(error.message || 'Failed to fetch subscriptions')
      }

      return subscriptions || []
    },
    enabled: !!referenceId,
    staleTime: 30 * 1000, // 30 seconds for more responsive plan updates
    refetchOnWindowFocus: true, // Refetch when user returns after upgrade
  })
}

// Cached hook for getting active subscription
export function useCachedActiveSubscription() {
  const {
    data: subscriptions,
    isLoading,
    error,
    refetch,
  } = useCachedSubscriptions()

  const activeSubscription = subscriptions?.find(
    (sub: SubscriptionData) =>
      sub.status === 'active' || sub.status === 'trialing'
  )

  return {
    subscription: activeSubscription || null,
    isLoading,
    error,
    refetch,
  }
}
