import { useQuery } from '@tanstack/react-query'

export interface UsageData {
  plan: {
    type: string
    status: string
    periodStart: string
    periodEnd: string
  }
  limits: {
    projects: number
    videoExports: number
    aiImages: number
    teamMembers: number
    storage: number
  }
  usage: {
    projects: {
      used: number
      max: number
      percentage: number
    }
    videoExports: {
      used: number
      max: number
      percentage: number
    }
    aiImages: {
      used: number
      max: number
      percentage: number
    }
    teamMembers: {
      used: number
      max: number
      percentage: number
    }
    storage: {
      used: number
      max: number
      percentage: number
    }
  }
}

export const useUsage = () => {
  return useQuery<UsageData>({
    queryKey: ['usage'],
    queryFn: async () => {
      const response = await fetch('/api/usage')
      if (!response.ok) {
        throw new Error('Failed to fetch usage data')
      }
      return response.json()
    },
    staleTime: 30 * 1000, // 30 seconds for more responsive usage updates
    refetchOnWindowFocus: true, // Refetch when user returns after changes
  })
}
