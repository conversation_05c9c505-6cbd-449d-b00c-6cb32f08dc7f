/**
 * Gated Voice Generation Hook
 *
 * Wrapper around voice generation that checks limits and shows upgrade modals
 */

import {
  useGenerateSpeechMutation,
  GenerateSpeechParams,
} from '@/hooks/useGenerateSpeechMutation'
import { useVoiceRegeneration } from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'
import { useVideoStore } from '@/store/video-store'

export function useGatedVoiceGeneration(projectId: string) {
  const generateSpeech = useGenerateSpeechMutation()
  const voiceRegeneration = useVoiceRegeneration(projectId)
  const { openUpgradeModal } = useUpgradeModal()
  const { incrementVoiceRegenerations, project } = useVideoStore()

  // Custom save function that uses fresh store data
  const saveWithLatestData = async () => {
    // Get the latest project state
    const latestState = useVideoStore.getState()
    const currentProject = latestState.project

    if (!currentProject) {
      throw new Error('No project data available')
    }

    console.log('🔍 Saving with fresh data:', currentProject.voiceRegenerations)

    // Make API call with fresh data
    const response = await fetch(`/api/projects/${projectId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectName: currentProject.projectName,
        orientation: currentProject.orientation,
        coverColor: currentProject.coverColor,
        coverPic: currentProject.coverPic,
        duration: currentProject.duration,
        summary: currentProject.summary,
        voiceRegenerations: currentProject.voiceRegenerations || 0,
        music: currentProject.music,
        speech: currentProject.speech,
        backgroundVideo: currentProject.backgroundVideo,
        captionSettings: currentProject.captionSettings,
        scenes: currentProject.scenes,
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to save project')
    }

    console.log(
      '✅ Project saved with voiceRegenerations:',
      currentProject.voiceRegenerations
    )
  }

  // Check limits using store value for immediate UI feedback
  const checkLimitsWithStore = () => {
    const currentAttempts = project?.voiceRegenerations || 0
    const limit = voiceRegeneration.limit

    return {
      allowed: currentAttempts < limit,
      attempts: currentAttempts,
      limit,
      remaining: Math.max(0, limit - currentAttempts),
      reason:
        currentAttempts >= limit
          ? `You've reached your limit of ${limit} voice regenerations per project`
          : '',
      upgradeRequired: currentAttempts >= limit,
    }
  }

  const generateVoiceWithLimits = async (
    params: Omit<GenerateSpeechParams, 'projectId'>,
    skipSave: boolean = false
  ) => {
    // Check limits using store value for immediate feedback
    const limits = checkLimitsWithStore()

    if (!limits.allowed) {
      // Show upgrade modal instead of generating
      openUpgradeModal('voiceRegenerations', limits.reason)
      return null
    }

    try {
      // Generate voice with projectId for tracking
      const result = await generateSpeech.mutateAsync({
        ...params,
        projectId,
      })

      // Increment voice regenerations in the store immediately
      incrementVoiceRegenerations()

      // Only save project if not skipped (for batch operations)
      if (!skipSave) {
        try {
          await saveWithLatestData()
          // Refetch voice regeneration data to sync with API after save
          voiceRegeneration.refetch()
        } catch (error) {
          console.error(
            'Failed to save project with updated voice regeneration count:',
            error
          )
        }
      }

      return result
    } catch (error) {
      console.error('Voice generation error:', error)
      throw error
    }
  }

  // Batch generation function for "Apply to all scenes"
  const generateVoiceBatch = async (
    generationTasks: Array<Omit<GenerateSpeechParams, 'projectId'>>
  ) => {
    const results = []

    for (let i = 0; i < generationTasks.length; i++) {
      const task = generationTasks[i]

      // Check limits before each generation using current store state
      const limits = checkLimitsWithStore()
      if (!limits.allowed) {
        // Show upgrade modal and stop batch
        openUpgradeModal('voiceRegenerations', limits.reason)
        break
      }

      try {
        // Generate voice with projectId for tracking
        const result = await generateSpeech.mutateAsync({
          ...task,
          projectId,
        })

        // Increment voice regenerations in the store immediately
        incrementVoiceRegenerations()

        results.push(result)
      } catch (error) {
        console.error('Voice generation error in batch:', error)
        // Continue with next generation on error
        continue
      }
    }

    // Save project once at the end if we completed any generations
    if (results.length > 0) {
      try {
        await saveWithLatestData()
        // Refetch voice regeneration data to sync with API after save
        voiceRegeneration.refetch()
      } catch (error) {
        console.error(
          'Failed to save project after batch voice generation:',
          error
        )
      }
    }

    return results
  }

  return {
    generateVoice: generateVoiceWithLimits,
    generateVoiceBatch,
    isGenerating: generateSpeech.isPending,
    error: generateSpeech.error,
    voiceRegeneration: {
      ...voiceRegeneration,
      // Override with store-based values for immediate UI feedback
      ...checkLimitsWithStore(),
    },
    // Expose the raw mutation for cases where limits are already checked
    rawMutation: generateSpeech,
  }
}
