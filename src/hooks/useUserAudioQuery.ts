import { useQuery } from '@tanstack/react-query'
import { MusicTrack } from '@/app/api/music/route'

interface UserAudioQueryParams {
  page?: number
  limit?: number
}

interface UserAudioResponse {
  data: MusicTrack[]
  count: number
  page: number
  limit: number
  totalPages: number
}

export function useUserAudioQuery(params: UserAudioQueryParams = {}) {
  const { page = 1, limit = 10 } = params

  return useQuery({
    queryKey: ['user-audio', page, limit],
    queryFn: async (): Promise<UserAudioResponse> => {
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      })

      const response = await fetch(`/api/media/audio?${searchParams}`)

      if (!response.ok) {
        throw new Error('Failed to fetch user audio files')
      }

      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}
