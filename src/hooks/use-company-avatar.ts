import { useState, useEffect } from 'react'
import {
  getEmailDomain,
  getCompanyLogoUrl,
  getFallbackLogoUrl,
} from '@/lib/email-utils'

export function useCompanyAvatar(email: string) {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [useFallback, setUseFallback] = useState(false)

  useEffect(() => {
    const domain = getEmailDomain(email)
    if (!domain) {
      setAvatarUrl(null)
      return
    }

    setIsLoading(true)
    setUseFallback(false)

    // Try Google S2 Favicon API first
    const googleUrl = getCompanyLogoUrl(domain)

    const img = new Image()
    img.onload = () => {
      setAvatarUrl(googleUrl)
      setIsLoading(false)
    }

    img.onerror = () => {
      // If Google fails, try DuckDuckGo fallback
      const fallbackUrl = getFallbackLogoUrl(domain)
      const fallbackImg = new Image()

      fallbackImg.onload = () => {
        setAvatarUrl(fallbackUrl)
        setUseFallback(true)
        setIsLoading(false)
      }

      fallbackImg.onerror = () => {
        // If both fail, use null (will show initials)
        setAvatarUrl(null)
        setIsLoading(false)
      }

      fallbackImg.src = fallbackUrl
    }

    img.src = googleUrl
  }, [email])

  return { avatarUrl, isLoading, useFallback }
}
