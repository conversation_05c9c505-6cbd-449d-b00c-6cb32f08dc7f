'use client'

import { useEffect, useState } from 'react'

/**
 * Custom hook for media queries
 * @param query CSS media query string
 * @returns boolean indicating if the query matches
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    // Prevent SSR issues and ensure we're in the browser
    if (typeof window === 'undefined') return

    const media = window.matchMedia(query)

    // Initial check - only set if not initialized to prevent unnecessary re-renders
    if (!isInitialized) {
      setMatches(media.matches)
      setIsInitialized(true)
    }

    // Add listener for changes with debouncing to prevent rapid state changes
    let timeoutId: NodeJS.Timeout
    const listener = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        setMatches(media.matches)
      }, 100) // 100ms debounce
    }

    media.addEventListener('change', listener)

    return () => {
      media.removeEventListener('change', listener)
      clearTimeout(timeoutId)
    }
  }, [query, isInitialized])

  return matches
}
