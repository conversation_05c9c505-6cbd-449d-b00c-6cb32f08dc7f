import { useQuery } from '@tanstack/react-query'

export type PexelsVideo = {
  id: number
  user?: { name?: string }
  image: string
  duration: number
  video_files: {
    link: string
    quality: string
    width: number
    height: number
  }[]
}

export type PexelsImage = {
  id: number
  photographer?: string
  src: {
    original: string
    large: string
    medium: string
    small: string
    portrait: string
    landscape: string
    tiny: string
  }
  alt?: string
}

export function usePexelsVideos(
  query: string,
  enabled: boolean,
  orientation?: 'landscape' | 'portrait' | 'square',
  page: number = 1
) {
  return useQuery({
    queryKey: ['pexels-videos', query, orientation, page],
    enabled: enabled && !!query,
    queryFn: async () => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        page: page.toString(),
      })

      if (orientation) {
        params.append('orientation', orientation)
      }

      const res = await fetch(`/api/media/pexels/videos?${params}`)
      if (!res.ok) throw new Error('Failed to fetch Pexels videos')
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}

export function usePexelsImages(
  query: string,
  enabled: boolean,
  orientation?: 'landscape' | 'portrait' | 'square',
  page: number = 1
) {
  return useQuery({
    queryKey: ['pexels-images', query, orientation, page],
    enabled: enabled && !!query,
    queryFn: async () => {
      const params = new URLSearchParams({
        query,
        per_page: '24',
        page: page.toString(),
      })

      if (orientation) {
        params.append('orientation', orientation)
      }

      const res = await fetch(`/api/media/pexels/images?${params}`)
      if (!res.ok) throw new Error('Failed to fetch Pexels images')
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}
