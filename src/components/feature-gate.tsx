'use client'

import React from 'react'
import { GatedFeature } from '@/lib/feature-gating'
import {
  useFeatureAccess,
  useVoiceRegeneration,
} from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'
import { PremiumBadge, PremiumButton } from '@/components/ui/premium-badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface FeatureGateProps {
  feature: GatedFeature
  children: React.ReactNode
  fallback?: React.ReactNode
  showUpgradeButton?: boolean
  upgradeButtonText?: string
  projectId?: string // For voice regeneration tracking
}

/**
 * FeatureGate component
 *
 * Wraps content that should be gated based on subscription limits.
 * Shows upgrade UI when feature is not allowed.
 */
export function FeatureGate({
  feature,
  children,
  fallback,
  showUpgradeButton = true,
  upgradeButtonText = 'Upgrade',
  projectId,
}: FeatureGateProps) {
  // Use voice regeneration hook for voice regenerations with projectId
  const voiceRegeneration = useVoiceRegeneration(
    feature === 'voiceRegenerations' && projectId ? projectId : ''
  )

  // Use standard feature access for all other features
  const standardFeatureAccess = useFeatureAccess(feature)

  // Determine which access data to use
  const featureAccess =
    feature === 'voiceRegenerations' && projectId
      ? {
          allowed: voiceRegeneration.allowed,
          reason: voiceRegeneration.reason,
          current: voiceRegeneration.attempts,
          limit: voiceRegeneration.limit,
          planName: voiceRegeneration.planName,
          upgradeRequired: voiceRegeneration.upgradeRequired,
          isLoading: false, // Voice regeneration hook doesn't have isLoading
        }
      : standardFeatureAccess

  const { openUpgradeModal } = useUpgradeModal()

  if (featureAccess.isLoading) {
    return <div className='animate-pulse bg-muted rounded h-8 w-24' />
  }

  if (featureAccess.allowed) {
    return <>{children}</>
  }

  if (fallback) {
    return <>{fallback}</>
  }

  return (
    <div className='flex items-center gap-2'>
      <PremiumBadge variant='gem' showText text='Premium' />
      {showUpgradeButton && (
        <PremiumButton
          onClick={() => openUpgradeModal(feature, featureAccess.reason)}
          size='sm'
        >
          {upgradeButtonText}
        </PremiumButton>
      )}
    </div>
  )
}

interface GatedButtonProps {
  feature: GatedFeature
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  className?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  projectId?: string
  premiumText?: string
}

/**
 * GatedButton component
 *
 * A button that automatically shows premium styling and opens upgrade modal
 * when the feature is not allowed.
 */
export function GatedButton({
  feature,
  children,
  onClick,
  disabled = false,
  className,
  variant = 'default',
  size = 'default',
  projectId,
  premiumText = 'Upgrade Required',
}: GatedButtonProps) {
  // Use voice regeneration hook for voice regenerations with projectId
  const voiceRegeneration = useVoiceRegeneration(
    feature === 'voiceRegenerations' && projectId ? projectId : ''
  )

  // Use standard feature access for all other features
  const standardFeatureAccess = useFeatureAccess(feature)

  // Determine which access data to use
  const featureAccess =
    feature === 'voiceRegenerations' && projectId
      ? {
          allowed: voiceRegeneration.allowed,
          reason: voiceRegeneration.reason,
          current: voiceRegeneration.attempts,
          limit: voiceRegeneration.limit,
          planName: voiceRegeneration.planName,
          upgradeRequired: voiceRegeneration.upgradeRequired,
          isLoading: false, // Voice regeneration hook doesn't have isLoading
        }
      : standardFeatureAccess

  const { openUpgradeModal } = useUpgradeModal()

  const handleClick = () => {
    if (!featureAccess.allowed) {
      openUpgradeModal(feature, featureAccess.reason)
      return
    }
    onClick?.()
  }

  const isDisabled = disabled || featureAccess.isLoading

  if (featureAccess.isLoading) {
    return (
      <Button disabled className={className} variant={variant} size={size}>
        <div className='animate-pulse'>Loading...</div>
      </Button>
    )
  }

  if (!featureAccess.allowed) {
    // Map Button sizes to PremiumButton sizes
    const premiumSize =
      size === 'default' ? 'md' : size === 'icon' ? 'sm' : size

    return (
      <PremiumButton
        onClick={handleClick}
        disabled={isDisabled}
        className={className}
        size={premiumSize as 'sm' | 'md' | 'lg'}
      >
        {premiumText}
      </PremiumButton>
    )
  }

  return (
    <Button
      onClick={handleClick}
      disabled={isDisabled}
      className={className}
      variant={variant}
      size={size}
    >
      {children}
    </Button>
  )
}

interface UsageBadgeProps {
  feature: GatedFeature
  projectId?: string
  showPercentage?: boolean
  className?: string
}

/**
 * UsageBadge component
 *
 * Shows current usage and limits for a feature
 */
export function UsageBadge({
  feature,
  projectId,
  showPercentage = false,
  className,
}: UsageBadgeProps) {
  // Use voice regeneration hook for voice regenerations with projectId
  const voiceRegeneration = useVoiceRegeneration(
    feature === 'voiceRegenerations' && projectId ? projectId : ''
  )

  // Use standard feature access for all other features
  const standardFeatureAccess = useFeatureAccess(feature)

  // Determine which access data to use
  const featureAccess =
    feature === 'voiceRegenerations' && projectId
      ? {
          allowed: voiceRegeneration.allowed,
          reason: voiceRegeneration.reason,
          current: voiceRegeneration.attempts,
          limit: voiceRegeneration.limit,
          planName: voiceRegeneration.planName,
          upgradeRequired: voiceRegeneration.upgradeRequired,
          isLoading: false, // Voice regeneration hook doesn't have isLoading
        }
      : standardFeatureAccess

  if (
    featureAccess.isLoading ||
    !featureAccess.current ||
    !featureAccess.limit
  ) {
    return null
  }

  const percentage = Math.round(
    (featureAccess.current / featureAccess.limit) * 100
  )
  const isNearLimit = percentage >= 80
  const isAtLimit = percentage >= 100 || featureAccess.limit === 0

  return (
    <div
      className={cn(
        'inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium',
        isAtLimit
          ? 'bg-amber-50 text-amber-700 border border-amber-200 dark:bg-amber-950/20 dark:text-amber-400 dark:border-amber-800'
          : isNearLimit
            ? 'bg-orange-50 text-orange-700 border border-orange-200 dark:bg-orange-950/20 dark:text-orange-400 dark:border-orange-800'
            : 'bg-muted text-muted-foreground border border-border',
        className
      )}
    >
      <span>
        {featureAccess.current}/{featureAccess.limit}
        {showPercentage && ` (${percentage}%)`}
      </span>
      {isAtLimit && <PremiumBadge variant='gem' size='sm' />}
    </div>
  )
}
