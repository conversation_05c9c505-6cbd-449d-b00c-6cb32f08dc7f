'use client'

import { useState, useEffect } from 'react'
import { Search, Play, Download, Eye, Check } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import {
  useMediaAssets,
  formatFileSize,
  formatDuration,
  getMediaType,
  getQualityBadgeColor,
  type MediaAsset,
} from '@/hooks/useMediaAssets'
import { Skeleton } from '@/components/ui/skeleton'
import { StockPagination } from '@/components/stock-pagination'
import { toast } from '@/lib/toast'

interface MediaGalleryProps {
  onMediaSelect?: (media: MediaAsset) => void
  className?: string
  searchQuery?: string
  mediaTypeFilter?: 'all' | 'image' | 'video'
}

export function MediaGallery({
  onMediaSelect,
  className = '',
  searchQuery = '',
  mediaTypeFilter = 'all',
}: MediaGalleryProps) {
  const [searchInput, setSearchInput] = useState('')
  const [search, setSearch] = useState('')
  const [mediaType, setMediaType] = useState<'all' | 'image' | 'video'>('all')
  const [viewMode] = useState<'grid' | 'list'>('grid')
  const [page, setPage] = useState(1)
  const [selectedId, setSelectedId] = useState<string | null>(null)
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null)
  const [previewVideoUrl, setPreviewVideoUrl] = useState<string | null>(null)

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearch(searchInput)
      setPage(1) // Reset to first page when searching
    }, 500) // 500ms delay

    return () => clearTimeout(timer)
  }, [searchInput])

  // Use external search and filter if provided, otherwise use internal state
  const effectiveSearch = searchQuery || search
  const effectiveMediaType =
    mediaTypeFilter !== 'all' ? mediaTypeFilter : mediaType

  const { data, isLoading } = useMediaAssets({
    page,
    limit: 20,
    filters: {
      type: effectiveMediaType,
      search: effectiveSearch || undefined,
    },
  })

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value)
  }

  const handleMediaTypeChange = (value: string) => {
    setMediaType(value as 'all' | 'image' | 'video')
    setPage(1)
  }

  const handleMediaClick = (media: MediaAsset) => {
    if (selectedId === media.id) {
      setSelectedId(null) // Deselect if already selected
    } else {
      setSelectedId(media.id)
    }
    onMediaSelect?.(media)
  }

  const downloadMedia = async (media: MediaAsset) => {
    try {
      const response = await fetch(media.originalUrl)
      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = media.originalName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      toast.success('Download started')
    } catch {
      toast.error('Failed to download file')
    }
  }

  const MediaCard = ({ media }: { media: MediaAsset }) => {
    const isSelected = selectedId === media.id
    const mediaTypeIcon = getMediaType(media.mimeType)

    return (
      <div
        key={media.id}
        onClick={() => handleMediaClick(media)}
        className={`relative overflow-hidden rounded-md border transition-all duration-200 hover:shadow-md cursor-pointer group ${
          isSelected
            ? 'border-primary ring-2 ring-primary/30'
            : 'border-border hover:border-primary/40'
        }`}
        style={{
          aspectRatio: '16/9',
          background: 'var(--muted)',
        }}
      >
        {/* Checkmark overlay when selected */}
        {isSelected && (
          <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1 z-10'>
            <Check className='h-3 w-3' />
          </div>
        )}

        {/* Media Preview */}
        {media.thumbnailUrl || media.originalUrl ? (
          <img
            src={
              media.mimeType === 'image/gif'
                ? media.originalUrl
                : media.thumbnailUrl || media.originalUrl
            }
            alt={media.originalName}
            className='w-full h-full object-cover'
            loading='lazy'
          />
        ) : (
          <div className='w-full h-full flex items-center justify-center bg-muted'>
            {mediaTypeIcon === 'video' ? (
              <Play className='h-8 w-8 text-muted-foreground' />
            ) : (
              <Eye className='h-8 w-8 text-muted-foreground' />
            )}
          </div>
        )}

        {/* Duration overlay for videos */}
        {media.duration && (
          <div className='absolute bottom-2 left-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded'>
            {formatDuration(parseFloat(media.duration))}
          </div>
        )}

        {/* Dimensions overlay */}
        {media.width && media.height && (
          <div className='absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded'>
            {media.width}×{media.height}
          </div>
        )}

        {/* Eye icon overlay - top left */}
        <div className='absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200'>
          <button
            onClick={e => {
              e.stopPropagation()
              if (mediaTypeIcon === 'video') {
                setPreviewVideoUrl(media.originalUrl)
              } else {
                setPreviewImageUrl(media.originalUrl)
              }
            }}
            className='bg-background/90 hover:bg-background border border-border text-foreground rounded-full p-1.5 shadow-sm hover:shadow-md transition-all duration-200'
          >
            <Eye className='h-4 w-4' />
          </button>
        </div>

        {/* Asset name overlay - top right, only on hover */}
        <div className='absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 max-w-[60%]'>
          <div className='bg-popover/90 text-popover-foreground text-xs px-1.5 py-0.5 rounded truncate border border-border shadow-sm'>
            {media.originalName}
          </div>
        </div>
      </div>
    )
  }

  const MediaListItem = ({ media }: { media: MediaAsset }) => {
    const isSelected = selectedId === media.id
    const mediaTypeIcon = getMediaType(media.mimeType)

    return (
      <Card
        className={`
          cursor-pointer transition-all hover:shadow-sm
          ${isSelected ? 'ring-2 ring-primary' : ''}
        `}
        onClick={() => handleMediaClick(media)}
      >
        <CardContent className='p-4'>
          <div className='flex items-center space-x-4'>
            {/* Thumbnail */}
            <div className='w-16 h-16 bg-muted rounded-lg overflow-hidden flex-shrink-0'>
              {media.thumbnailUrl || media.originalUrl ? (
                <img
                  src={
                    media.mimeType === 'image/gif'
                      ? media.originalUrl
                      : media.thumbnailUrl || media.originalUrl
                  }
                  alt={media.originalName}
                  className='w-full h-full object-cover'
                />
              ) : (
                <div className='w-full h-full flex items-center justify-center'>
                  {mediaTypeIcon === 'video' ? (
                    <Play className='h-6 w-6 text-muted-foreground' />
                  ) : (
                    <Eye className='h-6 w-6 text-muted-foreground' />
                  )}
                </div>
              )}
            </div>

            {/* Info */}
            <div className='flex-1 min-w-0'>
              <h3 className='font-medium text-sm truncate mb-1'>
                {media.originalName}
              </h3>
              <div className='flex items-center space-x-4 text-xs text-gray-500'>
                <span>{formatFileSize(media.fileSize)}</span>
                {media.width && media.height && (
                  <span>
                    {media.width} × {media.height}
                  </span>
                )}
                {media.duration && (
                  <span>{formatDuration(parseFloat(media.duration))}</span>
                )}
                {media.createdAt && (
                  <span>{new Date(media.createdAt).toLocaleDateString()}</span>
                )}
              </div>
            </div>

            {/* Badges */}
            <div className='flex items-center space-x-2'>
              <Badge variant='secondary' className='text-xs'>
                {mediaTypeIcon === 'video' ? 'Video' : 'Image'}
              </Badge>
              {media.quality && (
                <Badge
                  className={`text-xs ${getQualityBadgeColor(media.quality)}`}
                >
                  {media.quality.toUpperCase()}
                </Badge>
              )}
              <Button
                variant='ghost'
                size='sm'
                onClick={e => {
                  e.stopPropagation()
                  downloadMedia(media)
                }}
                className='h-8 w-8 p-0'
              >
                <Download className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header - only show if not using external props */}
      {!searchQuery && mediaTypeFilter === 'all' && (
        <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
          <div className='flex-1 w-full'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
              <Input
                placeholder='Search media...'
                value={searchInput}
                onChange={e => handleSearchInputChange(e.target.value)}
                className='pl-10'
              />
            </div>
          </div>

          <div className='flex items-center space-x-2'>
            <Select value={mediaType} onValueChange={handleMediaTypeChange}>
              <SelectTrigger className='w-32'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Media</SelectItem>
                <SelectItem value='image'>Images</SelectItem>
                <SelectItem value='video'>Videos</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Content */}
      {isLoading ? (
        <div
          className={
            searchQuery || mediaTypeFilter !== 'all' || viewMode === 'grid'
              ? 'grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-4'
              : 'space-y-3'
          }
        >
          {Array.from({ length: 10 }).map((_, i) => (
            <div key={i} className='relative overflow-hidden rounded-md border'>
              <Skeleton className='w-full' style={{ aspectRatio: '16/9' }} />
            </div>
          ))}
        </div>
      ) : data?.assets.length === 0 ? (
        <div className='text-center py-12'>
          <div className='text-gray-400 mb-4'>
            {effectiveMediaType === 'all'
              ? '📁'
              : effectiveMediaType === 'image'
                ? '🖼️'
                : '🎥'}
          </div>
          <h3 className='text-lg font-medium text-gray-900 mb-2'>
            No{' '}
            {effectiveMediaType === 'all' ? 'media' : `${effectiveMediaType}s`}{' '}
            found
          </h3>
          <p className='text-gray-500'>
            {effectiveSearch
              ? 'Try adjusting your search terms.'
              : 'Upload some files to get started.'}
          </p>
        </div>
      ) : (
        <div
          className={
            searchQuery || mediaTypeFilter !== 'all' || viewMode === 'grid'
              ? 'grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-4'
              : 'space-y-3'
          }
        >
          {data?.assets.map(media =>
            searchQuery || mediaTypeFilter !== 'all' || viewMode === 'grid' ? (
              <MediaCard key={media.id} media={media} />
            ) : (
              <MediaListItem key={media.id} media={media} />
            )
          )}
        </div>
      )}

      {/* Pagination */}
      {data && data.assets.length > 0 && data.total > 20 && (
        <StockPagination
          currentPage={page}
          totalPages={Math.ceil(data.total / 20)}
          onPageChange={setPage}
          className='mt-6'
        />
      )}

      {/* Image Preview Dialog */}
      <Dialog
        open={!!previewImageUrl}
        onOpenChange={() => setPreviewImageUrl(null)}
      >
        <DialogContent className='bg-black/90 rounded-lg shadow-lg border-0 w-full max-w-2xl flex flex-col items-center justify-center'>
          <img
            src={previewImageUrl || ''}
            className='w-full h-auto max-h-[70vh] rounded-lg'
            alt='Preview'
            style={{ background: '#000' }}
          />
        </DialogContent>
      </Dialog>

      {/* Video Preview Dialog */}
      <Dialog
        open={!!previewVideoUrl}
        onOpenChange={() => setPreviewVideoUrl(null)}
      >
        <DialogContent className='bg-transparent rounded-lg shadow-lg border-0 w-full max-w-2xl flex flex-col items-center justify-center'>
          {previewVideoUrl && (
            <video
              src={previewVideoUrl}
              className='w-full h-auto max-h-[70vh] rounded-lg'
              autoPlay
              muted
              loop
              controls
              style={{ background: '#000' }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
