'use client'
import React from 'react'
import { ArrowRight } from 'lucide-react'
import {
  BillingToggle,
  PricingTable,
} from '@/app/(dashboard)/billing/_components'
import { getUIPlans } from '@/lib/plan-utils'

export function PricingModal() {
  const [billingPeriod, setBillingPeriod] = React.useState<
    'monthly' | 'annual'
  >('annual')

  const plans = getUIPlans(billingPeriod)

  return (
    <div className='space-y-8'>
      {/* Page header */}
      <div>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8'>
          <div className='text-center sm:text-left mb-6 sm:mb-0'>
            <h2 className='text-2xl font-bold mb-3'>Choose your plan</h2>
          </div>
        </div>
      </div>

      {/* Pricing Table */}
      <div>
        {/* Billing Toggle */}
        <div className='flex justify-center mb-4'>
          <BillingToggle
            activePeriod={billingPeriod}
            onChange={setBillingPeriod}
          />
        </div>
        <PricingTable plans={plans} billingPeriod={billingPeriod} />
      </div>

      {/* Enterprise Contact Section */}
      <div className='w-full mx-auto rounded-lg bg-card border border-border/40 p-8 text-center'>
        <div className='max-w-lg mx-auto'>
          <h3 className='text-xl font-semibold mb-3'>
            Need a custom enterprise solution?
          </h3>
          <p className='text-muted-foreground mb-6 text-sm'>
            Our team can help you customize a plan that meets your specific
            requirements.
          </p>
          <a
            href='mailto:<EMAIL>'
            target='_blank'
            className='inline-flex items-center gap-1.5 text-primary hover:text-primary/90 font-medium'
          >
            Contact our sales team
            <ArrowRight className='h-4 w-4' />
          </a>
        </div>
      </div>
    </div>
  )
}
