'use client'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import {
  <PERSON>rkles,
  ArrowUp,
  ArrowDown,
  CheckCircle,
  Hash,
  Youtube,
} from 'lucide-react'
import { useState } from 'react'
import { useOpenAITextTransform } from '@/hooks/useOpenAITextTransform'

interface YouTubeAIAction {
  action: string
  label: string
  icon: string
  color: string
  requiresText?: boolean
  fieldTypes: ('title' | 'description' | 'tags')[]
}

const YOUTUBE_AI_ACTIONS: YouTubeAIAction[] = [
  {
    action: 'youtube-catchy-title',
    label: 'Make title catchy for YouTube',
    icon: 'youtube',
    color: 'text-red-500',
    requiresText: true,
    fieldTypes: ['title'],
  },
  {
    action: 'shorter-title',
    label: 'Make shorter title',
    icon: 'arrow-down',
    color: 'text-orange-500',
    requiresText: true,
    fieldTypes: ['title'],
  },
  {
    action: 'fix-grammar',
    label: 'Fix grammar',
    icon: 'check-circle',
    color: 'text-blue-500',
    requiresText: true,
    fieldTypes: ['title', 'description'],
  },
  {
    action: 'improve-writing',
    label: 'Improve writing',
    icon: 'sparkles',
    color: 'text-purple-500',
    requiresText: true,
    fieldTypes: ['title', 'description'],
  },
  {
    action: 'longer-description',
    label: 'Make description longer',
    icon: 'arrow-up',
    color: 'text-green-500',
    requiresText: true,
    fieldTypes: ['description'],
  },
  {
    action: 'generate-description-from-title',
    label: 'Generate description from title',
    icon: 'sparkles',
    color: 'text-purple-500',
    requiresText: false,
    fieldTypes: ['description'],
  },
  {
    action: 'shorter-description',
    label: 'Make description shorter',
    icon: 'arrow-down',
    color: 'text-orange-500',
    requiresText: true,
    fieldTypes: ['description'],
  },
  {
    action: 'generate-tags-from-title',
    label: 'Generate tags using title',
    icon: 'hash',
    color: 'text-indigo-500',
    requiresText: false,
    fieldTypes: ['tags'],
  },
  {
    action: 'generate-tags-from-description',
    label: 'Generate tags using description',
    icon: 'hash',
    color: 'text-cyan-500',
    requiresText: false,
    fieldTypes: ['tags'],
  },
]

interface YouTubeAITextAssistantProps {
  text: string
  onTextChange: (text: string) => void
  placeholder?: string
  buttonSize?: 'sm' | 'default' | 'lg'
  buttonClassName?: string
  fieldType?: 'title' | 'description' | 'tags'
  formData?: {
    title: string
    description: string
    tags: string[]
  }
}

export function YouTubeAITextAssistant({
  text,
  onTextChange,
  placeholder = 'AI Generate',
  buttonSize = 'default',
  buttonClassName = '',
  fieldType = 'title',
  formData,
}: YouTubeAITextAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [showResponse, setShowResponse] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const openaiTransform = useOpenAITextTransform()

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'youtube':
        return <Youtube className='h-4 w-4' />
      case 'arrow-up':
        return <ArrowUp className='h-4 w-4' />
      case 'arrow-down':
        return <ArrowDown className='h-4 w-4' />
      case 'check-circle':
        return <CheckCircle className='h-4 w-4' />
      case 'sparkles':
        return <Sparkles className='h-4 w-4' />
      case 'hash':
        return <Hash className='h-4 w-4' />
      default:
        return <Sparkles className='h-4 w-4' />
    }
  }

  const getPromptForAction = (action: string, inputText: string): string => {
    const titleText = formData?.title || ''
    const descText = formData?.description || ''

    switch (action) {
      case 'youtube-catchy-title':
        return `Make this title more catchy and engaging for YouTube viewers. Keep it under 60 characters and optimize for click-through rate: "${inputText}"`
      case 'shorter-title':
        return `Make this title shorter while maintaining its impact and meaning. Keep it under 40 characters: "${inputText}"`
      case 'fix-grammar':
        return `Fix any grammatical errors, spelling mistakes, and improve sentence structure in this text: "${inputText}"`
      case 'improve-writing':
        return `Improve the overall writing quality, clarity, and engagement of this text: "${inputText}"`
      case 'longer-description':
        return `Expand this description with more detail, context, and engaging information for YouTube viewers: "${inputText}"`
      case 'generate-description-from-title':
        return `Generate a compelling YouTube video description based on this title: "${titleText}". Include relevant details, call-to-action, and engaging content that would encourage viewers to watch and engage.`
      case 'shorter-description':
        return `Make this description shorter and more concise while maintaining its key information and impact: "${inputText}"`
      case 'generate-tags-from-title':
        return `Generate 10-15 relevant YouTube tags (comma-separated) based on this title: "${titleText}". Focus on searchable keywords and phrases that YouTube users would search for.`
      case 'generate-tags-from-description':
        return `Generate 10-15 relevant YouTube tags (comma-separated) based on this description: "${descText}". Focus on searchable keywords and phrases that YouTube users would search for.`
      default:
        return inputText
    }
  }

  const handleActionClick = async (action: string) => {
    setIsLoading(true)
    setShowResponse(false)

    try {
      let inputText = text

      // Special handling for actions that don't require current field text
      if (
        action === 'generate-description-from-title' ||
        action === 'generate-tags-from-title' ||
        action === 'generate-tags-from-description'
      ) {
        inputText = '' // These actions use formData context instead
      }

      const prompt = getPromptForAction(action, inputText)
      const result = await openaiTransform.mutateAsync({
        text: prompt,
        action: 'custom',
        customInstruction: prompt,
      })

      if (result) {
        onTextChange(result.transformedText)
        setShowResponse(true)
        setTimeout(() => {
          setShowResponse(false)
          setIsOpen(false)
        }, 2000)
      }
    } catch (error) {
      console.error('AI transformation error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Filter actions based on fieldType and available data
  const availableActions = YOUTUBE_AI_ACTIONS.filter(action => {
    // Check if action is available for current field type
    if (!action.fieldTypes.includes(fieldType)) {
      return false
    }

    // Check if required data is available for the action
    switch (action.action) {
      case 'generate-description-from-title':
        return formData?.title?.trim()
      case 'generate-tags-from-title':
        return formData?.title?.trim()
      case 'generate-tags-from-description':
        return formData?.description?.trim()
      default:
        // For actions that require text, check if current field has text
        return !action.requiresText || text.trim()
    }
  })

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant='outline'
          size={buttonSize}
          className={`gap-2 ${buttonClassName}`}
          disabled={isLoading}
        >
          <Sparkles className='h-4 w-4' />
          {isLoading ? 'Generating...' : placeholder}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-64'>
        {/* Success Response */}
        {showResponse && !isLoading && (
          <div className='p-3 text-center'>
            <CheckCircle className='h-6 w-6 text-green-500 mx-auto mb-2' />
            <p className='text-sm text-green-600 font-medium'>
              Text updated successfully!
            </p>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className='p-3 text-center'>
            <div className='animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2' />
            <p className='text-sm text-muted-foreground'>
              AI is working on it...
            </p>
          </div>
        )}

        {/* Action Items */}
        {!showResponse && !isLoading && (
          <>
            {availableActions.map(action => (
              <DropdownMenuItem
                key={action.action}
                onClick={e => {
                  e.preventDefault()
                  handleActionClick(action.action)
                }}
                className='flex items-center gap-2 cursor-pointer'
                onSelect={e => e.preventDefault()}
              >
                <span className={action.color}>{getIcon(action.icon)}</span>
                <span>{action.label}</span>
              </DropdownMenuItem>
            ))}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
