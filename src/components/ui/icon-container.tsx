'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface IconContainerProps {
  icon: React.ElementType
  className?: string
  containerClassName?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'secondary' | 'primary' | 'outline'
}

export function IconContainer({
  icon: Icon,
  className,
  containerClassName,
  size = 'md',
  variant = 'secondary',
}: IconContainerProps) {
  const sizeClasses = {
    sm: 'h-8 w-8 min-w-8',
    md: 'h-10 w-10 min-w-10',
    lg: 'h-11 w-11 min-w-11',
  }

  const iconSizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  }

  const variantClasses = {
    default: 'bg-muted text-muted-foreground',
    secondary: 'bg-secondary text-secondary-foreground',
    primary: 'bg-primary text-primary-foreground',
    outline: 'bg-transparent border border-border text-foreground',
  }

  return (
    <div
      className={cn(
        'rounded-lg shadow-sm relative z-10 overflow-hidden',
        'transform transition-transform duration-300 hover:scale-105',
        'flex items-center justify-center p-0',
        sizeClasses[size],
        variantClasses[variant],
        containerClassName
      )}
    >
      <div className='absolute inset-0 opacity-20 bg-white mix-blend-overlay' />
      <Icon className={cn(iconSizeClasses[size], 'relative z-10', className)} />
    </div>
  )
}
