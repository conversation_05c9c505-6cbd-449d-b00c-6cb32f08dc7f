'use client'

import { ReactNode } from 'react'
import { LucideIcon } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { cn } from '@/lib/utils'

interface PageHeaderProps {
  title: string
  icon?: LucideIcon
  leftContent?: ReactNode
  rightContent?: ReactNode
  className?: string
}

export function PageHeader({
  title,
  icon: Icon,
  leftContent,
  rightContent,
  className,
}: PageHeaderProps) {
  return (
    <header
      className={cn(
        'sticky top-0 z-30 flex h-14 md:h-16 shrink-0 items-center justify-between border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
        className
      )}
    >
      <div className='flex items-center gap-2 px-2 md:px-4'>
        <SidebarTrigger className='-ml-1' />
        <Separator
          orientation='vertical'
          className='hidden md:block mr-2 h-4'
        />
        <div className='flex items-center gap-2'>
          {Icon && <Icon className='h-5 w-5 md:h-6 md:w-6 text-primary' />}
          <h1
            className={cn(
              'font-semibold truncate max-w-[150px] sm:max-w-none',
              'text-lg md:text-xl'
            )}
          >
            {title}
          </h1>
        </div>
        {leftContent}
      </div>
      <div className='flex items-center gap-2 md:gap-4 px-2 md:px-4'>
        {rightContent}
      </div>
    </header>
  )
}
