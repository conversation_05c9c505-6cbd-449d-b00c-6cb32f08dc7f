'use client'

import * as React from 'react'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog'
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer'
import { cn } from '@/lib/utils'
import { X } from 'lucide-react'

interface BaseModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  children: React.ReactNode
  className?: string
  contentClassName?: string
  showClose?: boolean
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}

/**
 * BaseModal component
 *
 * A responsive modal that switches between Dialog (desktop) and Drawer (mobile).
 * This component is the foundation for all modals in the application.
 */
export function BaseModal({
  isOpen,
  onOpenChange,
  title,
  children,
  className,
  contentClassName,
  showClose = true,
  size = 'md',
}: BaseModalProps) {
  const isMobile = useIsMobile()

  // Determine max width based on size prop
  const sizeClasses = {
    sm: 'sm:max-w-sm',
    md: 'sm:max-w-md',
    lg: 'sm:max-w-lg',
    xl: 'sm:max-w-xl',
    full: 'sm:max-w-screen-lg',
  }

  // Mobile: Use Drawer component
  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={onOpenChange}>
        <DrawerContent className={cn('max-h-[85vh]', className)}>
          {title && (
            <DrawerHeader className='text-left'>
              <DrawerTitle>{title}</DrawerTitle>
            </DrawerHeader>
          )}
          <div
            className={cn(
              'px-4 pb-4 overflow-y-auto hide-scrollbar',
              contentClassName
            )}
          >
            {children}
          </div>
        </DrawerContent>
      </Drawer>
    )
  }

  // Desktop: Use Dialog component
  return (
    <Dialog open={isOpen} onOpenChange={showClose ? onOpenChange : undefined}>
      <DialogContent
        className={cn(
          'max-h-[85vh] overflow-hidden',
          sizeClasses[size],
          className
        )}
      >
        {title && (
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
        )}
        <div className={cn('overflow-y-auto hide-scrollbar', contentClassName)}>
          {children}
        </div>

        {/* Custom close button when showClose is false */}
        {!showClose && (
          <DialogClose className='absolute top-4 right-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground'>
            <X className='h-4 w-4' />
            <span className='sr-only'>Close</span>
          </DialogClose>
        )}
      </DialogContent>
    </Dialog>
  )
}
