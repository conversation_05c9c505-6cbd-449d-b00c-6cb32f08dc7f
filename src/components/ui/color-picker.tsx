'use client'

import { useState } from 'react'
import { Paintbrush } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'

interface ColorPickerProps {
  value: string
  onChange: (value: string) => void
  className?: string
}

const COLOR_PRESETS = [
  '#000000',
  '#ffffff',
  '#ef4444',
  '#f97316',
  '#eab308',
  '#22c55e',
  '#06b6d4',
  '#3b82f6',
  '#8b5cf6',
  '#ec4899',
  '#f43f5e',
  '#84cc16',
  '#10b981',
  '#0ea5e9',
  '#6366f1',
  '#a855f7',
  '#d946ef',
  '#f59e0b',
]

export function ColorPicker({ value, onChange, className }: ColorPickerProps) {
  const [open, setOpen] = useState(false)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          className={cn(
            'w-full justify-start text-left font-normal',
            !value && 'text-muted-foreground',
            className
          )}
        >
          <div
            className='w-4 h-4 rounded !bg-center !bg-cover transition-all'
            style={{ backgroundColor: value }}
          >
            {!value && <Paintbrush className='h-4 w-4' />}
          </div>
          <div className='truncate flex-1'>
            {value ? value.toUpperCase() : 'Pick a color'}
          </div>
        </Button>
      </PopoverTrigger>

      <PopoverContent className='w-64'>
        <div className='flex flex-wrap gap-1 mt-0'>
          {COLOR_PRESETS.map(color => (
            <div
              key={color}
              className={cn(
                'rounded-md h-6 w-6 cursor-pointer active:scale-105 border border-border',
                value === color && 'ring-2 ring-ring ring-offset-2'
              )}
              style={{ backgroundColor: color }}
              onClick={() => onChange(color)}
            />
          ))}
        </div>

        <div className='mt-3 space-y-3'>
          <div className='flex items-center gap-2'>
            <div
              className='rounded-md h-8 w-8 border border-border'
              style={{ backgroundColor: value }}
            />
            <Input
              value={value}
              onChange={e => onChange(e.target.value)}
              placeholder='#000000'
              className='flex-1'
            />
          </div>

          <div className='flex items-center gap-2'>
            <label
              htmlFor='colorInput'
              className='text-sm text-muted-foreground'
            >
              Custom:
            </label>
            <input
              id='colorInput'
              type='color'
              value={value}
              onChange={e => onChange(e.target.value)}
              className='w-8 h-8 rounded border border-border cursor-pointer'
            />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
