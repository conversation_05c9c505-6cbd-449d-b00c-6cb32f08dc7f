'use client'

import React, { useState, useRef } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { X, ChevronDown } from 'lucide-react'

export interface SelectItem {
  value: string
  label?: string
  [key: string]: unknown
}

interface SelectEditorProps {
  value: SelectItem[]
  onValueChange: (value: SelectItem[]) => void
  items: SelectItem[]
  placeholder?: string
  className?: string
  children?: React.ReactNode
}

interface SelectEditorContextType {
  value: SelectItem[]
  onValueChange: (value: SelectItem[]) => void
  items: SelectItem[]
  filteredItems: SelectItem[]
  inputValue: string
  setInputValue: (value: string) => void
  isOpen: boolean
  setIsOpen: (open: boolean) => void
  selectedIndex: number
  setSelectedIndex: React.Dispatch<React.SetStateAction<number>>
}

const SelectEditorContext = React.createContext<
  SelectEditorContextType | undefined
>(undefined)

function useSelectEditor() {
  const context = React.useContext(SelectEditorContext)
  if (!context) {
    throw new Error('useSelectEditor must be used within SelectEditor')
  }
  return context
}

export function SelectEditor({
  value,
  onValueChange,
  items,
  className,
  children,
}: SelectEditorProps) {
  const [inputValue, setInputValue] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)

  const filteredItems = items.filter(
    item =>
      !value.some(selected => selected.value === item.value) &&
      (item.label || item.value)
        .toLowerCase()
        .includes(inputValue.toLowerCase())
  )

  const contextValue: SelectEditorContextType = {
    value,
    onValueChange,
    items,
    filteredItems,
    inputValue,
    setInputValue,
    isOpen,
    setIsOpen,
    selectedIndex,
    setSelectedIndex,
  }

  return (
    <SelectEditorContext.Provider value={contextValue}>
      <div className={cn('relative', className)}>{children}</div>
    </SelectEditorContext.Provider>
  )
}

export function SelectEditorContent({
  children,
}: {
  children: React.ReactNode
}) {
  return <div className='relative'>{children}</div>
}

export function SelectEditorInput({ placeholder }: { placeholder?: string }) {
  const {
    value,
    onValueChange,
    inputValue,
    setInputValue,
    isOpen,
    setIsOpen,
    filteredItems,
    selectedIndex,
    setSelectedIndex,
  } = useSelectEditor()

  const inputRef = useRef<HTMLInputElement>(null)

  const handleRemoveItem = (itemToRemove: SelectItem) => {
    onValueChange(value.filter(item => item.value !== itemToRemove.value))
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      if (isOpen && filteredItems[selectedIndex]) {
        onValueChange([...value, filteredItems[selectedIndex]])
        setInputValue('')
        setSelectedIndex(0)
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      if (!isOpen) {
        setIsOpen(true)
      } else {
        setSelectedIndex(prev => Math.min(prev + 1, filteredItems.length - 1))
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      if (isOpen) {
        setSelectedIndex(prev => Math.max(prev - 1, 0))
      }
    } else if (e.key === 'Escape') {
      setIsOpen(false)
      setInputValue('')
    } else if (e.key === 'Backspace' && inputValue === '' && value.length > 0) {
      onValueChange(value.slice(0, -1))
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    if (newValue.length > 0) {
      setIsOpen(true)
    }
    setSelectedIndex(0)
  }

  const handleFocus = () => {
    // Don't automatically open dropdown on focus
    // Only open when user starts typing or clicks the chevron
  }

  const getPlaceholderText = () => {
    if (value.length === 0) {
      return (
        placeholder || 'Type to search filters (language, gender, accent...)'
      )
    }
    return ''
  }

  return (
    <div className='relative'>
      <div className='min-h-[2.5rem] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2'>
        <div className='flex flex-wrap gap-1 items-center'>
          {value.map(item => (
            <Badge
              key={item.value}
              variant='secondary'
              className='flex items-center gap-1 px-2 py-1'
            >
              <span>{item.label || item.value}</span>
              <Button
                type='button'
                variant='ghost'
                size='sm'
                className='h-auto p-0 w-4 h-4 hover:bg-transparent'
                onClick={() => handleRemoveItem(item)}
              >
                <X className='h-3 w-3' />
              </Button>
            </Badge>
          ))}
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            placeholder={getPlaceholderText()}
            className='border-none p-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0 min-w-[120px] flex-1'
          />
          <Button
            type='button'
            variant='ghost'
            size='sm'
            className='h-6 w-6 p-0'
            onClick={() => setIsOpen(!isOpen)}
          >
            <ChevronDown
              className={cn(
                'h-4 w-4 transition-transform',
                isOpen && 'rotate-180'
              )}
            />
          </Button>
        </div>
      </div>
    </div>
  )
}

export function SelectEditorCombobox() {
  const {
    value,
    onValueChange,
    filteredItems,
    isOpen,
    setIsOpen,
    setInputValue,
    selectedIndex,
    setSelectedIndex,
  } = useSelectEditor()

  if (!isOpen || filteredItems.length === 0) return null

  // Group items by category
  const groupedItems = filteredItems.reduce(
    (groups, item) => {
      const category = (item.category as string) || 'Other'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(item)
      return groups
    },
    {} as Record<string, typeof filteredItems>
  )

  // Define category order and labels
  const categoryOrder = [
    'language',
    'gender',
    'accent',
    'age',
    'use_case',
    'descriptive',
    'description',
  ]
  const categoryLabels: Record<string, string> = {
    language: 'Languages',
    gender: 'Gender',
    accent: 'Accents',
    age: 'Age Groups',
    use_case: 'Use Cases',
    descriptive: 'Styles',
    description: 'Styles',
  }

  const handleSelectItem = (item: SelectItem) => {
    onValueChange([...value, item])
    setInputValue('')
    setSelectedIndex(0)
    setIsOpen(false)
  }

  // Calculate flat index for keyboard navigation
  let flatIndex = 0
  const indexMap = new Map<number, SelectItem>()

  categoryOrder.forEach(categoryKey => {
    if (groupedItems[categoryKey]) {
      groupedItems[categoryKey].forEach(item => {
        indexMap.set(flatIndex, item)
        flatIndex++
      })
    }
  })

  const selectedItem = indexMap.get(selectedIndex)

  return (
    <div className='absolute top-full left-0 right-0 z-50 mt-1 max-h-80 overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md'>
      <div className='p-1'>
        {categoryOrder.map(categoryKey => {
          const categoryItems = groupedItems[categoryKey]
          if (!categoryItems || categoryItems.length === 0) return null

          return (
            <div key={categoryKey} className='mb-2 last:mb-0'>
              {/* Category Header */}
              <div className='px-2 py-1.5 text-xs font-semibold text-muted-foreground bg-muted/50 rounded-sm mb-1 sticky top-0 z-10 backdrop-blur-sm'>
                {categoryLabels[categoryKey] || categoryKey}
              </div>

              {/* Category Items */}
              <div className='space-y-0.5'>
                {categoryItems.map(item => {
                  const isSelected = selectedItem?.value === item.value
                  return (
                    <div
                      key={item.value}
                      className={cn(
                        'relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none ml-2',
                        'hover:bg-accent hover:text-accent-foreground',
                        isSelected && 'bg-accent text-accent-foreground'
                      )}
                      onClick={() => handleSelectItem(item)}
                      onMouseEnter={() => {
                        // Find the flat index for this item
                        for (const [index, mapItem] of indexMap.entries()) {
                          if (mapItem.value === item.value) {
                            setSelectedIndex(index)
                            break
                          }
                        }
                      }}
                    >
                      {/* Show the full descriptive text from label, or fallback to formatted value */}
                      <span className='capitalize'>
                        {item.label?.includes(':')
                          ? item.label.split(':')[1]?.trim()
                          : item.value.split(':')[1]?.replace('_', ' ')}
                      </span>
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
