import React from 'react'
import { cn } from '@/lib/utils'
import { <PERSON>, CardHeader, CardContent, CardFooter } from '@/components/ui/card'

interface CardWithHoverProps extends React.ComponentProps<'div'> {
  withGradient?: boolean
  withBorder?: boolean
  withShadow?: boolean
  children: React.ReactNode
}

const CardWithHover = React.forwardRef<HTMLDivElement, CardWithHoverProps>(
  (
    {
      className,
      withGradient = true,
      withBorder = true,
      withShadow = true,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <Card
        ref={ref}
        className={cn(
          'overflow-hidden transition-all duration-300 ease-out relative group h-full flex flex-col p-0',
          // Enhanced shadow system with layered shadows for depth
          withShadow && [
            'shadow-sm',
            'hover:shadow-lg hover:shadow-black/10',
            'dark:shadow-black/20 dark:hover:shadow-black/30',
            // Subtle scale effect on hover
            'hover:scale-[1.02] hover:-translate-y-1',
          ],
          withBorder && 'hover:border-primary/40',
          className
        )}
        {...props}
      >
        {withGradient && (
          <div className='absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-transparent group-hover:to-primary/8 transition-all duration-300 ease-out' />
        )}
        {children}
      </Card>
    )
  }
)

CardWithHover.displayName = 'CardWithHover'

// Compound components with improved spacing consistency
const CardWithHoverHeader = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<typeof CardHeader>
>(({ className, ...props }, ref) => (
  <CardHeader
    ref={ref}
    className={cn('pb-0 pt-6 px-6 relative', className)}
    {...props}
  />
))
CardWithHoverHeader.displayName = 'CardWithHoverHeader'

const CardWithHoverContent = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<typeof CardContent>
>(({ className, ...props }, ref) => (
  <CardContent
    ref={ref}
    className={cn('pb-0 px-6 pt-4 relative', className)}
    {...props}
  />
))
CardWithHoverContent.displayName = 'CardWithHoverContent'

const CardWithHoverFooter = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<typeof CardFooter>
>(({ className, ...props }, ref) => (
  <CardFooter
    ref={ref}
    className={cn('pt-4 pb-6 px-6 relative mt-auto', className)}
    {...props}
  />
))
CardWithHoverFooter.displayName = 'CardWithHoverFooter'

export {
  CardWithHover,
  CardWithHoverHeader,
  CardWithHoverContent,
  CardWithHoverFooter,
}
