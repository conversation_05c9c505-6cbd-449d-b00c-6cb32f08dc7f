'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface ResponsiveTextProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode
  hideBelow?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  showBelow?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
}

const breakpointClasses = {
  hideBelow: {
    sm: 'hidden sm:inline',
    md: 'hidden md:inline',
    lg: 'hidden lg:inline',
    xl: 'hidden xl:inline',
    '2xl': 'hidden 2xl:inline',
  },
  showBelow: {
    sm: 'inline sm:hidden',
    md: 'inline md:hidden',
    lg: 'inline lg:hidden',
    xl: 'inline xl:hidden',
    '2xl': 'inline 2xl:hidden',
  },
}

export function ResponsiveText({
  children,
  className,
  hideBelow,
  showBelow,
  ...props
}: ResponsiveTextProps) {
  let responsiveClass = ''

  if (hideBelow) {
    responsiveClass = breakpointClasses.hideBelow[hideBelow]
  } else if (showBelow) {
    responsiveClass = breakpointClasses.showBelow[showBelow]
  }

  return (
    <span className={cn(responsiveClass, className)} {...props}>
      {children}
    </span>
  )
}
