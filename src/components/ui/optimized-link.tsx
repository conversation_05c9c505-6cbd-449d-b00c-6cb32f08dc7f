'use client'

import Link, { LinkProps } from 'next/link'
import { useRouter } from 'next/navigation'
import { useState, useCallback, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface OptimizedLinkProps extends LinkProps {
  children: React.ReactNode
  className?: string
  prefetchStrategy?: 'hover' | 'viewport' | 'immediate' | 'none'
  showLoadingState?: boolean
}

export function OptimizedLink({
  children,
  className,
  prefetchStrategy = 'viewport',
  showLoadingState = false,
  onClick,
  ...props
}: OptimizedLinkProps) {
  const router = useRouter()
  const [isHovered, setIsHovered] = useState(false)
  const [isPrefetched, setIsPrefetched] = useState(false)
  const [isNavigating, setIsNavigating] = useState(false)

  // Determine prefetch behavior based on strategy
  const shouldPrefetch = useCallback(() => {
    switch (prefetchStrategy) {
      case 'immediate':
        return true
      case 'hover':
        return isHovered
      case 'viewport':
        return true // Let Next.js handle viewport detection
      case 'none':
        return false
      default:
        return true
    }
  }, [prefetchStrategy, isHovered])

  // Handle hover prefetching
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
    if (prefetchStrategy === 'hover' && !isPrefetched) {
      router.prefetch(props.href.toString())
      setIsPrefetched(true)
    }
  }, [prefetchStrategy, isPrefetched, router, props.href])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
  }, [])

  // Handle click with loading state
  const handleClick = useCallback(
    (e: React.MouseEvent<HTMLAnchorElement>) => {
      if (showLoadingState) {
        setIsNavigating(true)
        // Reset after a timeout as fallback
        setTimeout(() => setIsNavigating(false), 5000)
      }

      // Call original onClick if provided
      if (onClick) {
        onClick(e)
      }
    },
    [showLoadingState, onClick]
  )

  // Reset navigation state on route change
  useEffect(() => {
    const handleRouteChange = () => {
      setIsNavigating(false)
    }

    // Listen for route changes (simplified)
    const currentPath = window.location.pathname
    const checkRouteChange = () => {
      if (window.location.pathname !== currentPath) {
        handleRouteChange()
      }
    }

    const interval = setInterval(checkRouteChange, 100)
    return () => clearInterval(interval)
  }, [])

  return (
    <Link
      {...props}
      prefetch={shouldPrefetch()}
      className={cn(
        'transition-colors duration-200',
        isNavigating && showLoadingState && 'opacity-70 pointer-events-none',
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
    >
      <span
        className={cn(
          'inline-flex items-center gap-2',
          isNavigating && showLoadingState && 'animate-pulse'
        )}
      >
        {children}
        {isNavigating && showLoadingState && (
          <span className='inline-block w-3 h-3 border border-current border-t-transparent rounded-full animate-spin' />
        )}
      </span>
    </Link>
  )
}

// Specialized link components for common use cases
export function NavLink({ children, className, ...props }: OptimizedLinkProps) {
  return (
    <OptimizedLink
      {...props}
      prefetchStrategy='hover'
      showLoadingState={true}
      className={cn(
        'hover:text-primary transition-colors duration-200',
        className
      )}
    >
      {children}
    </OptimizedLink>
  )
}

export function CardLink({
  children,
  className,
  ...props
}: OptimizedLinkProps) {
  return (
    <OptimizedLink
      {...props}
      prefetchStrategy='viewport'
      showLoadingState={false}
      className={cn(
        'block hover:scale-[1.02] transition-transform duration-200',
        className
      )}
    >
      {children}
    </OptimizedLink>
  )
}

export function ButtonLink({
  children,
  className,
  ...props
}: OptimizedLinkProps) {
  return (
    <OptimizedLink
      {...props}
      prefetchStrategy='immediate'
      showLoadingState={true}
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
        'disabled:pointer-events-none disabled:opacity-50',
        className
      )}
    >
      {children}
    </OptimizedLink>
  )
}
