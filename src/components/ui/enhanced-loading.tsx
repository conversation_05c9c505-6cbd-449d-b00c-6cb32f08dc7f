'use client'

import React from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@/lib/utils'

// Simple memo wrapper to replace performance-utils
const withMemo = <P extends Record<string, unknown>>(
  Component: React.ComponentType<P>
): React.ComponentType<P> => {
  return React.memo(Component)
}

interface EnhancedLoadingProps {
  variant?: 'default' | 'card' | 'list' | 'grid' | 'form' | 'video' | 'sidebar'
  count?: number
  className?: string
  animate?: boolean
}

function EnhancedLoadingComponent({
  variant = 'default',
  count = 1,
  className,
  animate = true,
}: EnhancedLoadingProps) {
  const baseClassName = cn('space-y-4', !animate && 'animate-none', className)

  const renderSkeleton = (index: number) => {
    switch (variant) {
      case 'card':
        return (
          <div key={index} className='border rounded-lg p-4 space-y-3'>
            <Skeleton className='h-4 w-3/4' />
            <Skeleton className='h-3 w-1/2' />
            <Skeleton className='h-32 w-full' />
            <div className='flex gap-2'>
              <Skeleton className='h-8 w-16' />
              <Skeleton className='h-8 w-16' />
            </div>
          </div>
        )

      case 'list':
        return (
          <div
            key={index}
            className='flex items-center gap-3 p-3 border rounded-lg'
          >
            <Skeleton className='h-10 w-10 rounded-full' />
            <div className='flex-1 space-y-2'>
              <Skeleton className='h-4 w-3/4' />
              <Skeleton className='h-3 w-1/2' />
            </div>
            <Skeleton className='h-8 w-8' />
          </div>
        )

      case 'grid':
        return (
          <div key={index} className='space-y-2'>
            <Skeleton className='aspect-video w-full rounded-lg' />
            <Skeleton className='h-4 w-3/4' />
            <Skeleton className='h-3 w-1/2' />
          </div>
        )

      case 'form':
        return (
          <div key={index} className='space-y-4'>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-20' />
              <Skeleton className='h-10 w-full' />
            </div>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-32 w-full' />
            </div>
            <div className='flex gap-2'>
              <Skeleton className='h-10 w-20' />
              <Skeleton className='h-10 w-24' />
            </div>
          </div>
        )

      case 'video':
        return (
          <div key={index} className='space-y-4'>
            <Skeleton className='aspect-video w-full rounded-lg' />
            <div className='flex items-center gap-3'>
              <Skeleton className='h-10 w-10 rounded-full' />
              <div className='flex-1 space-y-2'>
                <Skeleton className='h-4 w-3/4' />
                <Skeleton className='h-3 w-1/2' />
              </div>
            </div>
          </div>
        )

      case 'sidebar':
        return (
          <div key={index} className='space-y-3'>
            <div className='flex items-center gap-2'>
              <Skeleton className='h-6 w-6' />
              <Skeleton className='h-4 w-24' />
            </div>
            <div className='pl-8 space-y-2'>
              <Skeleton className='h-3 w-full' />
              <Skeleton className='h-3 w-3/4' />
              <Skeleton className='h-3 w-1/2' />
            </div>
          </div>
        )

      default:
        return (
          <div key={index} className='space-y-2'>
            <Skeleton className='h-4 w-full' />
            <Skeleton className='h-4 w-3/4' />
            <Skeleton className='h-4 w-1/2' />
          </div>
        )
    }
  }

  if (variant === 'grid') {
    return (
      <div
        className={cn(
          'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4',
          className
        )}
      >
        {Array.from({ length: count }, (_, index) => renderSkeleton(index))}
      </div>
    )
  }

  return (
    <div className={baseClassName}>
      {Array.from({ length: count }, (_, index) => renderSkeleton(index))}
    </div>
  )
}

// Memoized loading components for specific use cases
export const CardLoading = withMemo(
  (props: Omit<EnhancedLoadingProps, 'variant'>) => (
    <EnhancedLoadingComponent {...props} variant='card' />
  )
)

export const ListLoading = withMemo(
  (props: Omit<EnhancedLoadingProps, 'variant'>) => (
    <EnhancedLoadingComponent {...props} variant='list' />
  )
)

export const GridLoading = withMemo(
  (props: Omit<EnhancedLoadingProps, 'variant'>) => (
    <EnhancedLoadingComponent {...props} variant='grid' />
  )
)

export const FormLoading = withMemo(
  (props: Omit<EnhancedLoadingProps, 'variant'>) => (
    <EnhancedLoadingComponent {...props} variant='form' />
  )
)

export const VideoLoading = withMemo(
  (props: Omit<EnhancedLoadingProps, 'variant'>) => (
    <EnhancedLoadingComponent {...props} variant='video' />
  )
)

export const SidebarLoading = withMemo(
  (props: Omit<EnhancedLoadingProps, 'variant'>) => (
    <EnhancedLoadingComponent {...props} variant='sidebar' />
  )
)

// Main component export
export const EnhancedLoading = withMemo(EnhancedLoadingComponent)

// Specialized loading components for common patterns
export const SceneEditorLoading = withMemo(() => (
  <div className='flex h-screen'>
    <div className='flex-1 p-6'>
      <VideoLoading />
    </div>
    <div className='w-80 border-l p-4'>
      <SidebarLoading count={5} />
    </div>
  </div>
))

export const MediaPickerLoading = withMemo(() => (
  <div className='space-y-4'>
    <div className='flex items-center justify-between'>
      <Skeleton className='h-6 w-32' />
      <Skeleton className='h-9 w-24' />
    </div>
    <GridLoading count={8} />
  </div>
))

export const VoicePickerLoading = withMemo(() => (
  <div className='space-y-4'>
    <Skeleton className='h-10 w-full' />
    <ListLoading count={6} />
  </div>
))

// Performance-optimized loading for heavy components
export const LazyComponentLoading = withMemo(
  ({
    height = 'h-64',
    message = 'Loading...',
  }: {
    height?: string
    message?: string
  }) => (
    <div className={cn('flex items-center justify-center', height)}>
      <div className='text-center space-y-2'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto' />
        <p className='text-sm text-muted-foreground'>{message}</p>
      </div>
    </div>
  )
)

// Intersection observer loading for lazy components
export const IntersectionLoading = withMemo(
  ({
    children,
    fallback,
    rootMargin = '50px',
  }: {
    children: React.ReactNode
    fallback?: React.ReactNode
    rootMargin?: string
  }) => {
    const [isVisible, setIsVisible] = React.useState(false)
    const ref = React.useRef<HTMLDivElement>(null)

    React.useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsVisible(true)
            observer.disconnect()
          }
        },
        { rootMargin }
      )

      if (ref.current) {
        observer.observe(ref.current)
      }

      return () => observer.disconnect()
    }, [rootMargin])

    return (
      <div ref={ref}>
        {isVisible ? children : fallback || <LazyComponentLoading />}
      </div>
    )
  }
)
