'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { Check, ChevronsUpDown, Search } from 'lucide-react'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import type { GoogleFont } from '@/app/api/fonts/route'

interface FontComboboxProps {
  selectedFont?: string
  onFontSelect: (fontFamily: string) => void
  className?: string
  placeholder?: string
}

interface FontsResponse {
  fonts: GoogleFont[]
  total: number
}

// Font cache to avoid repeated API calls
const fontCache = new Map<string, GoogleFont[]>()
const loadedFontsCache = new Set<string>()

export function FontCombobox({
  selectedFont = 'Inter',
  onFontSelect,
  className,
  placeholder = 'Search fonts...',
}: FontComboboxProps) {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState('')
  const [fonts, setFonts] = useState<GoogleFont[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Debounced search to avoid too many API calls
  const [debouncedSearch, setDebouncedSearch] = useState('')
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search)
    }, 300)
    return () => clearTimeout(timer)
  }, [search])

  // Load fonts with caching and debouncing
  const loadFonts = useCallback(async (searchTerm: string = '') => {
    const cacheKey = searchTerm.toLowerCase()

    // Check cache first
    if (fontCache.has(cacheKey)) {
      setFonts(fontCache.get(cacheKey)!)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/fonts')
      if (!response.ok) {
        throw new Error('Failed to fetch fonts')
      }

      const data: FontsResponse = await response.json()
      let filteredFonts = data.fonts

      // Client-side filtering for better performance
      if (searchTerm) {
        filteredFonts = data.fonts.filter(font =>
          font.family.toLowerCase().includes(searchTerm.toLowerCase())
        )
      }

      // Cache the results
      fontCache.set(cacheKey, filteredFonts)
      setFonts(filteredFonts)
    } catch (err) {
      console.error('Error fetching fonts:', err)
      setError('Failed to load fonts')
      // Fallback fonts
      const fallbackFonts: GoogleFont[] = [
        {
          family: 'Inter',
          variants: ['400', '700'],
          subsets: ['latin'],
          category: 'sans-serif',
          files: {},
        },
        {
          family: 'Roboto',
          variants: ['400', '700'],
          subsets: ['latin'],
          category: 'sans-serif',
          files: {},
        },
        {
          family: 'Open Sans',
          variants: ['400', '700'],
          subsets: ['latin'],
          category: 'sans-serif',
          files: {},
        },
        {
          family: 'Montserrat',
          variants: ['400', '700'],
          subsets: ['latin'],
          category: 'sans-serif',
          files: {},
        },
        {
          family: 'Poppins',
          variants: ['400', '700'],
          subsets: ['latin'],
          category: 'sans-serif',
          files: {},
        },
      ]
      setFonts(fallbackFonts)
    } finally {
      setLoading(false)
    }
  }, [])

  // Load initial fonts
  useEffect(() => {
    loadFonts()
  }, [loadFonts])

  // Load fonts when search changes
  useEffect(() => {
    if (debouncedSearch !== '') {
      loadFonts(debouncedSearch)
    } else if (search === '') {
      loadFonts() // Load all fonts when search is cleared
    }
  }, [debouncedSearch, loadFonts, search])

  // Load font dynamically when selected
  const loadFontCSS = useCallback((fontFamily: string) => {
    if (loadedFontsCache.has(fontFamily)) return

    const formattedName = fontFamily.replace(/\s+/g, '+')
    const existingLink = document.querySelector(
      `link[href*="${formattedName}"]`
    )

    if (!existingLink) {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = `https://fonts.googleapis.com/css2?family=${formattedName}:wght@400;700&display=swap`
      document.head.appendChild(link)
      loadedFontsCache.add(fontFamily)
    }
  }, [])

  // Preload selected font
  useEffect(() => {
    if (selectedFont) {
      loadFontCSS(selectedFont)
    }
  }, [selectedFont, loadFontCSS])

  // Handle font selection
  const handleFontSelect = useCallback(
    (fontFamily: string) => {
      loadFontCSS(fontFamily)
      onFontSelect(fontFamily)
      setOpen(false)
      setSearch('') // Clear search when selected
    },
    [onFontSelect, loadFontCSS]
  )

  // Memoize the current font display
  const selectedFontDisplay = useMemo(() => {
    const font = fonts.find(f => f.family === selectedFont)
    return font ? font.family : selectedFont
  }, [selectedFont, fonts])

  // Memoize filtered fonts for display (limit to first 100 for performance)
  const displayFonts = useMemo(() => {
    return fonts.slice(0, 100)
  }, [fonts])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className={cn('w-full justify-between font-normal', className)}
          style={{
            fontFamily: loadedFontsCache.has(selectedFont)
              ? selectedFont
              : 'inherit',
          }}
        >
          {selectedFontDisplay || 'Select font...'}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>

      <PopoverContent className='w-[300px] p-0' align='start'>
        <Command shouldFilter={false}>
          <div className='flex items-center border-b px-3'>
            <Search className='mr-2 h-4 w-4 shrink-0 opacity-50' />
            <input
              placeholder={placeholder}
              value={search}
              onChange={e => setSearch(e.target.value)}
              className='flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50'
            />
          </div>

          <CommandList className='max-h-[300px]'>
            {loading && (
              <div className='flex items-center justify-center py-6 text-sm text-muted-foreground'>
                Loading fonts...
              </div>
            )}

            {error && (
              <div className='flex items-center justify-center py-6 text-sm text-muted-foreground'>
                {error}
              </div>
            )}

            {!loading && !error && displayFonts.length === 0 && (
              <CommandEmpty>No fonts found.</CommandEmpty>
            )}

            {!loading && !error && displayFonts.length > 0 && (
              <CommandGroup>
                {displayFonts.map(font => (
                  <CommandItem
                    key={font.family}
                    value={font.family}
                    onSelect={() => handleFontSelect(font.family)}
                    className='cursor-pointer'
                  >
                    <div className='flex items-center justify-between w-full'>
                      <span
                        className='flex-1 truncate'
                        style={{
                          fontFamily: loadedFontsCache.has(font.family)
                            ? font.family
                            : 'inherit',
                        }}
                        onMouseEnter={() => loadFontCSS(font.family)}
                      >
                        {font.family}
                      </span>
                      <div className='flex items-center gap-2 ml-2'>
                        <span className='text-xs text-muted-foreground capitalize'>
                          {font.category}
                        </span>
                        <Check
                          className={cn(
                            'h-4 w-4',
                            selectedFont === font.family
                              ? 'opacity-100'
                              : 'opacity-0'
                          )}
                        />
                      </div>
                    </div>
                  </CommandItem>
                ))}

                {fonts.length > 100 && (
                  <div className='px-2 py-1 text-xs text-muted-foreground text-center border-t'>
                    Showing first 100 results. Use search to find more.
                  </div>
                )}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
