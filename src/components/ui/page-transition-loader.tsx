'use client'

import { useEffect, useState, useCallback } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'
import { useLinkStatus } from 'next/link'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PageTransitionLoaderProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
}

export function PageTransitionLoader({
  className,
  size = 'md',
  showText = true,
}: PageTransitionLoaderProps) {
  const { pending } = useLinkStatus()

  if (!pending) return null

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  }

  return (
    <div
      role='status'
      aria-label='Loading'
      className={cn(
        'inline-flex items-center gap-2 text-muted-foreground animate-in fade-in-0 duration-200',
        className
      )}
    >
      <Loader2 className={cn('animate-spin', sizeClasses[size])} />
      {showText && <span className='text-sm'>Loading...</span>}
    </div>
  )
}

// Optimized router-based navigation loader with debouncing
export function RouterBasedLoader() {
  const [isLoading, setIsLoading] = useState(false)
  const [progress, setProgress] = useState(0)
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Debounced loading state to prevent flashing on fast navigations
  const [debouncedLoading, setDebouncedLoading] = useState(false)

  const handleLinkClick = useCallback((e: Event) => {
    const target = e.target as HTMLElement
    const link = target.closest('a')

    // Only show loader for internal navigation
    if (
      link &&
      link.href &&
      !link.href.startsWith('#') &&
      !link.href.startsWith('mailto:') &&
      !link.href.startsWith('tel:')
    ) {
      const url = new URL(link.href)
      const currentUrl = new URL(window.location.href)

      // Only show loader for different pages
      if (
        url.pathname !== currentUrl.pathname ||
        url.search !== currentUrl.search
      ) {
        setIsLoading(true)
        setProgress(0)

        // Debounce the visual loader to prevent flashing
        const debounceTimer = setTimeout(() => {
          setDebouncedLoading(true)
        }, 100)

        // Fallback timeout
        const fallbackTimer = setTimeout(() => {
          setIsLoading(false)
          setDebouncedLoading(false)
          setProgress(0)
        }, 5000)

        // Store timers for cleanup
        ;(
          link as HTMLAnchorElement & {
            _debounceTimer?: NodeJS.Timeout
            _fallbackTimer?: NodeJS.Timeout
          }
        )._debounceTimer = debounceTimer
        ;(
          link as HTMLAnchorElement & {
            _debounceTimer?: NodeJS.Timeout
            _fallbackTimer?: NodeJS.Timeout
          }
        )._fallbackTimer = fallbackTimer
      }
    }
  }, [])

  const handlePopState = useCallback(() => {
    setIsLoading(true)
    setProgress(0)
    setDebouncedLoading(true)

    setTimeout(() => {
      setIsLoading(false)
      setDebouncedLoading(false)
      setProgress(0)
    }, 1000)
  }, [])

  useEffect(() => {
    document.addEventListener('click', handleLinkClick, { passive: true })
    window.addEventListener('popstate', handlePopState)

    return () => {
      document.removeEventListener('click', handleLinkClick)
      window.removeEventListener('popstate', handlePopState)
    }
  }, [handleLinkClick, handlePopState])

  // Progress animation
  useEffect(() => {
    if (!isLoading) return

    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) return prev
        return prev + Math.random() * 10
      })
    }, 200)

    return () => clearInterval(interval)
  }, [isLoading])

  // Reset loading when route actually changes
  useEffect(() => {
    setIsLoading(false)
    setDebouncedLoading(false)
    setProgress(100)

    // Complete the progress bar
    const timer = setTimeout(() => {
      setProgress(0)
    }, 200)

    return () => clearTimeout(timer)
  }, [pathname, searchParams])

  if (!debouncedLoading && !isLoading) return null

  return (
    <>
      {/* Professional progress bar with actual progress */}
      <div className='fixed top-0 left-0 right-0 z-[9999] h-1 bg-gradient-to-r from-background via-muted/20 to-background overflow-hidden'>
        {/* Progress bar */}
        <div
          className='h-full bg-gradient-to-r from-primary/80 via-primary to-primary/80 transition-all duration-300 ease-out shadow-sm'
          style={{
            width: `${Math.min(progress, 100)}%`,
            transform: progress === 100 ? 'translateX(0)' : 'translateX(0)',
          }}
        />

        {/* Shimmer effect for indeterminate progress */}
        {progress < 90 && (
          <div
            className='absolute top-0 h-full w-20 bg-gradient-to-r from-transparent via-primary/30 to-transparent animate-shimmer'
            style={{
              left: `${Math.max(0, progress - 10)}%`,
            }}
          />
        )}
      </div>

      {/* Subtle glow effect */}
      <div className='fixed top-0 left-0 right-0 z-[9998] h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent opacity-60' />

      {/* Optional: Very subtle backdrop */}
      <div className='fixed top-0 left-0 right-0 z-[9997] h-8 bg-gradient-to-b from-background/30 to-transparent pointer-events-none' />
    </>
  )
}

// Global page transition loader for layout
export function GlobalPageTransitionLoader() {
  const { pending } = useLinkStatus()

  // Clean implementation without debug logs

  // Use both approaches for maximum compatibility
  return (
    <>
      <RouterBasedLoader />
      {pending && (
        <>
          {/* useLinkStatus version - subtle indicator */}
          <div className='fixed top-1 left-0 right-0 z-[9995] h-px bg-gradient-to-r from-transparent via-accent/60 to-transparent animate-pulse' />
        </>
      )}
    </>
  )
}

// Alternative progress bar styles you can use
export function AlternativeProgressBar() {
  const { pending } = useLinkStatus()

  if (!pending) return null

  return (
    <div className='fixed top-0 left-0 right-0 z-50'>
      {/* Thicker progress bar with pulse */}
      <div className='h-2 bg-muted/10 overflow-hidden'>
        <div className='h-full bg-primary animate-progress shadow-lg shadow-primary/30' />
      </div>
    </div>
  )
}

// Minimalist style
export function MinimalistProgressBar() {
  const [isLoading, setIsLoading] = useState(false)
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLElement
      const link = target.closest('a')
      if (link && link.href && !link.href.startsWith('#')) {
        setIsLoading(true)
        setTimeout(() => setIsLoading(false), 3000)
      }
    }

    document.addEventListener('click', handleLinkClick)
    return () => document.removeEventListener('click', handleLinkClick)
  }, [])

  useEffect(() => {
    setIsLoading(false)
  }, [pathname, searchParams])

  if (!isLoading) return null

  return (
    <div className='fixed top-0 left-0 right-0 z-[9999] h-0.5 bg-muted/30 overflow-hidden'>
      <div
        className='h-full bg-primary animate-shimmer'
        style={{ width: '200%', transform: 'translateX(-100%)' }}
      />
    </div>
  )
}

// Premium style with multiple layers
export function PremiumProgressBar() {
  const [isLoading, setIsLoading] = useState(false)
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLElement
      const link = target.closest('a')
      if (link && link.href && !link.href.startsWith('#')) {
        setIsLoading(true)
        setTimeout(() => setIsLoading(false), 3000)
      }
    }

    document.addEventListener('click', handleLinkClick)
    return () => document.removeEventListener('click', handleLinkClick)
  }, [])

  useEffect(() => {
    setIsLoading(false)
  }, [pathname, searchParams])

  if (!isLoading) return null

  return (
    <>
      {/* Main progress bar */}
      <div className='fixed top-0 left-0 right-0 z-[9999] h-1 bg-gradient-to-r from-muted/10 via-muted/20 to-muted/10 overflow-hidden border-b border-border/20'>
        <div
          className='h-full bg-gradient-to-r from-primary/60 via-primary to-accent animate-shimmer'
          style={{ width: '250%', transform: 'translateX(-100%)' }}
        />
      </div>

      {/* Accent line */}
      <div className='fixed top-1 left-0 right-0 z-[9998] h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent' />

      {/* Subtle shadow */}
      <div className='fixed top-0 left-0 right-0 z-[9997] h-2 bg-gradient-to-b from-primary/5 to-transparent blur-sm' />
    </>
  )
}

// Clean inline loader - no ugly dots
export function InlinePageLoader() {
  const { pending } = useLinkStatus()
  return pending ? (
    <Loader2 className='inline-block h-4 w-4 ml-2 animate-spin text-primary/70' />
  ) : null
}

// Alternative inline loader styles
export function InlineShimmerLoader() {
  const { pending } = useLinkStatus()
  return pending ? (
    <div className='inline-block ml-2 h-4 w-8 bg-gradient-to-r from-primary/20 via-primary/60 to-primary/20 animate-shimmer rounded-sm' />
  ) : null
}

export function InlinePulseLoader() {
  const { pending } = useLinkStatus()
  return pending ? (
    <div className='inline-block ml-2 h-2 w-2 bg-primary rounded-full animate-pulse' />
  ) : null
}

// Router-based inline loader (more reliable)
export function RouterInlineLoader() {
  const [isLoading, setIsLoading] = useState(false)
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLElement
      const link = target.closest('a')
      if (link && link.href && !link.href.startsWith('#')) {
        setIsLoading(true)
        setTimeout(() => setIsLoading(false), 3000)
      }
    }

    document.addEventListener('click', handleLinkClick)
    return () => document.removeEventListener('click', handleLinkClick)
  }, [])

  useEffect(() => {
    setIsLoading(false)
  }, [pathname, searchParams])

  return isLoading ? (
    <Loader2 className='inline-block h-4 w-4 ml-2 animate-spin text-primary/70' />
  ) : null
}

// Minimalist router-based loader
export function RouterMinimalLoader() {
  const [isLoading, setIsLoading] = useState(false)
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLElement
      const link = target.closest('a')
      if (link && link.href && !link.href.startsWith('#')) {
        setIsLoading(true)
        setTimeout(() => setIsLoading(false), 3000)
      }
    }

    document.addEventListener('click', handleLinkClick)
    return () => document.removeEventListener('click', handleLinkClick)
  }, [])

  useEffect(() => {
    setIsLoading(false)
  }, [pathname, searchParams])

  return isLoading ? (
    <div className='inline-block ml-2 h-2 w-2 bg-primary rounded-full animate-pulse' />
  ) : null
}
