'use client'

import { Gem, Lock, Crown } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PremiumBadgeProps {
  variant?: 'gem' | 'lock' | 'crown'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  showText?: boolean
  text?: string
}

export function PremiumBadge({
  variant = 'gem',
  size = 'md',
  className,
  showText = false,
  text = 'Premium'
}: PremiumBadgeProps) {
  const icons = {
    gem: Gem,
    lock: Lock,
    crown: Crown
  }

  const sizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4', 
    lg: 'h-5 w-5'
  }

  const textSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  const Icon = icons[variant]

  return (
    <div className={cn(
      'inline-flex items-center gap-1 text-orange-500',
      showText && 'px-2 py-1 bg-orange-50 dark:bg-orange-950/20 rounded-full border border-orange-200 dark:border-orange-800',
      className
    )}>
      <Icon className={cn(sizes[size])} />
      {showText && (
        <span className={cn('font-medium', textSizes[size])}>
          {text}
        </span>
      )}
    </div>
  )
}

interface PremiumButtonProps {
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function PremiumButton({
  children,
  onClick,
  disabled = false,
  className,
  size = 'md'
}: PremiumButtonProps) {
  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-sm',
    lg: 'h-11 px-8 text-base'
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'inline-flex items-center justify-center gap-2 rounded-md font-medium transition-colors',
        'bg-gradient-to-r from-orange-600 to-orange-500 text-white',
        'hover:from-orange-700 hover:to-orange-600',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        sizeClasses[size],
        className
      )}
    >
      <Gem className='h-4 w-4' />
      {children}
    </button>
  )
}
