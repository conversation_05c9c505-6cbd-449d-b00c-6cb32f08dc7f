'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { VariantProps, cva } from 'class-variance-authority'

const gradientButtonVariants = cva(
  'transition-all duration-300 hover:shadow-md',
  {
    variants: {
      gradientType: {
        primary:
          'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-0 hover:from-primary/90 hover:to-primary/70',
        blue: 'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-0 hover:from-primary/90 hover:to-primary/70',
        green:
          'bg-gradient-to-r from-green-600 to-green-500 dark:from-green-500 dark:to-green-400 text-white border-0 hover:from-green-700 hover:to-green-600 dark:hover:from-green-600 dark:hover:to-green-500',
        orange:
          'bg-gradient-to-r from-orange-600 to-orange-500 dark:from-orange-500 dark:to-orange-400 text-white border-0 hover:from-orange-700 hover:to-orange-600 dark:hover:from-orange-600 dark:hover:to-orange-500',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
      hover: {
        scale: 'hover:translate-y-[-1px]',
        glow: 'hover:shadow-glow',
        none: '',
      },
    },
    defaultVariants: {
      gradientType: 'primary',
      size: 'default',
      hover: 'scale',
    },
  }
)

interface GradientButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof gradientButtonVariants> {
  asChild?: boolean
  responsive?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

export const GradientButton = React.forwardRef<
  HTMLButtonElement,
  GradientButtonProps
>(
  (
    {
      className,
      gradientType,
      size,
      hover,
      asChild = false,
      responsive = false,
      leftIcon,
      rightIcon,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <Button
        ref={ref}
        asChild={asChild}
        className={cn(
          gradientButtonVariants({ gradientType, size, hover }),
          responsive && 'sm:w-auto sm:px-3',
          className
        )}
        {...props}
      >
        <>
          {leftIcon && (
            <span className={cn('mr-2', responsive && 'mr-0 sm:mr-2')}>
              {leftIcon}
            </span>
          )}

          {responsive ? (
            <span className='hidden sm:inline'>{children}</span>
          ) : (
            children
          )}

          {rightIcon && (
            <span className={cn('ml-2', responsive && 'ml-0 sm:ml-2')}>
              {rightIcon}
            </span>
          )}
        </>
      </Button>
    )
  }
)

GradientButton.displayName = 'GradientButton'
