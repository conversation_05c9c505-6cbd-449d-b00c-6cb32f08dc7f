'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Check, Search, Loader2, Type } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import WebFont from 'webfontloader'
import type { GoogleFont } from '@/app/api/fonts/route'

interface FontPickerProps {
  selectedFont?: string
  onFontSelect: (fontFamily: string) => void
  previewText?: string
  className?: string
}

interface FontsResponse {
  fonts: GoogleFont[]
  total: number
}

const FONT_CATEGORIES = [
  { value: 'all', label: 'All Fonts' },
  { value: 'serif', label: 'Serif' },
  { value: 'sans-serif', label: 'Sans Serif' },
  { value: 'display', label: 'Display' },
  { value: 'handwriting', label: 'Handwriting' },
  { value: 'monospace', label: 'Monospace' },
]

export function FontPicker({
  selectedFont = 'Inter',
  onFontSelect,
  previewText = 'The quick brown fox jumps over the lazy dog',
  className,
}: FontPickerProps) {
  const [fonts, setFonts] = useState<GoogleFont[]>([])
  const [filteredFonts, setFilteredFonts] = useState<GoogleFont[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [loadedFonts, setLoadedFonts] = useState<Set<string>>(new Set())
  const [displayCount, setDisplayCount] = useState(50) // Virtual scrolling

  const loadedFontsRef = useRef<Set<string>>(new Set())
  const observerRef = useRef<IntersectionObserver | null>(null)

  // Fetch fonts from API
  useEffect(() => {
    const fetchFonts = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/fonts')

        if (!response.ok) {
          throw new Error('Failed to fetch fonts')
        }

        const data: FontsResponse = await response.json()
        setFonts(data.fonts)
        setFilteredFonts(data.fonts.slice(0, 50)) // Initially show first 50
      } catch (err) {
        console.error('Error fetching fonts:', err)
        setError('Failed to load fonts. Please check your API key.')
      } finally {
        setLoading(false)
      }
    }

    fetchFonts()
  }, [])

  // Filter fonts based on search and category
  useEffect(() => {
    let filtered = fonts

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(font => font.category === selectedCategory)
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(font =>
        font.family.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredFonts(filtered.slice(0, displayCount))
  }, [fonts, searchTerm, selectedCategory, displayCount])

  // Load font dynamically using WebFontLoader
  const loadFont = useCallback((fontFamily: string) => {
    if (loadedFontsRef.current.has(fontFamily)) return

    WebFont.load({
      google: {
        families: [fontFamily + ':400,700'],
      },
      active: () => {
        loadedFontsRef.current.add(fontFamily)
        setLoadedFonts(new Set(loadedFontsRef.current))
      },
      inactive: () => {
        console.warn(`Failed to load font: ${fontFamily}`)
      },
    })
  }, [])

  // Intersection observer for virtual scrolling
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const fontFamily = entry.target.getAttribute('data-font-family')
            if (fontFamily) {
              loadFont(fontFamily)
            }
          }
        })
      },
      { rootMargin: '100px' }
    )

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [loadFont])

  // Load more fonts when scrolling
  const loadMoreFonts = useCallback(() => {
    setDisplayCount(prev => Math.min(prev + 50, fonts.length))
  }, [fonts.length])

  if (loading) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        <Loader2 className='w-6 h-6 animate-spin' />
        <span className='ml-2'>Loading fonts...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div
        className={cn(
          'flex flex-col items-center justify-center p-8 text-center',
          className
        )}
      >
        <Type className='w-12 h-12 text-muted-foreground mb-2' />
        <p className='text-sm text-muted-foreground'>{error}</p>
      </div>
    )
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Search and Filters */}
      <div className='space-y-3 p-4 border-b'>
        <div className='relative'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground' />
          <Input
            placeholder='Search fonts...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='pl-10'
          />
        </div>

        <div className='flex gap-2 flex-wrap'>
          {FONT_CATEGORIES.map(category => (
            <Button
              key={category.value}
              variant={
                selectedCategory === category.value ? 'default' : 'outline'
              }
              size='sm'
              onClick={() => setSelectedCategory(category.value)}
              className='text-xs'
            >
              {category.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Font List */}
      <ScrollArea className='flex-1'>
        <div className='p-2'>
          {filteredFonts.length === 0 ? (
            <div className='text-center py-8'>
              <p className='text-sm text-muted-foreground'>No fonts found</p>
            </div>
          ) : (
            <>
              {filteredFonts.map(font => (
                <FontItem
                  key={font.family}
                  font={font}
                  isSelected={selectedFont === font.family}
                  isLoaded={loadedFonts.has(font.family)}
                  previewText={previewText}
                  onSelect={() => onFontSelect(font.family)}
                  observerRef={observerRef}
                />
              ))}

              {/* Load More Button */}
              {displayCount < fonts.length && (
                <div className='text-center py-4'>
                  <Button
                    variant='outline'
                    onClick={loadMoreFonts}
                    className='text-sm'
                  >
                    Load More Fonts ({fonts.length - displayCount} remaining)
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

interface FontItemProps {
  font: GoogleFont
  isSelected: boolean
  isLoaded: boolean
  previewText: string
  onSelect: () => void
  observerRef: React.RefObject<IntersectionObserver | null>
}

function FontItem({
  font,
  isSelected,
  isLoaded,
  previewText,
  onSelect,
  observerRef,
}: FontItemProps) {
  const itemRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = itemRef.current
    const observer = observerRef.current

    if (element && observer) {
      observer.observe(element)
      return () => observer.unobserve(element)
    }
  }, [observerRef])

  return (
    <div
      ref={itemRef}
      data-font-family={font.family}
      className={cn(
        'relative p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:border-primary/50 mb-2',
        isSelected
          ? 'border-primary ring-2 ring-primary/20 bg-primary/5'
          : 'border-border hover:bg-accent/50'
      )}
      onClick={onSelect}
    >
      {/* Selection Indicator */}
      {isSelected && (
        <div className='absolute -top-2 -right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center z-10 border-2 border-background shadow-lg'>
          <Check className='w-3.5 h-3.5 text-primary-foreground' />
        </div>
      )}

      <div className='space-y-2'>
        {/* Font Name */}
        <div className='flex items-center justify-between'>
          <h4 className='font-medium text-sm'>{font.family}</h4>
          <span className='text-xs text-muted-foreground capitalize'>
            {font.category}
          </span>
        </div>

        {/* Font Preview */}
        <div
          className={cn(
            'text-lg leading-relaxed transition-all duration-300',
            isLoaded ? 'opacity-100' : 'opacity-50'
          )}
          style={{
            fontFamily: isLoaded ? font.family : 'inherit',
          }}
        >
          {previewText}
        </div>

        {/* Loading indicator */}
        {!isLoaded && (
          <div className='flex items-center gap-2 text-xs text-muted-foreground'>
            <Loader2 className='w-3 h-3 animate-spin' />
            Loading font...
          </div>
        )}
      </div>
    </div>
  )
}
