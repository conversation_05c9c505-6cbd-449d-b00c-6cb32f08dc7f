'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface FullscreenGlassLoaderProps {
  message?: string
  className?: string
}

export function FullscreenGlassLoader({
  message = 'Preparing your workspace…',
  className,
}: FullscreenGlassLoaderProps) {
  return (
    <div
      aria-label='Application is initializing'
      aria-busy='true'
      role='status'
      className={cn(
        'fixed inset-0 z-[9999] flex items-center justify-center',
        'pointer-events-auto',
        className
      )}
    >
      {/* Backdrop with glass morphism */}
      <div className='absolute inset-0 bg-gradient-to-br from-background/60 to-background/40 backdrop-blur-xl' />

      {/* Subtle vignette/overlay for contrast */}
      <div className='absolute inset-0 [background:radial-gradient(60%_60%_at_50%_40%,theme(colors.background/0.0),theme(colors.background/0.4))]' />

      {/* Center content */}
      <div className='relative flex flex-col items-center gap-4 rounded-2xl border border-border/60 bg-background/30 p-6 shadow-xl backdrop-blur-2xl'>
        {/* Spinner */}
        <div className='h-10 w-10 rounded-full border-2 border-muted-foreground/30 border-t-primary animate-spin' />
        <p className='text-sm text-muted-foreground'>{message}</p>
      </div>
    </div>
  )
}

