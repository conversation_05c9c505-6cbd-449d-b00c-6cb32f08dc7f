'use client'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Spark<PERSON>, ArrowUp, ArrowDown, CheckCircle } from 'lucide-react'
import { useState } from 'react'
import { useOpenAITextTransform } from '@/hooks/useOpenAITextTransform'

const AI_ACTIONS = [
  {
    action: 'improve' as const,
    label: 'Improve writing',
    icon: 'sparkles',
    color: 'text-blue-500',
  },
  {
    action: 'emojify' as const,
    label: 'Emojify',
    icon: 'emoji',
    color: 'text-yellow-500',
  },
  {
    action: 'longer' as const,
    label: 'Make longer',
    icon: 'arrow-up',
    color: 'text-green-500',
  },
  {
    action: 'shorter' as const,
    label: 'Make shorter',
    icon: 'arrow-down',
    color: 'text-orange-500',
  },
  {
    action: 'fix' as const,
    label: 'Fix spelling & grammar',
    icon: 'check-circle',
    color: 'text-red-500',
  },
  {
    action: 'simplify' as const,
    label: 'Simplify language',
    icon: 'diamond',
    color: 'text-purple-500',
  },
] as const

const getIcon = (iconName: string) => {
  switch (iconName) {
    case 'sparkles':
      return <Sparkles className='h-4 w-4' />
    case 'emoji':
      return <span className='text-yellow-500'>😊</span>
    case 'arrow-up':
      return <ArrowUp className='h-4 w-4 rotate-90' />
    case 'arrow-down':
      return <ArrowDown className='h-4 w-4 rotate-90' />
    case 'check-circle':
      return <CheckCircle className='h-4 w-4' />
    case 'diamond':
      return (
        <span className='h-4 w-4 flex items-center justify-center font-bold'>
          ◆
        </span>
      )
    default:
      return <Sparkles className='h-4 w-4' />
  }
}

interface AITextAssistantProps {
  text: string
  onTextChange: (text: string) => void
  placeholder?: string
  buttonVariant?: 'default' | 'outline' | 'ghost'
  buttonSize?: 'sm' | 'default' | 'lg'
  buttonClassName?: string
  disabled?: boolean
}

export function AITextAssistant({
  text,
  onTextChange,
  placeholder = 'AI Generate',
  buttonVariant = 'outline',
  buttonSize = 'sm',
  buttonClassName = '',
  disabled = false,
}: AITextAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [customInput, setCustomInput] = useState('')
  const [response, setResponse] = useState('')
  const [showResponse, setShowResponse] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const openaiTransform = useOpenAITextTransform()

  const handleAction = async (
    action: 'improve' | 'emojify' | 'longer' | 'shorter' | 'fix' | 'simplify'
  ) => {
    if (!text) return

    setIsLoading(true)
    setShowResponse(false)

    try {
      const result = await openaiTransform.mutateAsync({
        text,
        action,
        customInstruction: customInput || undefined,
      })

      setResponse(result.transformedText)
      setShowResponse(true)
    } catch (error) {
      console.error('AI transformation failed:', error)
      setResponse(
        `Error: ${error instanceof Error ? error.message : 'Failed to process text'}`
      )
      setShowResponse(true)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCustomSubmit = async () => {
    if (!text || !customInput) return

    setIsLoading(true)
    setShowResponse(false)

    try {
      const result = await openaiTransform.mutateAsync({
        text,
        action: 'custom',
        customInstruction: customInput,
      })

      setResponse(result.transformedText)
      setShowResponse(true)
    } catch (error) {
      console.error('AI transformation failed:', error)
      setResponse(
        `Error: ${error instanceof Error ? error.message : 'Failed to process text'}`
      )
      setShowResponse(true)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInsertResponse = () => {
    onTextChange(response)
    setShowResponse(false)
    setResponse('')
    setIsOpen(false)
  }

  const handleDiscardResponse = () => {
    setShowResponse(false)
    setResponse('')
  }

  const handleTryAgain = () => {
    setShowResponse(false)
    setResponse('')
    if (customInput) {
      handleCustomSubmit()
    }
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={buttonVariant}
          size={buttonSize}
          className={`${buttonClassName}`}
          disabled={disabled}
        >
          <Sparkles className='h-3 w-3 mr-1' />
          {placeholder}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align='end' className='w-64 p-0'>
        <div className='px-3 py-2 text-sm font-medium text-muted-foreground border-b'>
          AI Assistant
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className='p-3 border-b bg-muted/30'>
            <div className='flex items-center gap-2 text-sm text-muted-foreground'>
              <div className='animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full'></div>
              Processing with AI...
            </div>
          </div>
        )}

        {/* AI Response */}
        {showResponse && !isLoading && (
          <div className='p-3 border-b bg-muted/30'>
            <div className='text-xs text-muted-foreground mb-2'>
              AI Response:
            </div>
            <div className='text-sm bg-background rounded-md p-2 mb-3 max-h-32 overflow-y-auto border'>
              {response}
            </div>
            <div className='flex gap-2'>
              <Button
                size='sm'
                onClick={handleInsertResponse}
                className='flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground'
              >
                Insert
              </Button>
              <Button
                size='sm'
                variant='outline'
                onClick={handleDiscardResponse}
                className='flex-1'
              >
                Discard
              </Button>
            </div>
            <Button
              size='sm'
              variant='ghost'
              onClick={handleTryAgain}
              className='w-full mt-2 text-xs'
            >
              Try again
            </Button>
          </div>
        )}

        {/* Input Field */}
        {!showResponse && !isLoading && (
          <div className='p-3 border-b'>
            <Input
              placeholder='Ask AI anything...'
              value={customInput}
              onChange={e => {
                e.stopPropagation()
                setCustomInput(e.target.value)
              }}
              onKeyDown={e => {
                e.stopPropagation()
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleCustomSubmit()
                }
              }}
              onFocus={e => e.stopPropagation()}
              onClick={e => e.stopPropagation()}
              className='text-sm'
              autoFocus
            />
            <Button
              size='sm'
              onClick={e => {
                e.stopPropagation()
                handleCustomSubmit()
              }}
              className='w-full mt-2 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground'
              disabled={!customInput}
            >
              Apply
            </Button>
          </div>
        )}

        {/* Quick Actions */}
        {!showResponse && !isLoading && (
          <>
            {AI_ACTIONS.map(action => (
              <DropdownMenuItem
                key={action.action}
                onClick={e => {
                  e.preventDefault()
                  handleAction(action.action)
                }}
                className='px-3 py-2 cursor-pointer'
                onSelect={e => e.preventDefault()}
              >
                <div className='flex items-center gap-2'>
                  <span className={action.color}>{getIcon(action.icon)}</span>
                  <span>{action.label}</span>
                </div>
              </DropdownMenuItem>
            ))}
            {AI_ACTIONS.length > 4 && <DropdownMenuSeparator />}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
