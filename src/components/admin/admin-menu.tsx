'use client'

import { useState } from 'react'
import { useIsAdmin, useImpersonation } from '@/hooks/use-admin'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Shield, Crown, UserX, Settings, Loader2 } from 'lucide-react'
import { authClient } from '@/lib/auth-client'
import { toast } from '@/lib/toast'
import Link from 'next/link'

export function AdminMenu() {
  const { isAdmin, isLoading } = useIsAdmin()
  const { isImpersonating, stopImpersonating, isStoppingImpersonation } =
    useImpersonation()
  const [impersonateDialog, setImpersonateDialog] = useState(false)
  const [userEmail, setUserEmail] = useState('')
  const [isImpersonating2, setIsImpersonating2] = useState(false)

  if (isLoading || !isAdmin) {
    return null
  }

  const handleQuickImpersonate = async () => {
    if (!userEmail.trim()) {
      toast.error('Please enter a user email')
      return
    }

    setIsImpersonating2(true)
    try {
      // First, find the user by email
      const usersResult = await authClient.admin.listUsers({
        query: {
          searchValue: userEmail.trim(),
          searchField: 'email',
          limit: 1,
        },
      })

      if (!usersResult.data?.users || !usersResult.data?.users?.length) {
        toast.error('User not found')
        return
      }

      const user = usersResult.data.users[0]

      // Then impersonate the user
      const result = await authClient.admin.impersonateUser({
        userId: user.id,
      })

      if (result.data) {
        toast.impersonationStart(user.name || user.email)
        setImpersonateDialog(false)
        setUserEmail('')
        // Refresh the page to update the session
        window.location.reload()
      } else {
        toast.adminError(
          'impersonate user',
          result.error?.message || 'Unknown error'
        )
      }
    } catch (error) {
      console.error('Error impersonating user:', error)
      toast.adminError('impersonate user', error)
    } finally {
      setIsImpersonating2(false)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
            <Shield className='h-4 w-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-56'>
          <DropdownMenuLabel className='flex items-center gap-2'>
            <Shield className='h-4 w-4' />
            Admin Tools
          </DropdownMenuLabel>
          <DropdownMenuSeparator />

          {isImpersonating ? (
            <DropdownMenuItem
              onClick={() => stopImpersonating()}
              disabled={isStoppingImpersonation}
              className='text-orange-600 focus:text-orange-600'
            >
              {isStoppingImpersonation ? (
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              ) : (
                <UserX className='mr-2 h-4 w-4' />
              )}
              Stop Impersonating
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem onClick={() => setImpersonateDialog(true)}>
              <Crown className='mr-2 h-4 w-4' />
              Quick Impersonate
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuItem asChild>
            <Link href='/admin'>
              <Settings className='mr-2 h-4 w-4' />
              Admin Dashboard
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Quick Impersonate Dialog */}
      <Dialog open={impersonateDialog} onOpenChange={setImpersonateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Quick Impersonate User</DialogTitle>
            <DialogDescription>
              Enter the email address of the user you want to impersonate.
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-4'>
            <div>
              <Label htmlFor='user-email'>User Email</Label>
              <Input
                id='user-email'
                type='email'
                placeholder='<EMAIL>'
                value={userEmail}
                onChange={e => setUserEmail(e.target.value)}
                onKeyDown={e => {
                  if (e.key === 'Enter') {
                    handleQuickImpersonate()
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => {
                setImpersonateDialog(false)
                setUserEmail('')
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleQuickImpersonate}
              disabled={isImpersonating2 || !userEmail.trim()}
            >
              {isImpersonating2 ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Impersonating...
                </>
              ) : (
                <>
                  <Crown className='mr-2 h-4 w-4' />
                  Impersonate
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
