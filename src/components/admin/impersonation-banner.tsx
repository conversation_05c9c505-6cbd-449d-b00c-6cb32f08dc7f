'use client'

import { useImpersonation } from '@/hooks/use-admin'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { UserX, Crown, Loader2 } from 'lucide-react'

export function ImpersonationBanner() {
  const {
    isImpersonating,
    impersonatingAdminId,
    stopImpersonating,
    isStoppingImpersonation,
  } = useImpersonation()

  if (!isImpersonating) {
    return null
  }

  return (
    <Alert
      variant='warning'
      className='rounded-none border-x-0 border-t-0 border-b'
    >
      {/* <AlertTriangle className='h-4 w-4' /> */}
      <AlertDescription className='flex items-center justify-between w-full mb-0'>
        <div className='flex items-center gap-2'>
          <Crown className='h-4 w-4' />
          <span className='font-medium'>
            You are currently impersonating a user
            {impersonatingAdminId && (
              <span className='ml-2 text-xs opacity-75 font-normal'>
                (Admin ID: {impersonatingAdminId.slice(0, 8)}...)
              </span>
            )}
          </span>
        </div>
        <Button
          variant='outline'
          size='sm'
          onClick={() => stopImpersonating()}
          disabled={isStoppingImpersonation}
          className='ml-4 shrink-0 border-warning/50 bg-warning/20 text-warning-foreground hover:bg-warning/30 dark:border-warning dark:bg-warning/10 dark:hover:bg-warning/20'
        >
          {isStoppingImpersonation ? (
            <>
              <Loader2 className='mr-2 h-3 w-3 animate-spin' />
              Stopping...
            </>
          ) : (
            <>
              <UserX className='mr-2 h-3 w-3' />
              Stop Impersonating
            </>
          )}
        </Button>
      </AlertDescription>
    </Alert>
  )
}
