'use client'

import { useState, useEffect } from 'react'
import {
  useAdminUsers,
  useImpersonateUser,
  useBanUser,
  useUserRole,
} from '@/hooks/use-admin'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  MoreHorizontal,
  <PERSON>r<PERSON>heck,
  UserX,
  Shield,
  Search,
  Loader2,
  Crown,
} from 'lucide-react'

import { formatUserRole } from '@/lib/admin-utils'
import { format } from 'date-fns'
import { Skeleton } from '@/components/ui/skeleton'

interface BanDialogState {
  isOpen: boolean
  userId: string
  userName: string
}

interface RoleDialogState {
  isOpen: boolean
  userId: string
  userName: string
  currentRole: string
}

export function UserManagement() {
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [searchField, setSearchField] = useState<'email' | 'name'>('email')
  const [currentPage, setCurrentPage] = useState(1)
  const [usersPerPage, setUsersPerPage] = useState(20)
  const [banDialog, setBanDialog] = useState<BanDialogState>({
    isOpen: false,
    userId: '',
    userName: '',
  })
  const [roleDialog, setRoleDialog] = useState<RoleDialogState>({
    isOpen: false,
    userId: '',
    userName: '',
    currentRole: '',
  })
  const [banReason, setBanReason] = useState('')
  const [banDuration, setBanDuration] = useState<string>('')
  const [newRole, setNewRole] = useState('')
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null)

  // Debounce search term with 600ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
      // Reset to page 1 when search changes
      if (searchTerm !== debouncedSearchTerm) {
        setCurrentPage(1)
      }
    }, 600)

    return () => clearTimeout(timer)
  }, [searchTerm, debouncedSearchTerm])

  // Close any open dropdowns when dialogs open
  useEffect(() => {
    if (banDialog.isOpen || roleDialog.isOpen) {
      setOpenDropdownId(null)
    }
  }, [banDialog.isOpen, roleDialog.isOpen])

  // Cleanup function to handle component unmount
  useEffect(() => {
    return () => {
      setOpenDropdownId(null)
    }
  }, [])

  const {
    data: usersData,
    isLoading,
    error,
    refetch,
  } = useAdminUsers({
    searchValue: debouncedSearchTerm,
    searchField,
    page: currentPage,
    limit: usersPerPage,
  })

  const impersonateUser = useImpersonateUser()
  const { banUser, unbanUser, isBanning, isUnbanning } = useBanUser()
  const setUserRole = useUserRole()

  const handleSearch = (value: string) => {
    setSearchTerm(value)
  }

  const handleImpersonate = (userId: string) => {
    impersonateUser.mutate(userId)
  }

  const handleBanUser = () => {
    const banExpiresIn = banDuration
      ? parseInt(banDuration) * 24 * 60 * 60
      : undefined

    banUser({
      userId: banDialog.userId,
      banReason: banReason || undefined,
      banExpiresIn,
    })

    // Close modal and reset state
    closeBanDialog()
  }

  const handleUnbanUser = (userId: string) => {
    unbanUser(userId)
  }

  const handleSetRole = () => {
    setUserRole.mutate({
      userId: roleDialog.userId,
      role: newRole,
    })

    // Close modal and reset state
    closeRoleDialog()
  }

  // Helper functions to properly close modals and reset state
  const closeBanDialog = () => {
    setBanDialog({ isOpen: false, userId: '', userName: '' })
    setBanReason('')
    setBanDuration('')
    setOpenDropdownId(null) // Close any open dropdown
  }

  const closeRoleDialog = () => {
    setRoleDialog({ isOpen: false, userId: '', userName: '', currentRole: '' })
    setNewRole('')
    setOpenDropdownId(null) // Close any open dropdown
  }

  // Helper function to handle dialog opening with dropdown cleanup
  const openBanDialog = (userId: string, userName: string) => {
    setBanDialog({
      isOpen: true,
      userId,
      userName,
    })
    setOpenDropdownId(null) // Close dropdown when opening dialog
  }

  const openRoleDialog = (
    userId: string,
    userName: string,
    currentRole: string
  ) => {
    setRoleDialog({
      isOpen: true,
      userId,
      userName,
      currentRole,
    })
    setOpenDropdownId(null) // Close dropdown when opening dialog
  }

  const getBadgeVariant = (role?: string) => {
    if (!role) return 'secondary'
    if (role.includes('admin')) return 'destructive'
    if (role.includes('superadmin')) return 'default'
    return 'secondary'
  }

  // Skeleton loader for table rows
  const SkeletonRow = () => (
    <TableRow>
      <TableCell className='w-[40%]'>
        <div>
          <Skeleton className='h-4 w-32 mb-1' />
          <Skeleton className='h-3 w-48' />
        </div>
      </TableCell>
      <TableCell className='w-[15%]'>
        <Skeleton className='h-5 w-16' />
      </TableCell>
      <TableCell className='w-[15%]'>
        <Skeleton className='h-5 w-16' />
      </TableCell>
      <TableCell className='w-[20%]'>
        <Skeleton className='h-4 w-24' />
      </TableCell>
      <TableCell className='w-[10%]'>
        <Skeleton className='h-8 w-8 rounded-full' />
      </TableCell>
    </TableRow>
  )

  // Show loading indicator in search input
  const isSearching = searchTerm !== debouncedSearchTerm

  if (error) {
    return (
      <div className='p-8 text-center'>
        <p className='text-red-600'>Error loading users: {error.message}</p>
        <Button onClick={() => refetch()} className='mt-4'>
          Retry
        </Button>
      </div>
    )
  }

  const users = usersData?.users || []

  return (
    <div className='space-y-6'>
      {/* Search and Filters */}
      <div className='flex items-center gap-4'>
        <div className='relative flex-1'>
          <Search className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
          {isSearching && (
            <Loader2 className='absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 animate-spin text-muted-foreground' />
          )}
          <Input
            placeholder='Search users...'
            value={searchTerm}
            onChange={e => handleSearch(e.target.value)}
            className={`pl-10 ${isSearching ? 'pr-10' : ''}`}
          />
        </div>
        <Select
          value={searchField}
          onValueChange={(value: 'email' | 'name') => setSearchField(value)}
        >
          <SelectTrigger className='w-32'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='email'>Email</SelectItem>
            <SelectItem value='name'>Name</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Users Table */}
      <div className='flex flex-col rounded-md border'>
        {/* Fixed Table Header */}
        <div className='border-b bg-muted/50'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='w-[40%]'>User</TableHead>
                <TableHead className='w-[15%]'>Role</TableHead>
                <TableHead className='w-[15%]'>Status</TableHead>
                <TableHead className='w-[20%]'>Created</TableHead>
                <TableHead className='w-[10%]'>Actions</TableHead>
              </TableRow>
            </TableHeader>
          </Table>
        </div>

        {/* Scrollable Table Body */}
        <div className='max-h-[40vh] min-h-[35vh] overflow-y-auto'>
          <Table>
            <TableBody>
              {isLoading ? (
                // Show skeleton rows while loading
                Array.from({ length: 8 }).map((_, index) => (
                  <SkeletonRow key={index} />
                ))
              ) : users.length === 0 ? (
                // Show empty state
                <TableRow className='h-32'>
                  <TableCell colSpan={5} className='text-center'>
                    <div className='text-muted-foreground'>
                      {debouncedSearchTerm
                        ? 'No users found matching your search.'
                        : 'No users found.'}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                users.map(user => (
                  <TableRow key={user.id}>
                    <TableCell className='w-[40%]'>
                      <div>
                        <div className='font-medium'>{user.name}</div>
                        <div className='text-sm text-muted-foreground'>
                          {user.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className='w-[15%]'>
                      <Badge variant={getBadgeVariant(user.role)}>
                        {formatUserRole(user.role)}
                      </Badge>
                    </TableCell>
                    <TableCell className='w-[15%]'>
                      {user.banned ? (
                        <Badge variant='destructive'>Banned</Badge>
                      ) : (
                        <Badge variant='outline'>Active</Badge>
                      )}
                    </TableCell>
                    <TableCell className='w-[20%]'>
                      {format(new Date(user.createdAt), 'MMM dd, yyyy')}
                    </TableCell>
                    <TableCell className='w-[10%]'>
                      <DropdownMenu
                        open={openDropdownId === user.id}
                        onOpenChange={open => {
                          setOpenDropdownId(open ? user.id : null)
                        }}
                      >
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant='ghost'
                            className='h-8 w-8 p-0'
                            aria-label={`Actions for ${user.name}`}
                          >
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={e => {
                              e.preventDefault()
                              e.stopPropagation()
                              setOpenDropdownId(null)
                              handleImpersonate(user.id)
                            }}
                            disabled={impersonateUser.isPending}
                          >
                            <Crown className='mr-2 h-4 w-4' />
                            Impersonate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={e => {
                              e.preventDefault()
                              e.stopPropagation()
                              openRoleDialog(
                                user.id,
                                user.name,
                                user.role || 'user'
                              )
                            }}
                          >
                            <Shield className='mr-2 h-4 w-4' />
                            Change Role
                          </DropdownMenuItem>
                          {user.banned ? (
                            <DropdownMenuItem
                              onClick={e => {
                                e.preventDefault()
                                e.stopPropagation()
                                setOpenDropdownId(null)
                                handleUnbanUser(user.id)
                              }}
                              disabled={isUnbanning}
                            >
                              <UserCheck className='mr-2 h-4 w-4' />
                              Unban User
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem
                              onClick={e => {
                                e.preventDefault()
                                e.stopPropagation()
                                openBanDialog(user.id, user.name)
                              }}
                              disabled={isBanning}
                            >
                              <UserX className='mr-2 h-4 w-4' />
                              Ban User
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination Controls */}
      {usersData && (
        <div className='flex items-center justify-between px-4'>
          {/* User Count Display */}
          <div className='text-muted-foreground hidden flex-1 text-sm lg:flex'>
            {usersData.users.length === 0 ? (
              debouncedSearchTerm ? (
                'No users found matching your search.'
              ) : (
                'No users found.'
              )
            ) : (
              <>
                Showing{' '}
                {usersData.pagination
                  ? `${usersData.pagination.offset + 1}-${Math.min(
                      usersData.pagination.offset + usersData.users.length,
                      usersData.total || 0
                    )} of ${usersData.total || 0}`
                  : usersData.users.length}{' '}
                users
                {debouncedSearchTerm && (
                  <span className='ml-1'>
                    matching &ldquo;{debouncedSearchTerm}&rdquo;
                  </span>
                )}
              </>
            )}
          </div>

          <div className='flex w-full items-center gap-8 lg:w-fit'>
            {/* Rows per page */}
            <div className='hidden items-center gap-2 lg:flex'>
              <Label htmlFor='rows-per-page' className='text-sm font-medium'>
                Rows per page
              </Label>
              <Select
                value={usersPerPage.toString()}
                onValueChange={value => {
                  const newPageSize = Number(value)
                  setUsersPerPage(newPageSize)
                  // Reset to page 1 when changing page size
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger size='sm' className='w-20' id='rows-per-page'>
                  <SelectValue placeholder={usersPerPage.toString()} />
                </SelectTrigger>
                <SelectContent side='top'>
                  {[10, 20, 30, 50].map(pageSize => (
                    <SelectItem key={pageSize} value={pageSize.toString()}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Page info */}
            {usersData.pagination && usersData.pagination.totalPages > 0 && (
              <div className='flex w-fit items-center justify-center text-sm font-medium'>
                Page {currentPage} of {usersData.pagination.totalPages}
              </div>
            )}

            {/* Navigation buttons */}
            {usersData.pagination && usersData.pagination.totalPages > 1 && (
              <div className='ml-auto flex items-center gap-2 lg:ml-0'>
                {/* First page */}
                <Button
                  variant='outline'
                  className='hidden h-8 w-8 p-0 lg:flex'
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1 || isLoading}
                >
                  <span className='sr-only'>Go to first page</span>
                  <svg className='h-4 w-4' fill='none' viewBox='0 0 24 24'>
                    <path
                      stroke='currentColor'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='m11 9-3 3 3 3m4-6-3 3 3 3'
                    />
                  </svg>
                </Button>

                {/* Previous page */}
                <Button
                  variant='outline'
                  className='h-8 w-8 p-0'
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1 || isLoading}
                >
                  <span className='sr-only'>Go to previous page</span>
                  <svg className='h-4 w-4' fill='none' viewBox='0 0 24 24'>
                    <path
                      stroke='currentColor'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='m15 18-6-6 6-6'
                    />
                  </svg>
                </Button>

                {/* Next page */}
                <Button
                  variant='outline'
                  className='h-8 w-8 p-0'
                  onClick={() =>
                    setCurrentPage(prev =>
                      Math.min(usersData.pagination!.totalPages, prev + 1)
                    )
                  }
                  disabled={
                    currentPage === usersData.pagination.totalPages || isLoading
                  }
                >
                  <span className='sr-only'>Go to next page</span>
                  <svg className='h-4 w-4' fill='none' viewBox='0 0 24 24'>
                    <path
                      stroke='currentColor'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='m9 18 6-6-6-6'
                    />
                  </svg>
                </Button>

                {/* Last page */}
                <Button
                  variant='outline'
                  className='hidden h-8 w-8 p-0 lg:flex'
                  onClick={() =>
                    setCurrentPage(usersData.pagination!.totalPages)
                  }
                  disabled={
                    currentPage === usersData.pagination.totalPages || isLoading
                  }
                >
                  <span className='sr-only'>Go to last page</span>
                  <svg className='h-4 w-4' fill='none' viewBox='0 0 24 24'>
                    <path
                      stroke='currentColor'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='m13 15 3-3-3-3m-4 6 3-3-3-3'
                    />
                  </svg>
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Ban User Dialog */}
      <Dialog
        open={banDialog.isOpen}
        onOpenChange={open => {
          if (!open) {
            closeBanDialog()
          }
        }}
        modal={true}
      >
        <DialogContent
          onInteractOutside={e => {
            // Ensure we properly close the dialog when clicking outside
            e.preventDefault()
            closeBanDialog()
          }}
        >
          <DialogHeader>
            <DialogTitle>Ban User</DialogTitle>
            <DialogDescription>
              Are you sure you want to ban {banDialog.userName}? This will
              prevent them from accessing the application.
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='ban-reason'>Reason (optional)</Label>
              <Textarea
                id='ban-reason'
                placeholder='Enter reason for ban...'
                value={banReason}
                onChange={e => setBanReason(e.target.value)}
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='ban-duration'>Duration (days, optional)</Label>
              <Input
                id='ban-duration'
                type='number'
                placeholder='Leave empty for permanent ban'
                value={banDuration}
                onChange={e => setBanDuration(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant='outline' onClick={closeBanDialog}>
              Cancel
            </Button>
            <Button
              variant='destructive'
              onClick={handleBanUser}
              disabled={isBanning}
            >
              {isBanning ? (
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              ) : null}
              Ban User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Change Role Dialog */}
      <Dialog
        open={roleDialog.isOpen}
        onOpenChange={open => {
          if (!open) {
            closeRoleDialog()
          }
        }}
        modal={true}
      >
        <DialogContent
          onInteractOutside={e => {
            // Ensure we properly close the dialog when clicking outside
            e.preventDefault()
            closeRoleDialog()
          }}
        >
          <DialogHeader>
            <DialogTitle>Change User Role</DialogTitle>
            <DialogDescription>
              Change the role for {roleDialog.userName}. Current role:{' '}
              {formatUserRole(roleDialog.currentRole)}
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='new-role'>New Role</Label>
              <Select value={newRole} onValueChange={setNewRole}>
                <SelectTrigger>
                  <SelectValue placeholder='Select a role' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='user'>User</SelectItem>
                  <SelectItem value='admin'>Admin</SelectItem>
                  <SelectItem value='superadmin'>Super Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant='outline' onClick={closeRoleDialog}>
              Cancel
            </Button>
            <Button
              onClick={handleSetRole}
              disabled={setUserRole.isPending || !newRole}
            >
              {setUserRole.isPending ? (
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              ) : null}
              Update Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
