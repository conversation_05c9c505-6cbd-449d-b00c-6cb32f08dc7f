'use client'

import * as React from 'react'
import { Moon, Sun, Laptop, Check } from 'lucide-react'
import { useTheme } from 'next-themes'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

export function ThemeToggle() {
  const { resolvedTheme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // Only show the theme toggle after mounting to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  const themeOptions = [
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon },
    { value: 'system', label: 'System', icon: Laptop },
  ]

  const currentTheme =
    themeOptions.find(t => {
      if (t.value === resolvedTheme) return true
      if (t.value === 'system' && !resolvedTheme) return true
      return false
    }) || themeOptions[0]

  if (!mounted) {
    return (
      <Button variant='outline' size='icon'>
        <div className='h-5 w-5' />
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='outline' size='icon'>
          <currentTheme.icon className='h-5 w-5' />
          <span className='sr-only'>Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        {themeOptions.map(option => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => setTheme(option.value)}
            className='flex justify-between'
          >
            <div className='flex items-center'>
              <option.icon className='mr-2 h-4 w-4' />
              <span>{option.label}</span>
            </div>
            {currentTheme.value === option.value && (
              <Check className='h-4 w-4 ml-2' />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
