'use client'

import * as React from 'react'
import { useTheme } from 'next-themes'
import { Sun, Moon, Laptop, Check } from 'lucide-react'
import { SidebarMenuButton, useSidebar } from '@/components/ui/sidebar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

const themeOptions = [
  { value: 'light', label: 'Light', icon: Sun },
  { value: 'dark', label: 'Dark', icon: Moon },
  { value: 'system', label: 'System', icon: Laptop },
]

export function ThemeSwitcher() {
  const { resolvedTheme, setTheme } = useTheme()
  const { state } = useSidebar()
  const isCollapsed = state === 'collapsed'
  const [mounted, setMounted] = React.useState(false)

  // Only show the theme toggle after mounting to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Find the current theme option
  const currentTheme = React.useMemo(() => {
    return (
      themeOptions.find(t => {
        if (t.value === resolvedTheme) return true
        if (t.value === 'system' && !resolvedTheme) return true
        return false
      }) || themeOptions[0]
    )
  }, [resolvedTheme])

  // Render a placeholder during SSR to avoid hydration mismatch
  if (!mounted) {
    return (
      <SidebarMenuButton
        size={isCollapsed ? 'sm' : 'default'}
        tooltip={isCollapsed ? 'Theme' : 'Theme'}
        className={cn('flex', isCollapsed ? 'justify-center' : 'justify-start')}
      >
        <div className='size-4' />
        {!isCollapsed && (
          <span className='ml-2 text-sm font-medium'>Theme</span>
        )}
      </SidebarMenuButton>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuButton
          size={isCollapsed ? 'sm' : 'default'}
          tooltip={isCollapsed ? `Theme: ${currentTheme.label}` : 'Theme'}
          className={cn(
            'flex',
            isCollapsed ? 'justify-center' : 'justify-start'
          )}
        >
          <currentTheme.icon className='size-4' />
          {!isCollapsed && (
            <span className='ml-2 text-sm font-medium'>
              {currentTheme.label}
            </span>
          )}
        </SidebarMenuButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start'>
        {themeOptions.map(option => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => setTheme(option.value)}
            className='flex justify-between'
          >
            <div className='flex items-center'>
              <option.icon className='mr-2 h-4 w-4' />
              <span>{option.label}</span>
            </div>
            {currentTheme.value === option.value && (
              <Check className='h-4 w-4 ml-2' />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
