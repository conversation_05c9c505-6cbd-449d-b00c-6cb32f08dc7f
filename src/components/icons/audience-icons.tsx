import React from 'react'

export const GeneralAudienceIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <circle cx='9' cy='7' r='3' fill='#4ECDC4' />
    <circle cx='15' cy='9' r='2.5' fill='#45B7D1' />
    <circle cx='6' cy='14' r='2' fill='#96CEB4' />
    <path d='M1 21v-2c0-2.5 3-4 8-4' fill='#4ECDC4' />
    <path d='M13 21v-1.5c0-2 2.5-3.5 6-3.5' fill='#45B7D1' />
    <path d='M2 21c0-1.5 1.5-3 4-3' fill='#96CEB4' />
  </svg>
)

export const BusinessProfessionalsIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <rect x='6' y='8' width='12' height='10' rx='2' fill='#2C3E50' />
    <rect x='9' y='4' width='6' height='4' rx='1' fill='#34495E' />
    <rect x='10' y='11' width='4' height='1' fill='white' />
    <rect x='10' y='13' width='3' height='1' fill='white' />
    <circle cx='8' cy='12' r='1' fill='#3498DB' />
    <rect x='10' y='2' width='4' height='2' rx='0.5' fill='#E74C3C' />
  </svg>
)

export const StudentsIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <path d='M12 3L2 8L12 13L22 8L12 3Z' fill='#FF6B6B' />
    <path
      d='M2 8V16C2 18 7 20 12 20C17 20 22 18 22 16V8'
      fill='none'
      stroke='#E55A4E'
      strokeWidth='2'
    />
    <circle cx='12' cy='8' r='2' fill='#FFD93D' />
    <rect x='18' y='10' width='2' height='8' fill='#6C5CE7' />
    <circle cx='19' cy='9' r='1' fill='#A29BFE' />
  </svg>
)

export const TechSavvyIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <rect x='2' y='5' width='20' height='12' rx='2' fill='#2D3748' />
    <rect x='4' y='7' width='16' height='8' fill='#4A5568' />
    <rect x='5' y='8' width='14' height='6' fill='#1A202C' />
    <rect x='6' y='9' width='4' height='1' fill='#00D9FF' />
    <rect x='6' y='11' width='6' height='1' fill='#00D9FF' />
    <rect x='6' y='13' width='3' height='1' fill='#00D9FF' />
    <circle cx='17' cy='10' r='1' fill='#FF6B6B' />
    <circle cx='17' cy='12' r='1' fill='#4ECDC4' />
    <rect x='10' y='18' width='4' height='2' fill='#A0AEC0' />
  </svg>
)

export const CreativeProfessionalsIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <ellipse cx='12' cy='16' rx='8' ry='6' fill='#F7F7F7' />
    <circle cx='8' cy='12' r='3' fill='#FF6B6B' />
    <circle cx='16' cy='12' r='3' fill='#4ECDC4' />
    <circle cx='12' cy='8' r='3' fill='#FFE066' />
    <circle cx='6' cy='18' r='2' fill='#A55EEA' />
    <circle cx='18' cy='18' r='2' fill='#FF9F43' />
    <circle cx='12' cy='20' r='2' fill='#26DE81' />
    <rect x='11' y='2' width='2' height='4' fill='#8B4513' />
  </svg>
)

export const EntrepreneursIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <path
      d='M12 2L14 8H20L16 12L18 18L12 15L6 18L8 12L4 8H10L12 2Z'
      fill='#FF6B6B'
    />
    <circle cx='12' cy='10' r='3' fill='#FFE066' />
    <path
      d='M8 15L16 15'
      stroke='#4ECDC4'
      strokeWidth='2'
      strokeLinecap='round'
    />
    <path
      d='M6 18L18 18'
      stroke='#4ECDC4'
      strokeWidth='3'
      strokeLinecap='round'
    />
    <path
      d='M4 21L20 21'
      stroke='#4ECDC4'
      strokeWidth='2'
      strokeLinecap='round'
    />
    <circle cx='12' cy='10' r='1' fill='#FF4757' />
  </svg>
)
