import React from 'react'

export const CasualIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <circle cx='12' cy='12' r='10' fill='#FFB84D' />
    <circle cx='9' cy='10' r='1.5' fill='#333' />
    <circle cx='15' cy='10' r='1.5' fill='#333' />
    <path
      d='M7 14.5c0 2.5 2.5 4.5 5 4.5s5-2 5-4.5'
      stroke='#333'
      strokeWidth='1.5'
      strokeLinecap='round'
      fill='none'
    />
  </svg>
)

export const FormalIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <path
      d='M12 2L15 6H21L16 10L18 16L12 13L6 16L8 10L3 6H9L12 2Z'
      fill='#FFD700'
      stroke='#DAA520'
      strokeWidth='1'
    />
    <circle cx='12' cy='8' r='2' fill='#FFA500' />
  </svg>
)

export const FriendlyIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <path
      d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'
      fill='#FF6B6B'
    />
  </svg>
)

export const ProfessionalIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <rect x='4' y='8' width='16' height='12' rx='2' fill='#4A90E2' />
    <rect x='8' y='4' width='8' height='4' rx='1' fill='#357ABD' />
    <rect x='9' y='11' width='6' height='1' fill='white' />
    <rect x='9' y='13' width='4' height='1' fill='white' />
    <circle cx='7' cy='12' r='1' fill='white' />
  </svg>
)

export const EnthusiasticIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <path
      d='M13 2L15.09 8.26L22 9L17 14L18.18 21L13 18.27L7.82 21L9 14L4 9L10.91 8.26L13 2Z'
      fill='#FFE066'
      stroke='#FFD700'
      strokeWidth='1'
    />
    <path
      d='M13 6L14.5 10.5L19 11L15.5 14.5L16.5 19L13 17L9.5 19L10.5 14.5L7 11L11.5 10.5L13 6Z'
      fill='#FF4757'
    />
  </svg>
)

export const EducationalIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox='0 0 24 24' fill='none'>
    <rect x='3' y='6' width='18' height='12' rx='2' fill='#5F27CD' />
    <rect x='5' y='8' width='14' height='8' fill='white' />
    <rect x='7' y='10' width='10' height='1' fill='#5F27CD' />
    <rect x='7' y='12' width='8' height='1' fill='#5F27CD' />
    <rect x='7' y='14' width='6' height='1' fill='#5F27CD' />
    <circle cx='19' cy='4' r='2' fill='#FF6B6B' />
  </svg>
)
