import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin'
import { ListPlugin } from '@lexical/react/LexicalListPlugin'

import { ContentEditable } from '@/components/editor/editor-ui/content-editable'
import { ToolbarPlugin } from '@/components/editor/plugins/toolbar/toolbar-plugin'
import { FontFormatToolbarPlugin } from '@/components/editor/plugins/toolbar/font-format-toolbar-plugin'
import { HistoryToolbarPlugin } from '@/components/editor/plugins/toolbar/history-toolbar-plugin'
import { BlockFormatDropDown } from '@/components/editor/plugins/toolbar/block-format-toolbar-plugin'
import { FormatParagraph } from '@/components/editor/plugins/toolbar/block-format/format-paragraph'
import { FormatHeading } from '@/components/editor/plugins/toolbar/block-format/format-heading'
import { FormatBulletedList } from '@/components/editor/plugins/toolbar/block-format/format-bulleted-list'
import { FormatNumberedList } from '@/components/editor/plugins/toolbar/block-format/format-numbered-list'
import { FormatQuote } from '@/components/editor/plugins/toolbar/block-format/format-quote'
import { DragDropPastePlugin } from '@/components/editor/plugins/drag-drop-paste-plugin'
import { ImagesPlugin } from '@/components/editor/plugins/images-plugin'
import { Separator } from '@/components/ui/separator'

export function Plugins() {
  return (
    <div className='relative'>
      {/* Toolbar */}
      <ToolbarPlugin>
        {() => (
          <div className='flex items-center gap-1 p-2 border-b border-border bg-muted/30'>
            {/* History Controls */}
            <HistoryToolbarPlugin />

            <Separator orientation='vertical' className='mx-1 h-5' />

            {/* Block Format Controls */}
            <BlockFormatDropDown>
              <FormatParagraph />
              <FormatHeading levels={['h1', 'h2', 'h3']} />
              <FormatBulletedList />
              <FormatNumberedList />
              <FormatQuote />
            </BlockFormatDropDown>

            <Separator orientation='vertical' className='mx-1 h-5' />

            {/* Text Formatting */}
            <FontFormatToolbarPlugin format='bold' />
            <FontFormatToolbarPlugin format='italic' />
            <FontFormatToolbarPlugin format='underline' />
            <FontFormatToolbarPlugin format='strikethrough' />
            <FontFormatToolbarPlugin format='code' />
          </div>
        )}
      </ToolbarPlugin>

      {/* Main Editor */}
      <div className='relative'>
        <RichTextPlugin
          contentEditable={
            <div className='relative'>
              <ContentEditable placeholder='You can copy paste text or write your own content here...' />
            </div>
          }
          ErrorBoundary={LexicalErrorBoundary}
        />

        {/* Core Plugins */}
        <HistoryPlugin />
        <ListPlugin />
        <DragDropPastePlugin />
        <ImagesPlugin />
      </div>
    </div>
  )
}
