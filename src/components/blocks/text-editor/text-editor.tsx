'use client'

import { useState, useEffect } from 'react'
import { SerializedEditorState } from 'lexical'

import { Editor } from '@/components/blocks/editor-00/editor'

interface TextEditorProps {
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  className?: string
}

const initialValue = {
  root: {
    children: [
      {
        children: [],
        direction: 'ltr' as const,
        format: 0,
        indent: 0,
        type: 'paragraph',
        version: 1,
      },
    ],
    direction: 'ltr' as const,
    format: 0,
    indent: 0,
    type: 'root',
    version: 1,
  },
}

// Helper function to extract plain text from SerializedEditorState
function extractPlainText(editorState: SerializedEditorState): string {
  function extractFromNode(node: unknown): string {
    const nodeObj = node as {
      type?: string
      text?: string
      children?: unknown[]
    }

    if (nodeObj.type === 'text') {
      return nodeObj.text || ''
    }

    if (nodeObj.children && Array.isArray(nodeObj.children)) {
      return nodeObj.children.map(extractFromNode).join('')
    }

    return ''
  }

  if (!editorState?.root?.children) {
    return ''
  }

  return editorState.root.children.map(extractFromNode).join('\n').trim()
}

export function TextEditor({
  value,
  onChange,
  disabled = false,
  className,
}: TextEditorProps) {
  const [editorState, setEditorState] = useState<SerializedEditorState>(
    initialValue as unknown as SerializedEditorState
  )

  // Initialize editor with value if provided
  useEffect(() => {
    if (value && value.trim()) {
      // Create a simple editor state from the plain text value
      const textParagraphs = value.split('\n').filter(line => line.trim())

      const editorStateFromText: SerializedEditorState = {
        root: {
          children:
            textParagraphs.length > 0
              ? textParagraphs.map(text => ({
                  children: [
                    {
                      detail: 0,
                      format: 0,
                      mode: 'normal',
                      style: '',
                      text: text.trim(),
                      type: 'text',
                      version: 1,
                    },
                  ],
                  direction: 'ltr',
                  format: 0,
                  indent: 0,
                  type: 'paragraph',
                  version: 1,
                }))
              : initialValue.root.children,
          direction: 'ltr',
          format: 0,
          indent: 0,
          type: 'root',
          version: 1,
        },
      } as unknown as SerializedEditorState

      setEditorState(editorStateFromText)
    }
  }, [value])

  const handleEditorChange = (newEditorState: SerializedEditorState) => {
    setEditorState(newEditorState)

    // Extract plain text and call onChange
    const plainText = extractPlainText(newEditorState)
    onChange(plainText)
  }

  return (
    <div className={className}>
      <div
        className={`relative ${disabled ? 'pointer-events-none opacity-60' : ''}`}
        style={{ minHeight: '440px' }}
      >
        <style jsx global>{`
          .ContentEditable__root {
            min-height: 460px;
            max-height: 500px;
            overflow-y: auto;
            padding: 16px;
            font-size: 14px;
            line-height: 1.5;
          }
        `}</style>
        <Editor
          editorSerializedState={editorState}
          onSerializedChange={handleEditorChange}
        />
      </div>
    </div>
  )
}
