import { projects } from './schema'
import type { InferInsertModel } from 'drizzle-orm'

export const exampleProject: InferInsertModel<typeof projects> = {
  projectId: '550e8400-e29b-41d4-a716-************', // UUID format
  userId: 'user_123456789',
  projectName: '5 places to visit in Delhi',
  method: 'Idea to Video',
  createdAt: new Date(),
  updatedAt: new Date(),
  coverColor: '#FF5733',
  coverPic: 'https://example.com/cover.jpg',
  orientation: 'Landscape',
  duration: '4.533', // as string to match schema
  summary: '',
  speech: {
    enabled: true,
    src: 'https://example.com/speech.mp3',
    name: 'AI Voice',
    volume: 80,
    transcript: {
      captions: [
        {
          start: 0,
          end: 5,
          sentence: 'Welcome to the video!',
          wordBoundries: [
            { start: 0, end: 0.5, word: 'Welcome' },
            { start: 0.6, end: 1.2, word: 'to' },
            { start: 1.3, end: 2.0, word: 'the' },
            { start: 2.1, end: 3.0, word: 'video!' },
          ],
        },
      ],
      status: 'COMPLETED',
    },
  },
  backgroundVideo: {
    src: 'https://example.com/bg.mp4',
    muted: false,
  },
  music: {
    enabled: true,
    src: 'https://example.com/music.mp3',
    volume: 60,
    duration: 120,
    name: 'Background Track',
  },
  captionSettings: {
    enabled: true,
    fontFamily: 'Arial',
    fontSize: 18,
    fontWeight: 'normal',
    fontStyle: 'normal',
    textColor: '#FFFFFF',
    highlightColor: '#FFEB3B',
    backgroundColor: '#000000',
    backgroundOpacity: 60,
    textAlign: 'center',
    textShadow: true,
    borderRadius: 8,
    padding: 16,
    maxWidth: 90,
    animation: 'fade',
  },
  scenes: [],
}

// Type assertion to ensure the example matches the schema
export type ExampleProject = typeof exampleProject
