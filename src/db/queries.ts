import { db } from '@/lib/db'
import { projects } from './schema'
import { exampleProject } from './example-projects'
import { eq, desc, count, and } from 'drizzle-orm'

// Insert a new project
export const insertProject = async () => {
  try {
    const result = await db.insert(projects).values(exampleProject).returning()
    return result
  } catch (error) {
    console.error('Error inserting project:', error)
    throw error
  }
}

// Get all projects with limit and user filtering
export const getProjects = async (userId: string, limit = 10, offset = 0) => {
  try {
    const result = await db
      .select({
        projectId: projects.projectId,
        projectName: projects.projectName,
        method: projects.method,
        updatedAt: projects.updatedAt,
        createdAt: projects.createdAt,
        coverColor: projects.coverColor,
        coverPic: projects.coverPic,
        orientation: projects.orientation,
        duration: projects.duration,
      })
      .from(projects)
      .where(eq(projects.userId, userId))
      .orderBy(desc(projects.updatedAt))
      .limit(limit)
      .offset(offset)
    return result
  } catch (error) {
    console.error('Error fetching projects:', error)
    throw error
  }
}

// Get total count of projects for a user
export const getProjectsCount = async (userId: string) => {
  try {
    const result = await db
      .select({ count: count() })
      .from(projects)
      .where(eq(projects.userId, userId))
    return result[0]?.count || 0
  } catch (error) {
    console.error('Error fetching projects count:', error)
    throw error
  }
}

// Get project by ID
export const getProjectById = async (projectId: string) => {
  try {
    const result = await db
      .select()
      .from(projects)
      .where(eq(projects.projectId, projectId))
    return result[0]
  } catch (error) {
    console.error('Error fetching project by ID:', error)
    throw error
  }
}

// Delete project by ID
export const deleteProjectById = async (projectId: string, userId: string) => {
  try {
    const result = await db
      .delete(projects)
      .where(
        and(eq(projects.projectId, projectId), eq(projects.userId, userId))
      )
      .returning()
    return result[0]
  } catch (error) {
    console.error('Error deleting project:', error)
    throw error
  }
}
