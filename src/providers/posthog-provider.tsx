'use client'

import React from 'react'
import posthog from 'posthog-js'
import { authClient } from '@/lib/auth-client'

/**
 * PostHog provider that handles user identification on app startup
 * and manages PostHog state throughout the user session
 */
export function PostHogProvider({ children }: { children: React.ReactNode }) {
  const { data: session, isPending: sessionLoading } = authClient.useSession()
  const [hasIdentified, setHasIdentified] = React.useState(false)

  // Handle user identification when session is available
  React.useEffect(() => {
    // Skip if session is still loading or PostHog is not initialized
    if (sessionLoading || typeof window === 'undefined') return

    // Check if PostHog is initialized (only in production)
    const isPostHogInitialized = posthog.__loaded

    if (!isPostHogInitialized) {
      // PostHog is not initialized (likely in development), skip identification
      return
    }

    // If user is authenticated and we haven't identified them yet
    if (session?.user?.id && !hasIdentified) {
      try {
        posthog.identify(session.user.id, {
          email: session.user.email,
          name: session.user.name,
        })
        setHasIdentified(true)
        console.log('PostHog: User identified on startup', session.user.id)
      } catch (error) {
        console.error('PostHog: Failed to identify user on startup', error)
        // Don't throw error to avoid breaking the app
      }
    }

    // If user is not authenticated and we had previously identified them
    if (!session?.user?.id && hasIdentified) {
      try {
        posthog.reset()
        setHasIdentified(false)
        console.log('PostHog: User reset (logged out)')
      } catch (error) {
        console.error('PostHog: Failed to reset user', error)
      }
    }
  }, [session, sessionLoading, hasIdentified])

  // This provider doesn't render anything, just manages PostHog state
  return <>{children}</>
}
