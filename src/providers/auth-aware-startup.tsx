'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { StartupLoaderProvider } from '@/providers/startup-loader-provider'

export function AuthAwareStartup({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const isAuthRoute = React.useMemo(() => {
    if (!pathname) return false
    return (
      pathname.startsWith('/signin') ||
      pathname.startsWith('/signup') ||
      pathname.startsWith('/forgot-password') ||
      pathname.startsWith('/reset-password') ||
      pathname.startsWith('/accept-invitation') ||
      pathname.startsWith('/verify-email') ||
      pathname.startsWith('/api') ||
      pathname.startsWith('/_next')
    )
  }, [pathname])

  // Do not show StartupLoader on scene editor; it causes visual flash after hard reloads
  const isSceneEditor = React.useMemo(() => {
    if (!pathname) return false
    return pathname.startsWith('/scene-editor')
  }, [pathname])

  if (isAuthRoute || isSceneEditor) {
    return <>{children}</>
  }

  return <StartupLoaderProvider>{children}</StartupLoaderProvider>
}
