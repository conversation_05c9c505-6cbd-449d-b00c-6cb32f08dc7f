'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useState } from 'react'

/**
 * Optimized stale times for different query types
 */
const QUERY_STALE_TIMES = {
  // Static data that rarely changes
  fonts: 24 * 60 * 60 * 1000, // 24 hours
  voices: 60 * 60 * 1000, // 1 hour

  // Search results that can be cached for moderate time
  'podcast-search': 30 * 60 * 1000, // 30 minutes
  'podcast-rss': 60 * 60 * 1000, // 1 hour
  'podcast-episodes': 10 * 60 * 1000, // 10 minutes
  music: 30 * 60 * 1000, // 30 minutes

  // Media search results
  'pexels-images': 15 * 60 * 1000, // 15 minutes
  'pexels-videos': 15 * 60 * 1000, // 15 minutes
  'pixabay-images': 15 * 60 * 1000, // 15 minutes
  'unsplash-images': 15 * 60 * 1000, // 15 minutes

  // User data that changes more frequently
  projects: 5 * 60 * 1000, // 5 minutes
  'media-assets': 10 * 60 * 1000, // 10 minutes

  // YouTube data
  'youtube-connections': 5 * 60 * 1000, // 5 minutes
  'youtube-playlists': 5 * 60 * 1000, // 5 minutes

  // Real-time data
  'video-status': 0, // No stale time
  'speech-generation': 0, // No stale time
} as const

/**
 * Get optimized stale time based on query key
 */
function getStaleTime(queryKey: readonly unknown[]): number {
  if (!Array.isArray(queryKey) || queryKey.length === 0) {
    return 60 * 1000 // Default 1 minute
  }

  const key = queryKey[0] as string
  return QUERY_STALE_TIMES[key as keyof typeof QUERY_STALE_TIMES] || 60 * 1000
}

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Dynamic stale time based on query key
            staleTime: ({ queryKey }) => getStaleTime(queryKey),
            // Optimized garbage collection time (2x max stale time)
            gcTime: 48 * 60 * 60 * 1000, // 48 hours (2x max stale time)
            // Retry configuration
            retry: (failureCount, error) => {
              // Don't retry on 4xx errors
              if (error instanceof Error && error.message.includes('4')) {
                return false
              }

              // Don't retry CONNECTION_REVOKED errors
              if (
                error instanceof Error &&
                error.message === 'CONNECTION_REVOKED'
              ) {
                return false
              }

              // Don't retry on specific HTTP status codes
              if (error instanceof Error) {
                const errorMessage = error.message.toLowerCase()
                // Don't retry on 410 Gone (connection revoked), 401 Unauthorized, 403 Forbidden
                if (
                  errorMessage.includes('410') ||
                  errorMessage.includes('401') ||
                  errorMessage.includes('403')
                ) {
                  return false
                }
              }

              // Retry up to 2 times for other errors
              return failureCount < 2
            },
            retryDelay: attemptIndex =>
              Math.min(1000 * 2 ** attemptIndex, 30000),
            // Background refetch settings
            refetchOnWindowFocus: true,
            refetchOnReconnect: true,
            refetchOnMount: true,
            // Network mode
            networkMode: 'online',
          },
          mutations: {
            retry: 1,
            networkMode: 'online',
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}
    </QueryClientProvider>
  )
}
