'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { Crisp } from 'crisp-sdk-web'
import { authClient } from '@/lib/auth-client'
import { usePlanLimits } from '@/hooks/use-feature-gating'

const CRISP_WEBSITE_ID = '77c4b10d-1fa8-422b-a5cf-372c2a3b29cb'

// Check if we're in a production environment (similar to PostHog logic)
const isProduction = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return false

  const host = window.location.host
  const isLocalhost =
    host.includes('localhost') ||
    host.includes('127.0.0.1') ||
    host.includes('0.0.0.0') ||
    host.includes('::1') ||
    host.startsWith('192.168.') ||
    host.startsWith('10.') ||
    host.startsWith('172.')

  // Also check for common development domains
  const isDevDomain =
    host.includes('.staging') ||
    host.includes('.dev') ||
    (host.includes('vercel.app') && !host.includes('adoriai.com'))

  return !isLocalhost && !isDevDomain
}

/**
 * Crisp customer support chat provider that integrates with our authentication system
 * and manages chat state throughout the user session
 */
export function CrispProvider({ children }: { children: React.ReactNode }) {
  const { data: session, isPending: sessionLoading } = authClient.useSession()
  const { data: activeOrganization } = authClient.useActiveOrganization()
  const { planName } = usePlanLimits()
  const pathname = usePathname()
  const [isInitialized, setIsInitialized] = React.useState(false)

  // Check if current page is an auth page
  const isAuthPage = React.useMemo(() => {
    if (!pathname) return false
    return (
      pathname.startsWith('/signin') ||
      pathname.startsWith('/signup') ||
      pathname.startsWith('/forgot-password') ||
      pathname.startsWith('/reset-password') ||
      pathname.startsWith('/accept-invitation') ||
      pathname.startsWith('/verify-email')
    )
  }, [pathname])

  // Determine if Crisp should be available
  const shouldInitializeCrisp = React.useMemo(() => {
    return isProduction() && !isAuthPage && !!session?.user
  }, [isAuthPage, session?.user])

  // Initialize Crisp only in production for authenticated users not on auth pages
  React.useEffect(() => {
    if (
      typeof window === 'undefined' ||
      isInitialized ||
      !shouldInitializeCrisp
    )
      return

    try {
      // Configure Crisp with manual loading
      Crisp.configure(CRISP_WEBSITE_ID, {
        autoload: false, // Manual loading to control when chat appears
        sessionMerge: true, // Enable session continuity
      })

      // Load Crisp
      Crisp.load()
      setIsInitialized(true)
      console.log('Crisp: Initialized successfully')
    } catch (error) {
      console.error('Crisp: Failed to initialize', error)
      // Don't throw error to avoid breaking the app
    }
  }, [isInitialized, shouldInitializeCrisp])

  // Handle user authentication and identity management
  React.useEffect(() => {
    if (!isInitialized || sessionLoading || typeof window === 'undefined')
      return

    try {
      if (session?.user) {
        // Set user identity when authenticated
        if (session.user.email) {
          Crisp.user.setEmail(session.user.email)
        }

        if (session.user.name) {
          Crisp.user.setNickname(session.user.name)
        }

        // Set session token for continuity
        if (session.user.id) {
          Crisp.setTokenId(session.user.id)
        }

        // Add user metadata with proper typing
        const userData: Record<string, string | boolean | number> = {
          user_id: session.user.id,
          authenticated: true,
          plan: planName, // Add current plan information for support agents
        }

        // Add organization data if available
        if (activeOrganization) {
          userData.organization_id = activeOrganization.id
          userData.organization_name = activeOrganization.name
        }

        Crisp.session.setData(userData)

        console.log(
          'Crisp: User identity set',
          session.user.email,
          'Plan:',
          planName
        )
      } else {
        // Clear user data when not authenticated
        Crisp.user.setEmail('')
        Crisp.user.setNickname('')
        Crisp.setTokenId()

        // Set anonymous user data
        Crisp.session.setData({
          authenticated: false,
        })

        console.log('Crisp: User identity cleared (anonymous)')
      }
    } catch (error) {
      console.error('Crisp: Failed to set user identity', error)
      // Don't throw error to avoid breaking the app
    }
  }, [isInitialized, session, sessionLoading, activeOrganization, planName])

  // This provider doesn't render anything, just manages Crisp state
  return <>{children}</>
}
