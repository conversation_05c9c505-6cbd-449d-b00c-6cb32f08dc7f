'use client'

import { useQueryPrefetch } from '@/hooks/use-query-prefetch'

/**
 * Provider component that initializes query prefetching
 * Add this to your root layout to enable automatic prefetching
 */
export function PrefetchProvider({ children }: { children: React.ReactNode }) {
  // Initialize prefetching
  useQueryPrefetch()
  
  // This provider doesn't render anything, just initializes prefetching
  return <>{children}</>
}
