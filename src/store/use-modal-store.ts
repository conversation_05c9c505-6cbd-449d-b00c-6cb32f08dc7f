import { create } from 'zustand'
import { ModalType, ModalData } from '@/types/modal'

interface ModalStore {
  type: ModalType | null
  data: ModalData
  isOpen: boolean
  onClose: () => void
  onOpen: (type: ModalType, data?: ModalData) => void
  onUpdate: (data: Partial<ModalData>) => void // Add ability to update modal data
}

export const useModalStore = create<ModalStore>(set => ({
  type: null,
  data: {},
  isOpen: false,
  onClose: () => set({ isOpen: false, type: null, data: {} }),
  onOpen: (type, data = {}) => set({ isOpen: true, type, data }),
  onUpdate: newData => set(state => ({ data: { ...state.data, ...newData } })),
}))
