import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface CaptionStyle {
  id: string
  name: string
  fontFamily: string
  fontSize: number
  fontWeight: 'normal' | 'bold'
  fontStyle: 'normal' | 'italic'
  textColor: string
  highlightColor: string
  backgroundColor: string
  backgroundOpacity: number
  animation:
    | 'none'
    | 'fade'
    | 'slide-up'
    | 'bounce'
    | 'typewriter'
    | 'color-up'
    | 'bounce-out'
  textAlign: 'left' | 'center' | 'right'
  textShadow: boolean
  borderRadius: number
  padding: number
  maxWidth: number
  isCustom: boolean
  createdAt: string
  // Additional properties for predefined styles
  preview?: string
  previewBg?: string
  isActive?: boolean
}

// Live preview style for custom caption editing
export type LiveCaptionStyle = Omit<
  CaptionStyle,
  'id' | 'name' | 'isCustom' | 'createdAt'
>

// Default custom style that gets loaded from localStorage
const DEFAULT_CUSTOM_STYLE: LiveCaptionStyle = {
  fontFamily: 'Inter',
  fontSize: 50,
  fontWeight: 'bold',
  fontStyle: 'normal',
  textColor: '#ffffff',
  highlightColor: '#3b82f6',
  backgroundColor: '#000000',
  backgroundOpacity: 60,
  animation: 'none',
  textAlign: 'center',
  textShadow: true,
  borderRadius: 8,
  padding: 16,
  maxWidth: 90,
}

// Predefined subtitle styles
const PREDEFINED_STYLES: CaptionStyle[] = [
  {
    id: 'classic',
    name: 'Classic',
    preview: 'start',
    previewBg: 'blue',
    fontFamily: 'Inter',
    fontSize: 48,
    fontWeight: 'bold',
    fontStyle: 'normal',
    textColor: '#ffffff',
    highlightColor: '#3b82f6',
    backgroundColor: 'rgba(0,0,0,0.6)',
    backgroundOpacity: 60,
    animation: 'none',
    textAlign: 'center',
    textShadow: true,
    borderRadius: 8,
    padding: 16,
    maxWidth: 90,
    isActive: false,
    isCustom: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: 'hustle',
    name: 'Hustle',
    preview: 'start',
    previewBg: 'red',
    fontFamily: 'Inter',
    fontSize: 52,
    fontWeight: 'bold',
    fontStyle: 'normal',
    textColor: '#ffffff',
    highlightColor: '#ef4444',
    backgroundColor: 'rgba(0,0,0,0.8)',
    backgroundOpacity: 80,
    animation: 'bounce',
    textAlign: 'center',
    textShadow: true,
    borderRadius: 8,
    padding: 16,
    maxWidth: 90,
    isActive: true,
    isCustom: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: 'medusa',
    name: 'Medusa',
    preview: 'start',
    previewBg: 'green',
    fontFamily: 'Inter',
    fontSize: 46,
    fontWeight: 'bold',
    fontStyle: 'normal',
    textColor: '#ffffff',
    highlightColor: '#22c55e',
    backgroundColor: 'rgba(0,0,0,0.7)',
    backgroundOpacity: 70,
    animation: 'slide-up',
    textAlign: 'center',
    textShadow: true,
    borderRadius: 8,
    padding: 16,
    maxWidth: 90,
    isActive: false,
    isCustom: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: 'modern',
    name: 'Modern',
    preview: 'start',
    previewBg: 'purple',
    fontFamily: 'Inter',
    fontSize: 44,
    fontWeight: 'normal',
    fontStyle: 'normal',
    textColor: '#ffffff',
    highlightColor: '#8b5cf6',
    backgroundColor: 'rgba(0,0,0,0.5)',
    backgroundOpacity: 50,
    animation: 'fade',
    textAlign: 'center',
    textShadow: true,
    borderRadius: 8,
    padding: 16,
    maxWidth: 90,
    isActive: false,
    isCustom: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: 'minimal',
    name: 'Minimal',
    preview: 'start',
    previewBg: 'gray',
    fontFamily: 'Inter',
    fontSize: 40,
    fontWeight: 'normal',
    fontStyle: 'normal',
    textColor: '#ffffff',
    highlightColor: '#6b7280',
    backgroundColor: 'transparent',
    backgroundOpacity: 0,
    animation: 'typewriter',
    textAlign: 'center',
    textShadow: false,
    borderRadius: 8,
    padding: 16,
    maxWidth: 90,
    isActive: false,
    isCustom: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: 'bold',
    name: 'Bold',
    preview: 'start',
    previewBg: 'orange',
    fontFamily: 'Inter',
    fontSize: 56,
    fontWeight: 'bold',
    fontStyle: 'normal',
    textColor: '#ffffff',
    highlightColor: '#f97316',
    backgroundColor: 'rgba(0,0,0,0.9)',
    backgroundOpacity: 90,
    animation: 'bounce-out',
    textAlign: 'center',
    textShadow: true,
    borderRadius: 8,
    padding: 16,
    maxWidth: 90,
    isActive: false,
    isCustom: false,
    createdAt: new Date().toISOString(),
  },
]

interface CaptionStylesState {
  // State
  predefinedStyles: CaptionStyle[]
  customStyles: CaptionStyle[]
  selectedStyleId: string

  // Live preview state for custom editing
  liveCustomStyle: LiveCaptionStyle
  isLivePreviewActive: boolean

  // Actions
  selectStyle: (styleId: string) => void
  addCustomStyle: (
    style: Omit<CaptionStyle, 'id' | 'isCustom' | 'createdAt'>
  ) => string
  removeCustomStyle: (styleId: string) => void
  updateCustomStyle: (styleId: string, updates: Partial<CaptionStyle>) => void

  // Live preview actions
  updateLiveCustomStyle: (updates: Partial<LiveCaptionStyle>) => void
  setLivePreviewActive: (active: boolean) => void
  resetLiveCustomStyle: () => void
  applyLiveStyleAsCustom: (name: string) => string

  // Getters
  getAllStyles: () => CaptionStyle[]
  getSelectedStyle: () => CaptionStyle | undefined
  getStyleById: (id: string) => CaptionStyle | undefined
  getEffectiveStyle: () => CaptionStyle | undefined // Returns live style when active, otherwise selected style
}

export const useCaptionStylesStore = create<CaptionStylesState>()(
  persist(
    (set, get) => ({
      // Initial state
      predefinedStyles: PREDEFINED_STYLES,
      customStyles: [],
      selectedStyleId: 'classic',

      // Live preview state
      liveCustomStyle: DEFAULT_CUSTOM_STYLE,
      isLivePreviewActive: false,

      // Actions
      selectStyle: (styleId: string) => {
        set({
          selectedStyleId: styleId,
          isLivePreviewActive: false, // Disable live preview when selecting a different style
        })
      },

      addCustomStyle: styleData => {
        const newStyle: CaptionStyle = {
          ...styleData,
          id: `custom-${Date.now()}`,
          isCustom: true,
          createdAt: new Date().toISOString(),
        }

        set(state => ({
          customStyles: [newStyle, ...state.customStyles],
        }))

        return newStyle.id
      },

      removeCustomStyle: (styleId: string) => {
        set(state => ({
          customStyles: state.customStyles.filter(
            style => style.id !== styleId
          ),
          // If the deleted style was selected, fallback to default
          selectedStyleId:
            state.selectedStyleId === styleId
              ? 'classic'
              : state.selectedStyleId,
        }))
      },

      updateCustomStyle: (styleId: string, updates: Partial<CaptionStyle>) => {
        set(state => ({
          customStyles: state.customStyles.map(style =>
            style.id === styleId ? { ...style, ...updates } : style
          ),
        }))
      },

      // Live preview actions
      updateLiveCustomStyle: (updates: Partial<LiveCaptionStyle>) => {
        set(state => ({
          liveCustomStyle: { ...state.liveCustomStyle, ...updates },
          isLivePreviewActive: true,
          selectedStyleId: 'custom-preview', // Special ID for live preview
        }))
      },

      setLivePreviewActive: (active: boolean) => {
        set(state => {
          if (!active) {
            // When disabling live preview, revert to previously selected style
            const previousStyleId =
              state.selectedStyleId === 'custom-preview'
                ? 'classic'
                : state.selectedStyleId
            return {
              isLivePreviewActive: false,
              selectedStyleId: previousStyleId,
            }
          }
          return { isLivePreviewActive: active }
        })
      },

      resetLiveCustomStyle: () => {
        set({
          liveCustomStyle: DEFAULT_CUSTOM_STYLE,
          isLivePreviewActive: false,
        })
      },

      applyLiveStyleAsCustom: (name: string) => {
        const { liveCustomStyle, addCustomStyle } = get()
        const styleId = addCustomStyle({
          ...liveCustomStyle,
          name: name.trim(),
        })

        set({
          selectedStyleId: styleId,
          isLivePreviewActive: false,
        })

        return styleId
      },

      // Getters
      getAllStyles: () => {
        const { customStyles, predefinedStyles } = get()
        return [...customStyles, ...predefinedStyles]
      },

      getSelectedStyle: () => {
        const { selectedStyleId } = get()
        return get().getStyleById(selectedStyleId)
      },

      getStyleById: (id: string) => {
        const { customStyles, predefinedStyles } = get()
        return [...customStyles, ...predefinedStyles].find(
          style => style.id === id
        )
      },

      getEffectiveStyle: () => {
        const { isLivePreviewActive, liveCustomStyle, selectedStyleId } = get()

        if (isLivePreviewActive || selectedStyleId === 'custom-preview') {
          // Return live custom style as a CaptionStyle object
          return {
            id: 'custom-preview',
            name: 'Live Preview',
            isCustom: true,
            createdAt: new Date().toISOString(),
            ...liveCustomStyle,
          }
        }

        return get().getSelectedStyle()
      },
    }),
    {
      name: 'caption-styles-storage', // name of the item in localStorage
      // Persist custom styles, selected style, and live custom style settings
      partialize: state => ({
        customStyles: state.customStyles,
        selectedStyleId: state.selectedStyleId,
        liveCustomStyle: state.liveCustomStyle, // Persist live custom style settings
      }),
    }
  )
)

// Initialize live preview state if custom-preview is selected
if (typeof window !== 'undefined') {
  const store = useCaptionStylesStore.getState()
  if (store.selectedStyleId === 'custom-preview') {
    useCaptionStylesStore.setState({ isLivePreviewActive: true })
  }
}
