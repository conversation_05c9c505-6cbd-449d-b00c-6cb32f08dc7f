name: Vercel Preview Deployment

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
    branches:
      - dev
  # Explicitly exclude pull request events to prevent preview deployments on PRs
  # Only deploy when commits are pushed directly to dev or when PRs are merged into dev

jobs:
  Deploy-Preview:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log deployment context
        run: |
          echo "🚀 Deploying preview for dev branch"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Event: ${{ github.event_name }}"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      # ✅ Install Bun so `bun.lockb` can be used
      - name: Install Bun
        run: |
          curl -fsSL https://bun.sh/install | bash
          echo "$HOME/.bun/bin" >> $GITHUB_PATH
          export PATH="$HOME/.bun/bin:$PATH"
          bun --version

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy Project Artifacts to Vercel (Preview)
        run: |
          echo "🌐 Deploying to Vercel preview environment..."
          DEPLOYMENT_URL=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }})
          echo "✅ Preview deployment successful!"
          echo "🔗 Preview URL: $DEPLOYMENT_URL"
