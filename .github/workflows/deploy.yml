# This workflow will trigger a Vercel production deployment ONLY when a new release tag is pushed.
# It will NOT deploy on regular commits or merges to the main branch.
#
# You need to add the following secrets to your GitHub repository:
# - VERCEL_ORG_ID: Your Vercel organization ID.
# - VERCEL_PROJECT_ID: Your Vercel project ID.
# - VERCEL_TOKEN: Your Vercel authentication token.
#
# You can find your VERCEL_ORG_ID and VERCEL_PROJECT_ID in the .vercel/project.json
# file after linking your project with `vercel link`.
# You can create a Vercel token at https://vercel.com/account/tokens

name: Vercel Production Deployment

permissions:
  contents: read

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
    tags:
      - 'v*.*.*' # Deploy on semantic version tags like v1.0.0, v2.1.3, etc.
      - 'v*.*.*-*' # Also support pre-release tags like v1.0.0-beta.1
      - '*.*.*' # Also support tags without v prefix like 1.0.0, 2.1.3, etc.
      - '*.*.*-*' # Also support pre-release tags without v prefix like 1.0.0-beta.1

jobs:
  Deploy-Production:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Extract version from tag
        id: version
        run: |
          VERSION=${GITHUB_REF#refs/tags/}
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "🏷️ Deploying version: $VERSION"

      - name: Log deployment context
        run: |
          echo "🚀 Deploying to PRODUCTION"
          echo "Tag: ${{ steps.version.outputs.version }}"
          echo "Commit: ${{ github.sha }}"
          echo "Event: ${{ github.event_name }}"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      # ✅ Install Bun so `bun.lockb` can be used
      - name: Install Bun
        run: |
          curl -fsSL https://bun.sh/install | bash
          echo "$HOME/.bun/bin" >> $GITHUB_PATH
          export PATH="$HOME/.bun/bin:$PATH"
          bun --version

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy Project Artifacts to Vercel (Production)
        run: |
          echo "🌐 Deploying to Vercel production environment..."
          DEPLOYMENT_URL=$(vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }})
          echo "✅ Production deployment successful!"
          echo "🔗 Production URL: $DEPLOYMENT_URL"
          echo "🏷️ Version: ${{ steps.version.outputs.version }}"
