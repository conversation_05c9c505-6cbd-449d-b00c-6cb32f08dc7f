{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.tabSize": 2, "editor.insertSpaces": true, "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "[markdown]": {"files.trimTrailingWhitespace": false}, "restoreTerminals.terminals": [{"splitTerminals": [{"name": "nextjs dev server", "commands": ["yarn dev"]}]}, {"splitTerminals": [{"name": "drizzle studio", "commands": ["yarn db:studio"]}]}, {"splitTerminals": [{"name": "inngest dev server", "commands": ["yarn inngest:dev"]}]}, {"splitTerminals": [{"name": "migration", "commands": ["yarn db:push"], "shouldRunCommands": false}]}]}