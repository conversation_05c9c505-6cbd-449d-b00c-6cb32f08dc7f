# Email Verification Setup with AWS SES

This document explains how to set up email verification and password reset functionality using AWS SES (Simple Email Service) for the Adori AI application.

## Environment Variables Required

Add the following environment variables to your `.env` file:

# AWS SES Configuration

SES_ACCESS_ID=<YOUR_AWS_ACCESS_KEY_ID>
SES_ACCESS_KEY=<YOUR_AWS_SECRET_ACCESS_KEY>
AWS_REGION=us-east-1

# Email Sender Configuration

EMAIL_SENDER_ADDRESS=<EMAIL>

## 🎨 **Email Branding Setup**

### **Display Name Configuration:**

- ✅ **Automatically configured**: Emails will show "Adori AI" instead of "<EMAIL>"
- The system formats the sender as: `Adori AI <<EMAIL>>`

### **Brand Logo Setup (BIMI):**

To show your brand logo in email clients like Gmail, you need to set up BIMI (Brand Indicators for Message Identification):

1. **Create a square logo** (minimum 32x32px, recommended 196x196px)
2. **Upload to /public static folder**: `https://app.adoriai.com/adori-logo-light.svg`
3. **Add DNS record**:
   ```
   Type: TXT
   Name: default._bimi
   Value: "v=BIMI1; l=https://app.adoriai.com/adori-logo-light.svg; a=https://app.adoriai.com/adori-logo-light.svg;"
   ```

**Note**: BIMI is supported by Gmail, Yahoo, and some other providers. The logo will appear next to your brand name in the inbox.

## AWS SES Setup Requirements

### 1. Verify Your Domain or Email Address

Before sending emails, you need to verify your domain or email address in AWS SES:

1. Go to AWS SES Console
2. Navigate to "Verified identities"
3. Click "Create identity"
4. Choose "Domain" or "Email address"
5. Follow the verification process

### 2. Request Production Access (if needed)

By default, AWS SES is in sandbox mode and can only send to verified email addresses. To send to any email address:

1. Go to AWS SES Console
2. Navigate to "Account dashboard"
3. Click "Request production access"
4. Fill out the form explaining your use case

### 3. IAM Permissions

Ensure your AWS credentials have the following SES permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["ses:SendEmail", "ses:SendRawEmail"],
      "Resource": "*"
    }
  ]
}
```

## Testing the Email Functionality

### Test API Endpoint

You can test the email functionality using the test API endpoint:

```bash
# Test email verification
curl -X POST http://localhost:3000/api/test-email \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "type": "verification"}'

# Test password reset
curl -X POST http://localhost:3000/api/test-email \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "type": "reset"}'

# Test welcome email
curl -X POST http://localhost:3000/api/test-email \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "type": "welcome"}'

### Manual Testing

1. Start your development server: `npm run dev`
2. Navigate to the signup page
3. Create a new account with a verified email address
4. Check your email for the verification link
5. Test password reset by clicking "Forgot password" on the signin page

## Email Templates

The application includes two email templates:

### 1. Email Verification Template

- **File**: `src/lib/email.ts` - `createVerificationEmailTemplate()`
- **Purpose**: Sent when users sign up to verify their email address
- **Features**:
  - Professional HTML design with Adori AI branding
  - Responsive layout
  - Clear call-to-action button
  - Fallback text version

### 2. Password Reset Template

- **File**: `src/lib/email.ts` - `createPasswordResetEmailTemplate()`
- **Purpose**: Sent when users request a password reset
- **Features**:
  - Security-focused design
  - Warning about link expiration
  - Professional styling consistent with verification emails

### 3. Welcome Email Template

- **File**: `src/lib/email.ts` - `createWelcomeEmailTemplate()`
- **Purpose**: Sent automatically after successful email verification
- **Features**:
  - Welcoming design with emojis and engaging content
  - Feature highlights and quick start guide
  - Call-to-action button to start creating videos
  - Professional styling with feature boxes

## Implementation Details

### Email Service (`src/lib/email.ts`)

- Uses AWS SES SDK v3
- Supports both HTML and text email formats
- Includes error handling and logging
- Configurable sender address

### Auth Integration (`src/lib/auth.ts`)

- Integrated with Better Auth
- Automatic email sending on signup and password reset requests
- Email verification required for login
- Password reset functionality with secure tokens

### Security Features

- Email verification required before login
- Password reset tokens expire after 1 hour
- Email verification tokens expire after 24 hours
- Secure token generation and validation
- Logging of password reset events for security monitoring

## Troubleshooting

### Common Issues

1. **"Email not verified" error**
   - Ensure the sender email address is verified in AWS SES
   - Check that `EMAIL_SENDER_ADDRESS` is set correctly

2. **"Access denied" error**
   - Verify AWS credentials are correct
   - Ensure IAM permissions include SES send permissions
   - Check that the AWS region is correct

3. **Emails not received**
   - Check spam/junk folder
   - Verify recipient email address is correct
   - Ensure AWS SES is out of sandbox mode or recipient is verified

4. **Template rendering issues**
   - Check browser console for HTML errors
   - Verify email client supports HTML emails
   - Test with plain text version

### Debug Mode

Enable debug logging by checking the console for:

- Email send success/failure messages
- AWS SES response details
- Template generation logs

## Production Considerations

1. **Domain Verification**: Verify your domain in AWS SES for better deliverability
2. **SPF/DKIM Records**: Set up proper DNS records for your domain
3. **Monitoring**: Set up CloudWatch alarms for email sending metrics
4. **Rate Limits**: Be aware of AWS SES sending limits
5. **Bounce/Complaint Handling**: Implement proper handling for bounced emails and complaints

## Support

For issues with:

- **AWS SES**: Check AWS SES documentation and support
- **Email Templates**: Modify templates in `src/lib/email.ts`
- **Auth Integration**: Refer to Better Auth documentation
- **Application Issues**: Check application logs and error messages
```
