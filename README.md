# Adori AI - Video Creation Platform

Adori AI is a modern web application that converts text, blogs, ideas, and audio into engaging videos using AI technology.

## Table of Contents

- [Adori AI - Video Creation Platform](#adori-ai---video-creation-platform)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
  - [Tech Stack](#tech-stack)
  - [Project Structure](#project-structure)
  - [Setup Instructions](#setup-instructions)
    - [Prerequisites](#prerequisites)
    - [Local Development](#local-development)
    - [Environment Variables](#environment-variables)
  - [Development Guidelines](#development-guidelines)
    - [Route Groups and Components](#route-groups-and-components)
    - [Component Structure](#component-structure)
    - [File Naming Conventions](#file-naming-conventions)
  - [Coding Standards](#coding-standards)
    - [TypeScript](#typescript)
    - [React Best Practices](#react-best-practices)
    - [Styling](#styling)
    - [State Management](#state-management)
    - [Code Formatting](#code-formatting)
    - [Editor Configuration](#editor-configuration)
  - [License](#license)

## Overview

Adori AI is a platform that allows users to create videos from various content types:

- Ideas to Video
- Blog to Video
- Text to Video
- PDF to Video
- Audio to Video
- Podcast to Video

The platform features a modern UI with dark/light mode support, authentication, and a dashboard for managing video creation projects.

## Tech Stack

- **Framework**: [Next.js 15](https://nextjs.org/) with App Router
- **Language**: [TypeScript](https://www.typescriptlang.org/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **UI Components**: [shadcn/ui](https://ui.shadcn.com/)
- **Authentication**: [Better Auth](https://www.better-auth.com/)
- **Video Creation**: [Remotion](https://www.remotion.dev/)
- **Package Manager**: [Bun](https://bun.sh/) for local development, npm for Docker/cloud

## Project Structure

The project follows a structured approach with route groups and component organization:

```
src/
├── app/
│   ├── (auth)/              # Authentication routes
│   │   ├── sign-in/
│   │   └── sign-up/
│   ├── (dashboard)/         # Dashboard routes
│   │   ├── _components/     # Shared dashboard components
│   │   ├── create-video/    # Video creation routes
│   │   │   ├── _components/ # Video creation specific components
│   │   │   ├── idea/        # Idea to video route
│   │   │   ├── blog/        # Blog to video route
│   │   │   └── ...          # Other conversion routes
│   │   ├── drafts/          # Drafts management
│   │   ├── my-videos/       # User's videos
│   │   ├── pricing/         # Pricing plans
│   │   │   ├── _components/ # Pricing specific components
│   │   │   └── examples/    # Example pricing scenarios
│   │   └── settings/        # User settings
│   ├── (home)/              # Public home page
│   ├── globals.css          # Global styles
│   └── layout.tsx           # Root layout
├── components/              # Global shared components
│   ├── ui/                  # UI components (shadcn)
│   └── ...                  # Other global components
├── lib/                     # Utility functions and helpers
└── ...                      # Other directories
```

## Setup Instructions

### Prerequisites

- Node.js 18+
- Bun (recommended for local development)
- npm (for Docker/cloud environments)

### Local Development

1. Clone the repository:

   ```bash
   git clone https://github.com/your-username/adori-ai-v2.git
   cd adori-ai-v2
   ```

2. Install dependencies:

   ```bash
   bun install
   ```

3. Set up environment variables:

   Copy the existing `.env.example` file to create your environment file:

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your actual configuration values.

4. Run the development server:

   ```bash
   bun run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

6. Set up Stripe webhook testing with ngrok:

   Install ngrok to create a secure tunnel to your local development server:

   ```bash
   brew install ngrok
   ```

   Start the ngrok tunnel to expose your local server:

   ```bash
   ngrok http 3000
   ```

   Copy the generated HTTPS forwarding URL (e.g. https://abc123.ngrok.io) and use it in your Stripe Dashboard:

   1. Go to Stripe Dashboard > Developers > Webhooks
   2. Click "Add endpoint"
   3. Paste your ngrok URL and append `/api/stripe/webhook`
   4. Select the events you want to test
   5. Save the webhook endpoint

### Environment Variables

The project requires several environment variables to function properly. Refer to the `.env.example` file for the complete list of required variables and their descriptions.

To obtain the necessary API keys:

1. **Better Auth**: Configure authentication settings in your environment
2. **OpenAI API**: Sign up at [platform.openai.com](https://platform.openai.com)
3. **Remotion**: Get your API key from [remotion.dev](https://www.remotion.dev)

## Development Guidelines

### Route Groups and Components

- **Route Groups**: Use route groups with parentheses (e.g., `(dashboard)`, `(auth)`) for organizing related routes
- **Component Organization**:
  - Global components go in `/components`
  - Route-specific components go in `_components` folders within their respective route groups
  - UI components from shadcn go in `/components/ui`

### Component Structure

- **Component Creation**:

  - Create small, focused components (< 50 lines)
  - Follow atomic design principles
  - Use shadcn/ui components when possible
  - Each component should have a single responsibility

- **Component Organization**:
  - Create a separate file for each component
  - Group related components in a directory
  - Use an index.ts file to export components from a directory
  - Use proper TypeScript interfaces for props

### File Naming Conventions

- Use kebab-case for file and directory names (e.g., `conversion-card.tsx`)
- Use PascalCase for component names (e.g., `ConversionCard`)
- Use camelCase for variables, functions, and instances
- Suffix component files with `.tsx` and utility files with `.ts`

## Coding Standards

### TypeScript

- Use TypeScript for all new code
- Define interfaces for component props
- Use proper type exports with `export type`
- Avoid using `any` type
- Use React.FC for functional components with explicit prop types

### React Best Practices

- Use functional components with hooks
- Minimize the use of `useEffect` and prefer server components when possible
- Implement proper error handling with early returns
- Use proper form handling with TypeScript types
- Create reusable components for common UI patterns

### Styling

- Use Tailwind CSS for styling
- Follow a mobile-first approach
- Use the `cn` utility for conditional class names
- Use shadcn/ui components and customize as needed
- Maintain consistent spacing and layout patterns

### State Management

- Use React hooks for local state
- Use React Context for shared state when needed
- Implement proper form state management
- Use controlled components for form inputs

### Code Formatting

We use Prettier for consistent code formatting across the project:

- Single quotes for strings and JSX attributes
- No semicolons
- 2 spaces for indentation
- 80 character line length
- Arrow function parentheses only when needed
- Trailing commas in objects and arrays

Run formatting commands:

```bash
# Format all files
bun run format

# Check formatting without changing files
bun run format:check
```

### Editor Configuration

The project includes configuration files to ensure consistent development experience:

- `.prettierrc` - Prettier configuration
- `.editorconfig` - Editor-agnostic configuration
- `.vscode/settings.json` - VS Code specific settings

For VS Code users, install the following extensions:

- Prettier - Code formatter
- ESLint
- Tailwind CSS IntelliSense

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
